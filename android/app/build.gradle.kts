import java.util.Properties

plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

// 讀取 key.properties
val keystorePropertiesFile = rootProject.file("key.properties")
val keystoreProperties = Properties().apply {
    load(keystorePropertiesFile.inputStream())
}

android {
    namespace = "com.example.sbar_pos"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    defaultConfig {
        applicationId = "com.example.sbar_pos"
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = "11"
    }

    // 簽名配置
    signingConfigs {
        create("kidTest") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
        }
        create("cusProd") {
            keyAlias = keystoreProperties["cusProdKeyAlias"] as String
            keyPassword = keystoreProperties["cusProdKeyPassword"] as String
            storeFile = file(keystoreProperties["cusProdStoreFile"] as String)
            storePassword = keystoreProperties["cusProdStorePassword"] as String
        }
    }

    // 編譯類型
    buildTypes {
        getByName("release") {
            signingConfig = signingConfigs.getByName("kidTest") // 可改成 cusProd
            isMinifyEnabled = false          // 關閉 R8/Proguard
            isShrinkResources = false        // 關閉資源縮減，避免錯誤
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
        getByName("debug") {
            isMinifyEnabled = false
            isShrinkResources = false
        }
    }

    // Flavors 設定
    flavorDimensions += "flavors"
    productFlavors {
        create("kidTest") {
            applicationId = "com.kidTest_sbar"
            dimension = "flavors"
            signingConfig = signingConfigs.getByName("kidTest")
        }
        create("kidProd") {
            applicationId = "com.kidTest_sbar"
            dimension = "flavors"
            signingConfig = signingConfigs.getByName("kidTest")
        }
        create("cusTest") {
            applicationId = "com.sbar.official"
            dimension = "flavors"
            signingConfig = signingConfigs.getByName("cusProd")
        }
        create("cusProd") {
            applicationId = "com.sbar.official"
            dimension = "flavors"
            signingConfig = signingConfigs.getByName("cusProd")
        }
    }
}

flutter {
    source = "../.."
}
