allprojects {
    repositories {
        google()
        mavenCentral()
    }

    // 如果需要，可以把 common 的設定放這
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)

    afterEvaluate {
        if (project.hasProperty("android")) {
            val androidExt = project.extensions.findByName("android")
            if (androidExt != null) {
                when (androidExt) {
                    is com.android.build.gradle.LibraryExtension -> {
                        if (androidExt.namespace.isNullOrBlank()) {
                            androidExt.namespace = "com.example.${project.name}"
                        }
                        // 這裡設定 NDK 版本
                        androidExt.ndkVersion = "27.0.12077973"
                        // 設定 Java 版本
                        androidExt.compileOptions {
                            sourceCompatibility = JavaVersion.VERSION_1_8
                            targetCompatibility = JavaVersion.VERSION_1_8
                        }
                    }
                    is com.android.build.gradle.AppExtension -> {
                        if (androidExt.namespace.isNullOrBlank()) {
                            androidExt.namespace = "com.example.${project.name}"
                        }
                        // 這裡設定 NDK 版本
                        androidExt.ndkVersion = "27.0.12077973"
                        // 設定 Java 版本
                        androidExt.compileOptions {
                            sourceCompatibility = JavaVersion.VERSION_1_8
                            targetCompatibility = JavaVersion.VERSION_1_8
                        }
                    }
                }
            }

            // 針對 Kotlin 編譯器設定 JVM target
            project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile::class.java).configureEach {
                kotlinOptions {
                    jvmTarget = "1.8"
                }
            }
        }
    }
}

subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
