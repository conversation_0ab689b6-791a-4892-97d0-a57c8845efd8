# 宣告

@host = sbar-posweb-api.pascation.com.tw
@baseURL = https://{{host}}
@token = {{login.response.body.data.access_token}}

###############################################################################
### 登入(Login_API)
###############################################################################
# @name login

POST {{baseURL}}/api/login HTTP/1.1
Content-Type: application/x-www-form-urlencoded

email=<EMAIL>
&password=password

###

POST {{baseURL}}/api/login HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"  
}

###############################################################################
### 商品供應商(Products_Suppliers_API)
###############################################################################

GET {{baseURL}}/api/products_suppliers HTTP/1.1
Authorization: Bearer {{token}}
