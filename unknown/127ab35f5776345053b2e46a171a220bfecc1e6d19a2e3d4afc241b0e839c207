import 'dart:convert';

class ProductsSuppliersRes {
  bool? success;
  String? message;
  List<Datum>? data;

  ProductsSuppliersRes({this.success, this.message, this.data});

  ProductsSuppliersRes copyWith({
    bool? success,
    String? message,
    List<Datum>? data,
  }) => ProductsSuppliersRes(
    success: success ?? this.success,
    message: message ?? this.message,
    data: data ?? this.data,
  );

  factory ProductsSuppliersRes.fromRawJson(String str) =>
      ProductsSuppliersRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory ProductsSuppliersRes.fromJson(Map<String, dynamic> json) =>
      ProductsSuppliersRes(
        success: json["success"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
    "success": success,
    "message": message,
    "data": data == null
        ? []
        : List<dynamic>.from(data!.map((x) => x.toJson())),
  };
}

class Datum {
  int? id;
  String? name;

  Datum({this.id, this.name});

  Datum copyWith({int? id, String? name}) =>
      Datum(id: id ?? this.id, name: name ?? this.name);

  factory Datum.fromRawJson(String str) => Datum.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Datum.fromJson(Map<String, dynamic> json) =>
      Datum(id: json["id"], name: json["name"]);

  Map<String, dynamic> toJson() => {"id": id, "name": name};
}
