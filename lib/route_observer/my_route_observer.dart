import 'package:firebase_analytics/observer.dart';
import 'package:flutter/material.dart';
import 'package:sbar_pos/route_observer/page_info.dart';
import '../logger/my_print.dart';

class MyRouteObserver extends RouteObserver<PageRoute<dynamic>> {
  // final ScreenNameExtractor nameExtractor;
  RouteFilter get logRouteFilter => (route) {
    if(route == null) return false;
    // if(route.settings.name == PageName.programMain.toString()) return false;
    // if(route.settings.name == PageName.newsInfo.toString()) return false;
    return true;
  };


  void _sendScreenView(PageRoute<dynamic> route) {
    var screenName = route.settings.name;
    PageInfo().pageName.add(route.settings.name ?? "");
    myPrint('screenName: $screenName');

    // do something with it, ie. send it to your analytics service collector
    if(screenName != null && logRouteFilter.call(route)) {
      // AnalyticsHelper.logOtherUserExplorer(screenName);
    }
  }



  @override
  void didPush(Route route, Route? previousRoute) {
    super.didPush(route, previousRoute);
    if (route is PageRoute) {
      _sendScreenView(route);
    }
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    //super.didReplace(newRoute, oldRoute);
    super.didReplace();
    if (newRoute is PageRoute) {
      _sendScreenView(newRoute);
    }
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);
    if (previousRoute is PageRoute && route is PageRoute) {
      _sendScreenView(previousRoute);
    }
  }
}
