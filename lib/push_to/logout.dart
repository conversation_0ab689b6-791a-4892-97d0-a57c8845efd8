import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import '../../screen/login/login_screen.dart';
import '../hive/hive_provider/hive_user_provider.dart';
import '../provider/main_provider.dart';
import '../screen/start/my_app.dart';
import '../sqlite/provider/product_shopping_car_record_provider.dart';
import '../util/MyRoutes.dart';

void logout(BuildContext context) async {
  showLoadingOverlay();
  await _logoutClearData(context);
  await _logoutClearUserDb();
  await _initShoppingCar();
  await Future.delayed(300.ms);
  dismissLoadingOverlay();

  pushAndRemoveUntil(
    PageName.login.toString(),
    builder: (BuildContext context) => LoginScreen(),
  );
}

///清空資料
Future<void> _logoutClearData(BuildContext context) async {
  final shoppingCarVm = context.read<ShoppingCarProvider>();
  shoppingCarVm.clearShoppingCar(); //清空購物車
  shoppingCarVm.isSelectWallet = false; //清空錢包
  shoppingCarVm.clearMember(); //清空會員資料
  shoppingCarVm.clearSelectedPerformance(); //清空銷售人員資料

  shoppingCarVm.productModel.clear();  //清空主商品列表
  shoppingCarVm.classModel.clear(); //清空主課程列表
  // shoppingCarVm.cashModel.clear(); //清空主儲值金列表
  shoppingCarVm.productSelectedTypeIndex = 0; //將主商品類別恢復初始
  shoppingCarVm.classSelectedTypeIndex = 0; //將主課程類別恢復初始
  context.read<MainProvider>().setInitialIndex(2);
}

///清空user DB
Future<void> _logoutClearUserDb() async {
  final user = await HiveUserProvider.getUser();
  user.memberToken = '';
  user.stuffPermissions = '';
  user.name = '';
  user.email = '';
  user.memberId = '';
  await HiveUserProvider.updateUser(user);
}

///init shoppingCar
Future<void> _initShoppingCar() async {

  // 清空購物車
  final shoppingCarProvider = ProductShoppingCarRecordProvider();
  await shoppingCarProvider.createTableIfNotExists();
  await shoppingCarProvider.clearAll();
}