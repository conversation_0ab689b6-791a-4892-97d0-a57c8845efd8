// ignore_for_file: non_constant_identifier_names

import 'package:flutter/cupertino.dart';

class R {

  static const InnerAsset image = InnerAsset._();



}


class InnerAsset {

  const InnerAsset._();

  static const String basePath = 'assets/images';
  AssetImage image_login_bg() => const AssetImage('$basePath/image_login_bg.png');
  AssetImage icon_arrow_down_with_circle() => const AssetImage('$basePath/icon_arrow_down_with_circle.png');
  AssetImage icon_sbar_logo() => const AssetImage('$basePath/icon_sbar_logo.png');
  AssetImage icon_default_avatar() => const AssetImage('$basePath/icon_default_avatar.png');
  AssetImage icon_calendar() => const AssetImage('$basePath/icon_calendar.png');
  AssetImage icon_member_class() => const AssetImage('$basePath/icon_member_class.png');
  AssetImage icon_member_history_record() => const AssetImage('$basePath/icon_member_history_record.png');
  AssetImage icon_member_info() => const AssetImage('$basePath/icon_member_info.png');
  AssetImage icon_member_reserve() => const AssetImage('$basePath/icon_member_reserve.png');
  AssetImage icon_member_top_up_record() => const AssetImage('$basePath/icon_member_top_up_record.png');
  AssetImage icon_member_top_up() => const AssetImage('$basePath/icon_member_top_up.png');
  AssetImage icon_shopping_car() => const AssetImage('$basePath/icon_shopping_car.png');
  AssetImage icon_drawer_performance_distribution() => const AssetImage('$basePath/icon_drawer_performance_distribution.png');
  AssetImage icon_drawer_pos_payment() => const AssetImage('$basePath/icon_drawer_pos_payment.png');
  AssetImage icon_drawer_reserve() => const AssetImage('$basePath/icon_drawer_reserve.png');
  AssetImage icon_drawer_stock_management() => const AssetImage('$basePath/icon_drawer_stock_management.png');
  AssetImage icon_open_money_box() => const AssetImage('$basePath/icon_open_money_box.png');
  AssetImage icon_class_confirm() => const AssetImage('$basePath/icon_class_confirm.png');
  AssetImage icon_edit() => const AssetImage('$basePath/icon_edit.png');



  //svg
  String icon_search() => '$basePath/icon_search.svg';
  String icon_top_up() => '$basePath/icon_top_up.svg';
  String icon_history_order() => '$basePath/icon_history_order.svg';
  String icon_brand_member() => '$basePath/icon_brand_member.svg';
  String icon_reserve_class() => '$basePath/icon_reserve_class.svg';
  String icon_class() => '$basePath/icon_class.svg';
  String icon_product() => '$basePath/icon_product.svg';
  String icon_scan() => '$basePath/icon_scan.svg';
  String icon_reserve_calendar() => '$basePath/icon_reserve_calendar.svg';

}
