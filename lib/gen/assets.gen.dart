/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/icon_arrow_down_with_circle.png
  AssetGenImage get iconArrowDownWithCircle =>
      const AssetGenImage('assets/images/icon_arrow_down_with_circle.png');

  /// File path: assets/images/icon_brand_member.svg
  SvgGenImage get iconBrandMember =>
      const SvgGenImage('assets/images/icon_brand_member.svg');

  /// File path: assets/images/icon_calendar.png
  AssetGenImage get iconCalendar =>
      const AssetGenImage('assets/images/icon_calendar.png');

  /// File path: assets/images/icon_class.svg
  SvgGenImage get iconClass =>
      const SvgGenImage('assets/images/icon_class.svg');

  /// File path: assets/images/icon_class_confirm.png
  AssetGenImage get iconClassConfirm =>
      const AssetGenImage('assets/images/icon_class_confirm.png');

  /// File path: assets/images/icon_default_avatar.png
  AssetGenImage get iconDefaultAvatar =>
      const AssetGenImage('assets/images/icon_default_avatar.png');

  /// File path: assets/images/icon_drawer_performance_distribution.png
  AssetGenImage get iconDrawerPerformanceDistribution => const AssetGenImage(
      'assets/images/icon_drawer_performance_distribution.png');

  /// File path: assets/images/icon_drawer_pos_payment.png
  AssetGenImage get iconDrawerPosPayment =>
      const AssetGenImage('assets/images/icon_drawer_pos_payment.png');

  /// File path: assets/images/icon_drawer_reserve.png
  AssetGenImage get iconDrawerReserve =>
      const AssetGenImage('assets/images/icon_drawer_reserve.png');

  /// File path: assets/images/icon_drawer_stock_management.png
  AssetGenImage get iconDrawerStockManagement =>
      const AssetGenImage('assets/images/icon_drawer_stock_management.png');

  /// File path: assets/images/icon_edit.png
  AssetGenImage get iconEdit =>
      const AssetGenImage('assets/images/icon_edit.png');

  /// File path: assets/images/icon_history_order.svg
  SvgGenImage get iconHistoryOrder =>
      const SvgGenImage('assets/images/icon_history_order.svg');

  /// File path: assets/images/icon_member_class.png
  AssetGenImage get iconMemberClass =>
      const AssetGenImage('assets/images/icon_member_class.png');

  /// File path: assets/images/icon_member_history_record.png
  AssetGenImage get iconMemberHistoryRecord =>
      const AssetGenImage('assets/images/icon_member_history_record.png');

  /// File path: assets/images/icon_member_info.png
  AssetGenImage get iconMemberInfo =>
      const AssetGenImage('assets/images/icon_member_info.png');

  /// File path: assets/images/icon_member_reserve.png
  AssetGenImage get iconMemberReserve =>
      const AssetGenImage('assets/images/icon_member_reserve.png');

  /// File path: assets/images/icon_member_top_up.png
  AssetGenImage get iconMemberTopUp =>
      const AssetGenImage('assets/images/icon_member_top_up.png');

  /// File path: assets/images/icon_member_top_up_record.png
  AssetGenImage get iconMemberTopUpRecord =>
      const AssetGenImage('assets/images/icon_member_top_up_record.png');

  /// File path: assets/images/icon_open_money_box.png
  AssetGenImage get iconOpenMoneyBox =>
      const AssetGenImage('assets/images/icon_open_money_box.png');

  /// File path: assets/images/icon_product.svg
  SvgGenImage get iconProduct =>
      const SvgGenImage('assets/images/icon_product.svg');

  /// File path: assets/images/icon_reserve_calendar.svg
  SvgGenImage get iconReserveCalendar =>
      const SvgGenImage('assets/images/icon_reserve_calendar.svg');

  /// File path: assets/images/icon_reserve_class.svg
  SvgGenImage get iconReserveClass =>
      const SvgGenImage('assets/images/icon_reserve_class.svg');

  /// File path: assets/images/icon_sbar_logo.png
  AssetGenImage get iconSbarLogo =>
      const AssetGenImage('assets/images/icon_sbar_logo.png');

  /// File path: assets/images/icon_scan.svg
  SvgGenImage get iconScan => const SvgGenImage('assets/images/icon_scan.svg');

  /// File path: assets/images/icon_search.svg
  SvgGenImage get iconSearch =>
      const SvgGenImage('assets/images/icon_search.svg');

  /// File path: assets/images/icon_shopping_car.png
  AssetGenImage get iconShoppingCar =>
      const AssetGenImage('assets/images/icon_shopping_car.png');

  /// File path: assets/images/icon_top_up.svg
  SvgGenImage get iconTopUp =>
      const SvgGenImage('assets/images/icon_top_up.svg');

  /// File path: assets/images/image_login_bg.png
  AssetGenImage get imageLoginBg =>
      const AssetGenImage('assets/images/image_login_bg.png');

  /// List of all assets
  List<dynamic> get values => [
        iconArrowDownWithCircle,
        iconBrandMember,
        iconCalendar,
        iconClass,
        iconClassConfirm,
        iconDefaultAvatar,
        iconDrawerPerformanceDistribution,
        iconDrawerPosPayment,
        iconDrawerReserve,
        iconDrawerStockManagement,
        iconEdit,
        iconHistoryOrder,
        iconMemberClass,
        iconMemberHistoryRecord,
        iconMemberInfo,
        iconMemberReserve,
        iconMemberTopUp,
        iconMemberTopUpRecord,
        iconOpenMoneyBox,
        iconProduct,
        iconReserveCalendar,
        iconReserveClass,
        iconSbarLogo,
        iconScan,
        iconSearch,
        iconShoppingCar,
        iconTopUp,
        imageLoginBg
      ];
}

class Assets {
  const Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
