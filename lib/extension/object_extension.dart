part of 'my_extension.dart';

extension ObjectExtension on Object? {

  /// try toString on nullable object
  ///
  /// if object is null return ''
  String toStringOrEmpty() {
    if (this == null) return '';
    return this.toString();
  }

  /// try to cast type T.
  ///
  /// You can use [castToOr] like this:
  /// ```dart
  /// Object? obj1;
  /// obj1.castToOr<bool>(() => false) // let obj1 cast to bool, if null then return or() supplier
  /// // expect return false
  /// ```
  ///
  /// ```dart
  /// Object? obj2 = true;
  /// obj2.castToOr<bool>(() => false)
  /// // expect return true;
  /// ```
  T castToOr<T>(T Function() or) {
    if (this == null) return or();
    return (this as T) ?? or();
  }
}
