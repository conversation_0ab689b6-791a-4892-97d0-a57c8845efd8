import 'package:flutter/material.dart';

extension GlobalPaintBounds on BuildContext {
  Rect? get globalPaintBounds {
    final renderBox = findRenderObject();
    if (renderBox is! RenderBox) return null;

    final Offset offset = renderBox.localToGlobal(Offset.zero);
    return Rect.fromPoints(
      offset,
      Offset(
          offset.dx + renderBox.size.width, offset.dy + renderBox.size.height),
    );
  }
}