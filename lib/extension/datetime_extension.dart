part of 'my_extension.dart';

extension DateTimeExtension on DateTime {
  String toDateString(format) {
    return Intl.DateFormat(format).format(this);
  }

  String toDateYMDString() {
    return Intl.DateFormat('yyyy-MM-dd').format(this);
  }

  String toDateYMDHHmmString() {
    return Intl.DateFormat('yyyy/MM/dd HH:mm').format(this);
  }

  String toDateYMDCustomSplitString({String? split}) {
    myPrint(this);
    return Intl.DateFormat('yyyy-MM-dd')
        .format(this)
        .replaceAll('-', split ?? '.');
  }

  bool isInBetween(DateTime start, DateTime end) {
    end = DateTime(end.year, end.month, end.day, 23, 59, 59, 999);
    //myPrint("@@@start: $start, end: $end");
    return isAfter(start) && isBefore(end);
  }

  String toYMDChinese(locale) {
    if ("zh-TW" == locale) {
      return "$year年${month.toString().padLeft(2, '0')}月${day.toString().padLeft(2, '0')}日(${StringUtils.toCHWeekDay(this).replaceAll('星期', '')}) ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}";
    } else {
      return "$year/$month/$day(${StringUtils.toEnWeekDay(this)}) ${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}";
    }
  }

  DateTime copyWith({
    int? year,
    int? month,
    int? day,
    int? hour,
    int? minute,
    int? second,
    int? millisecond,
    int? microsecond,
  }) {
    return DateTime(
      year ?? this.year,
      month ?? this.month,
      day ?? this.day,
      hour ?? this.hour,
      minute ?? this.minute,
      second ?? this.second,
      millisecond ?? this.millisecond,
      microsecond ?? this.microsecond,
    );
  }

  String toFormatString(String format) {
    DateFormat.DateFormat dateFormat = DateFormat.DateFormat(format);
    return dateFormat.format(this);
  }

  bool isSameDate(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }

  DateTime clamp({DateTime? min, DateTime? max}) {
    assert(
      ((min != null) && (max != null))
          ? (min.isBefore(max) || (min == max))
          : true,
      'DateTime min has to be before or equal to max\n(min: $min - max: $max)',
    );
    if ((min != null) && compareTo(min).isNegative) {
      return min;
    } else if ((max != null) && max.compareTo(this).isNegative) {
      return max;
    }
    return this;
  }
}

///提取當天日期(口月口日)
String getTodayMD() {
  DateTime today = DateTime.now();
  int month = today.month;
  int day = today.day;
  return '$month月$day日';
}

/// 提取當天日期 (英文 "January 6")
String getTodayMDInEnglish() {
  DateTime today = DateTime.now();
  return Intl.DateFormat('MMMM d').format(today); // "MMMM" 代表月份全名，"d" 代表日期
}

///提取當日西元年
String getTodayY() {
  DateTime today = DateTime.now();
  int years = today.year;
  return '$years';
}

///減去_天
DateTime reduceDay(int reduce) {
  return DateTime.now().subtract(Duration(days: 365 * reduce));
}
