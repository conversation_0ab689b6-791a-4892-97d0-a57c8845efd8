part of 'my_extension.dart';


extension StringExtension on String {
  double textHeight(TextStyle style, double textWidth, int maxLine) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(
        text: this,
        style: style,
      ),
      strutStyle: StrutStyle(
        forceStrutHeight: true,
        leading: 0.5,
      ),
      textDirection: TextDirection.ltr,
      maxLines: maxLine,
    )..layout(minWidth: 0, maxWidth: double.infinity);

    final countLines = (textPainter.size.width / textWidth).ceil();
    final height = countLines * textPainter.size.height;
    return height;
  }

  bool isNullOrEmpty() {
    if (this == null) {
      return true;
    } else if (this.isEmpty) {
      return true;
    } else {
      return false;
    }
  }

  DateTime toDateTime(format) {
    try {
      Intl.DateFormat dateFormat = Intl.DateFormat(format);
      return dateFormat.parse(this);
    } catch (e) {
      return DateTime.now();
    }
  }

  DateTime toDateTimeYMD() {
    try {
      // if (this.contains('年') && this.contains('月') && this.contains('日')) {
      //   this.replaceAll('年', '/').replaceAll('月', '/').replaceAll('日', '/');
      //   print('--------------------');
      //   print(this);
      //   print('--------------------');
      // }
      var format = '';
      if (this.contains('-')) format = 'yyyy-MM-dd';
      if (this.contains('/')) format = 'yyyy/MM/dd';
      if (this.contains('.')) format = 'yyyy.MM.dd';

      Intl.DateFormat dateFormat = Intl.DateFormat(format);
      return dateFormat.parse(this);
    } catch (e) {
      return DateTime.now();
    }
  }

  DateTime toDateTimeYMDhm() {
    try {
      // if (this.contains('年') && this.contains('月') && this.contains('日')) {
      //   this.replaceAll('年', '/').replaceAll('月', '/').replaceAll('日', '/');
      //   print('--------------------');
      //   print(this);
      //   print('--------------------');
      // }
      var format = '';
      if (this.contains('-')) format = 'yyyy-MM-dd H:mm';
      if (this.contains('/')) format = 'yyyy/MM/dd H:mm';
      if (this.contains('.')) format = 'yyyy.MM.dd H:mm';



      Intl.DateFormat dateFormat = Intl.DateFormat(format);
      return dateFormat.parse(this);
    } catch (e) {
      return DateTime.now();
    }
  }
}
