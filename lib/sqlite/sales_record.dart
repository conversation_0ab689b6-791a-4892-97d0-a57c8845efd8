import 'dart:convert';

//暫時沒用到
class SalesRecord{
  final String staffName;
  final String staffId;

  SalesRecord({
    required this.staffName,
    required this.staffId,

  });



  Map<String, dynamic> toMap() {
    return {
      'staffName': staffName,
      'staffId': staffId,
    };
  }

  factory SalesRecord.fromMap(Map<String, dynamic> json) {
    return SalesRecord(
      staffName: json['staffName'],
      staffId: json['staffId'],

    );
  }

  static SalesRecord fromJson(String str) =>
      SalesRecord.fromMap(json.decode(str));

  static String toJson(SalesRecord data) => json.encode(data.toMap());

  @override
  String toString() {
    return 'SalesRecord(staffName: $staffName, staffId: $staffId)';
  }

}