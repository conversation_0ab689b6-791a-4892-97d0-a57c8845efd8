import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

import '../App.dart';

class DatabaseHelper {
  Future<void> createTable() async {
    App.database = await openDatabase(
      join(await getDatabasesPath(), App.dbName),
      version: 2,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
      onDowngrade: onDatabaseDowngradeDelete,
    );
  }

  // 初次建立資料庫，會一次建立所有表
  Future<void> _onCreate(Database db, int version) async {
    var batch = db.batch();
    _createTableUserV1(batch);
    _createTableSalesRecordV1(batch);
    await batch.commit();
    print("@@@SQLite : Tables created on version $version");
  }

  void _onUpgrade(Database db, int oldVersion, int newVersion) async {
    var batch = db.batch();
    if (oldVersion == 1) {
      _createTableProductShoppingCarRecordRecordV1ToV2(batch);
    }



    await batch.commit();
    print(
        "@@@SQLite : Table is upgraded, oldVersion: $oldVersion, newVersion: $newVersion");
  }

  ///建立初始版本
  void _createTableUserV1(Batch batch) {
    batch.execute(
      "CREATE TABLE user(id INTEGER PRIMARY KEY, account TEXT,pwd TEXT, isFbLogin BIT, isRememberMe BIT ,token TEXT, photo TEXT)",
    );
  }

  ///加入主銷人員（初始版本）
  //暫時沒用到
  void _createTableSalesRecordV1(Batch batch) {
    batch.execute('''
    CREATE TABLE salesRecord (
      staffId TEXT PRIMARY KEY,
        staffName TEXT NOT NULL
    )
    ''');
  }

  ///加入購物車
  void _createTableProductShoppingCarRecordRecordV1ToV2(Batch batch) {
    batch.execute('''
     CREATE TABLE IF NOT EXISTS productShoppingCarRecord (
        productId TEXT NOT NULL,
        type INTEGER NOT NULL,
        selectAmount INTEGER NOT NULL DEFAULT 0,
        PRIMARY KEY(productId, type)
      )
    ''');
  }
}
