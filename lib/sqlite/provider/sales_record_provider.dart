//
import 'package:sqflite/sqflite.dart';
import '../../App.dart';
import '../sales_record.dart';

class SalesRecordProvider {
  final String tableName = 'salesRecord';

  /// 建立資料表（如未存在）
  Future<void> createTableIfNotExists() async {
    final db = await App.database;

    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableName (
        staffId TEXT PRIMARY KEY,
        staffName TEXT NOT NULL
      )
    ''');
  }

  /// 新增或更新一筆資料
  Future<void> insertOrUpdate(SalesRecord record) async {
    final db = await App.database;

    final data = record.toMap();

    // 依 staffId 更新，若不存在就插入
    final count = await db.update(
      tableName,
      data,
      where: 'staffId = ?',
      whereArgs: [record.staffId],
    );

    if (count == 0) {
      await db.insert(
        tableName,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
  }

  /// 根據 staffId 取得單筆資料
  Future<SalesRecord?> getByStaffId(String staffId) async {
    final db = await App.database;

    final res = await db.query(
      tableName,
      where: 'staffId = ?',
      whereArgs: [staffId],
      limit: 1,
    );

    if (res.isNotEmpty) {
      return SalesRecord.fromMap(res.first);
    }
    return null;
  }

  /// 取得全部資料
  Future<List<SalesRecord>> getAll() async {
    final db = await App.database;
    final List<Map<String, dynamic>> res = await db.query(tableName);

    return res
        .map<SalesRecord>(
            (map) => SalesRecord.fromMap(map))
        .toList();
  }

  /// 刪除資料（依 staffId）
  Future<void> deleteByOrderId(String staffId) async {
    final db = await App.database;
    await db.delete(
      tableName,
      where: 'staffId = ?',
      whereArgs: [staffId],
    );
  }

  /// 刪除全部資料
  Future<void> deleteAll() async {
    final db = await App.database;
    await db.delete(tableName);
  }

  /// 顯示全部資料（for debug）
  Future<void> showAllData() async {
    final all = await getAll();
    for (var record in all) {
      print(record);
    }
  }
}
