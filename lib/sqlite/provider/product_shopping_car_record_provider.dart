import '../../App.dart';

class ProductShoppingCarRecordProvider {
  final String tableName = 'shopping_car';

  /// 建立資料表（如未存在）
  Future<void> createTableIfNotExists() async {
    final db = await App.database;

    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableName (
        productId TEXT NOT NULL,
        type INTEGER NOT NULL,
        selectAmount INTEGER NOT NULL DEFAULT 0,
        PRIMARY KEY(productId, type)
      )
    ''');
  }

  Future<void> insertOrUpdate(String productId, int type, {int amount = 1}) async {
    final db = await App.database;

    final existing = await db.query(
      tableName,
      where: 'productId = ? AND type = ?',
      whereArgs: [productId, type],
      limit: 1,
    );

    if (existing.isEmpty) {
      await db.insert(tableName, {
        'productId': productId,
        'type': type,
        'selectAmount': amount,
      });
    } else {
      final current = existing.first['selectAmount'] as int;
      await db.update(
        tableName,
        {'selectAmount': current + amount},
        where: 'productId = ? AND type = ?',
        whereArgs: [productId, type],
      );
    }
  }

  Future<int> getAmount(String productId, int type) async {
    final db = await App.database;

    final res = await db.query(
      tableName,
      where: 'productId = ? AND type = ?',
      whereArgs: [productId, type],
      limit: 1,
    );

    if (res.isEmpty) return 0;
    return res.first['selectAmount'] as int;
  }

  Future<void> decrementAmount(String productId, int type) async {
    final db = await App.database;

    final res = await db.query(
      tableName,
      where: 'productId = ? AND type = ?',
      whereArgs: [productId, type],
      limit: 1,
    );

    if (res.isNotEmpty) {
      final current = res.first['selectAmount'] as int;
      final newAmount = current - 1;

      // 不再刪除紀錄，直接更新為 0
      await db.update(
        tableName,
        {'selectAmount': newAmount < 0 ? 0 : newAmount},
        where: 'productId = ? AND type = ?',
        whereArgs: [productId, type],
      );
    }
  }


  /// 取得單筆資料
  Future<Map<String, dynamic>?> getByProductId(String productId, int type) async {
    final db = await App.database;

    final res = await db.query(
      tableName,
      where: 'productId = ? AND type = ?',
      whereArgs: [productId, type],
      limit: 1,
    );

    if (res.isEmpty) return null;
    return res.first;
  }

  /// 取得全部資料
  Future<List<Map<String, dynamic>>> getAll() async {
    final db = await App.database;
    final res = await db.query(tableName);
    return res;
  }

  ///刪除
  Future<void> deleteByProductId(String productId) async {
    final db = await App.database;
    await db.delete(
      tableName,
      where: 'productId = ?',
      whereArgs: [productId],
    );
  }

  /// 清空整個購物車
  Future<void> clearAll() async {
    final db = await App.database;
    await db.delete(tableName);
  }

}
