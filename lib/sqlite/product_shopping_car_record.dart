import 'dart:convert';

class ProductShoppingCarRecord{
  final String productId;
  final int type;
  final int selectAmount;

  ProductShoppingCarRecord({
    required this.productId,
    required this.type,
    required this.selectAmount,

  });



  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'type': type,
      'selectAmount': selectAmount,
    };
  }

  factory ProductShoppingCarRecord.fromMap(Map<String, dynamic> json) {
    return ProductShoppingCarRecord(
      productId: json['productId'],
      type: json['type'],
      selectAmount: json['selectAmount'],

    );
  }

  static ProductShoppingCarRecord fromJson(String str) =>
      ProductShoppingCarRecord.fromMap(json.decode(str));

  static String toJson(ProductShoppingCarRecord data) => json.encode(data.toMap());

  @override
  String toString() {
    return 'ProductShoppingCarRecord(productId: $productId, type: $type, selectAmount: $selectAmount)';
  }

}