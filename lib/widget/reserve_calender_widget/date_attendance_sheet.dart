import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/state_text_type_widget/cours_date_top.dart';

import '../../../../model/reserve_calendar_model/course_order_details_data.dart';
import 'time_table_widget.dart';

class DataAttendanceSheet extends StatefulWidget {
  const DataAttendanceSheet({super.key});

  @override
  State<DataAttendanceSheet> createState() => _DataAttendanceSheetState();
}

class _DataAttendanceSheetState extends State<DataAttendanceSheet> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final vm = Provider.of<ReserveCalendarScreenProvider>(
        context,
        listen: false,
      );

      //初始化事件資料到 VM
      vm.initializeTestEventsForDay();
    });
  }



  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        return TimeTableWidget(
          cellHeight: 100.h,
          //cellWidth: 200,
          onEventTap: (events, person) {
            vm.setCalendarEventDataSize(Calendar.day, vm.dayDataAPI.length);
            vm.setStateWidgetView(CourseDateTop()); //顯示狀態欄

            vm.setCalendarCourseOrderData(
              Calendar.day,
              CourseOrderDetailsData(
                memberName: events.clientName,
                phoneNumber: events.phoneNumber,
                beauticianName: person,
                date: vm.nowDay(),
                courseName: events.title,
                price: events.price?.toDouble(),
                depositState: vm.setDeposit(events.depoist),
                // startTime: vm.timeOfFormat(events.startTime),
                // endTime: vm.timeOfFormat(events.endTime)
                startTime: events.startTime,
                endTime: events.endTime,
              ),
            );
          },
        );
      },
    );
  }
}
