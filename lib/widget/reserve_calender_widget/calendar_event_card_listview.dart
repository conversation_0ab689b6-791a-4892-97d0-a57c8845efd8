import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sbar_pos/model/reserve_calendar_model/event_card_model.dart';

import 'card_widget/calender_event_card_widget.dart';

class CalendarEventCardListView extends StatefulWidget {
  final Map<DateTime, List<CalenderEventCardWidget>> events;
  final Function(EventCardModel)? onCardTap;
  final EdgeInsetsGeometry? padding;
  final DateTime? date;

  const CalendarEventCardListView({super.key,
    required this.events,
    this.onCardTap,
    this.padding,
    this.date
  });

  @override
  State<StatefulWidget> createState() => _CalendarEventCardListViewState();
}

class _CalendarEventCardListViewState extends State<CalendarEventCardListView> {

  @override
  Widget build(BuildContext context) {
    // 取得當天的日期，如果沒有指定日期則使用今天
    DateTime targetDate = widget.date ?? DateTime.now();

    DateTime normalizedDate = DateTime(targetDate.year, targetDate.month, targetDate.day);

    List<CalenderEventCardWidget> todayEvents = [];

    // 遍歷所有日期，找到匹配的日期
    widget.events.forEach((eventDate, eventList) {
      DateTime normalizedEventDate = DateTime(eventDate.year, eventDate.month, eventDate.day);
      if (normalizedEventDate.isAtSameMomentAs(normalizedDate)) {
        todayEvents = eventList;
      }
    });

    // 如果當天沒有事件，顯示空狀態
    if (todayEvents.isEmpty) {
      return Container(
        padding: widget.padding ?? EdgeInsets.all(16),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.event_busy,
                size: 64,
                color: Colors.grey[400],
              ),
              SizedBox(height: 16),
              Text(
                '當天沒有預約事件',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 顯示當天的事件列表
    return Container(
      padding: widget.padding,
      child: ListView.builder(
        itemCount: todayEvents.length,
        itemBuilder: (context, index) {
          final eventWidget = todayEvents[index];

          // 如果有設定 onCardTap 回調，需要包裝一下
          if (widget.onCardTap != null) {
            return CalenderEventCardWidget(
              event: eventWidget.event,
              onTap: () => widget.onCardTap!(eventWidget.event),
            );
          }

          return eventWidget;
        },
      ),
    );
  }
}

