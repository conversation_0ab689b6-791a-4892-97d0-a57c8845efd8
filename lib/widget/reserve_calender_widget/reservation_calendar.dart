import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import 'package:sbar_pos/model/reserve_calendar_model/event_card_model.dart';
import '../../../../model/reserve_calendar_model/course_order_details_data.dart';
import '../../screen/reserve_calendar/provider/custompaint_calendar.dart';
import 'custom_paint_calendar.dart';


class ReservationCalendar extends StatefulWidget {
  const ReservationCalendar({super.key});

  @override
  State<ReservationCalendar> createState() => _ReservationCalendarState();
}

class _ReservationCalendarState extends State<ReservationCalendar> {

  @override
  void initState() {
    super.initState();

    // 使用 postFrameCallback 確保 context 可用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final vm = Provider.of<ReserveCalendarScreenProvider>(context, listen: false);

      //初始化事件資料到 VM
      vm.initializeTestEventsForMonth();


      //預設選擇今天
      vm.setCalendarEventDataList(DateTime.now());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
          child: MultiProvider(
            providers: [
              ChangeNotifierProvider(create: (_) => CalendarController()),
            ],
            child: CustomPaintCalendarForMonth(
              gridLineColor: Colors.grey.shade300,
              gridLineWidth: 1.0,
              cellHeight: 12.0,
              showGridLines: true,
              onDateSelected: (date) {
                // 選擇日期時直接呼叫 VM 更新右側事件
                vm.updateCurrentDateForMonth(date);

                vm.setCalendarCourseOrderData(
                  Calendar.month,
                  CourseOrderDetailsData(
                    date: vm.selectDay(date),
                    week: vm.weekOfFormat(date.weekday),
                  ),
                );

                vm.setCheckEvent(false);
              },
            ),
          ),
        );
      },
    );
  }
}
