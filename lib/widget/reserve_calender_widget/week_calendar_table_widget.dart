import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/state_text_type_widget/cours_date_top.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/widgets/week_calendar_table.dart';

import '../../../../model/reserve_calendar_model/course_order_details_data.dart';
import '../../../../widget/reserve/reserve_slider.dart';
import '../../screen/reserve_calendar/provider/calendar_provider.dart';


class WeekCalendarTableView extends StatefulWidget {
  const WeekCalendarTableView({super.key});

  @override
  State<WeekCalendarTableView> createState() => _WeekCalendarTableViewState();
}

class _WeekCalendarTableViewState extends State<WeekCalendarTableView> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final vm = Provider.of<ReserveCalendarScreenProvider>(
        context,
        listen: false,
      );
      vm.initWeekCalendarDataForWeek();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        return ChangeNotifierProvider(
          create: (context) => CalendarProvider(),
          child: Consumer<CalendarProvider>(
            builder: (context, provider, child) {
              return WeekCalendarTable(
                eventsMapWithTime: vm.weekDataAPI, //輸入資料會動更新畫面
                onEventTap: (weekEvent) {
                  vm.setStateWidgetView(CourseDateTop()); //顯示狀態欄
                  vm.setCalendarEventDataSize(
                    Calendar.week,
                    vm.weekDataAPI.length,
                  ); //
                  vm.setCalendarCourseOrderData(
                    Calendar.week,
                    CourseOrderDetailsData(
                      date: vm.selectDay(weekEvent.start),
                      week: vm.weekOfFormat(weekEvent.start.weekday),
                      memberName: weekEvent.member,
                      beauticianName: weekEvent.beautician,
                      courseName: weekEvent.title,
                      phoneNumber: '${weekEvent.phone}',
                      price: weekEvent.price?.toDouble(),
                      startTime: vm.timeOfFormat(
                        vm.dateTimeToTD(weekEvent.start),
                      ),
                      endTime: vm.timeOfFormat(vm.dateTimeToTD(weekEvent.end)),
                      orderNumber: weekEvent.orderNumber,
                    ),
                  );

                  print('${weekEvent.title}: !!!!!!!!!');
                },
              );
            },
          ),
        );
      },
    );
  }
}
