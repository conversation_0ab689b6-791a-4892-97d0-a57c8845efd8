import 'package:flutter/material.dart';


class CustomTabBarView extends StatelessWidget {

final int currentIndex;

final List<Widget> children;

  const CustomTabBarView({
    super.key,
    required this.currentIndex,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {

final validIndex = currentIndex.clamp(0, children.length - 1);

print('CustomTabBarView - currentIndex: $currentIndex, validIndex: $validIndex, children.length: ${children.length}'); // 加入這行

return IndexedStack(
      index: validIndex,
      children: children,
    );
  }
}