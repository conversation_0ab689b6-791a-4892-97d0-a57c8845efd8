// 自定義日曆線框繪製器
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// 自定義日曆線框繪製器
class CalendarGridPainter extends CustomPainter {
  final Color lineColor;
  final double lineWidth;
  final int rows;
  final int cols;
  final bool drawOuterBorder;
  final bool drawInnerLines;

  CalendarGridPainter({
    this.lineColor = Colors.black,
    this.lineWidth = 1.0,
    this.rows = 6,
    this.cols = 7,
    this.drawOuterBorder = true,
    this.drawInnerLines = true,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = lineColor
      ..strokeWidth = lineWidth
      ..style = PaintingStyle.stroke;

    // 計算每個格子的寬度和高度
    final cellWidth = size.width / cols;
    final cellHeight = size.height / rows;

    // 繪製外邊框
    if (drawOuterBorder) {
      final rect = Rect.fromLTWH(0, 0, size.width, size.height);
      canvas.drawRect(rect, paint);
    }

    if (drawInnerLines) {
      // 繪製垂直線
      for (int i = 1; i < cols; i++) {
        final x = i * cellWidth;
        canvas.drawLine(Offset(x, 0), Offset(x, size.height), paint);
      }

      // 繪製水平線
      for (int i = 1; i < rows; i++) {
        final y = i * cellHeight;
        canvas.drawLine(Offset(0, y), Offset(size.width, y), paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}