import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../provider/reserve_calendar_provider.dart';
import '../../resource/MyColor.dart';

//enum Calendar { day, week, month, year }

class SingleChoice extends StatefulWidget {
  final Function(Calendar)? onSelectionChanged;
  final Calendar initialValue;

  const SingleChoice({
    super.key,
    this.onSelectionChanged,
    this.initialValue = Calendar.month,
  });

  @override
  State<SingleChoice> createState() => _SingleChoiceState();
}

class _SingleChoiceState extends State<SingleChoice> {
  late Calendar calendarView;

  @override
  void initState() {
    super.initState();
    calendarView = widget.initialValue;
  }

  @override
  void didUpdateWidget(SingleChoice oldWidget) {
    super.didUpdateWidget(oldWidget);
    print('didUpdateWidget: ${oldWidget.initialValue} -> ${widget.initialValue}');
    if (oldWidget.initialValue != widget.initialValue) {
      setState(() {
        calendarView = widget.initialValue;


      });
      print('更新 calendarView: $calendarView');
    }
  }

  @override
  Widget build(BuildContext context) {
    final double buttonH = 60.h;
    final double buttonW = 240.w;
    return SizedBox(
      width: buttonW, // 固定寬度
      height: buttonH, // 固定高度
      child: SegmentedButton<Calendar>(
        segments: <ButtonSegment<Calendar>>[
          ButtonSegment<Calendar>(
            value: Calendar.month,
            label: Container(
              width: buttonW, // 固定每個按鈕的寬度
              height: buttonH, // 固定按鈕高度，確保背景大小一致
              alignment: Alignment.center, // 文字完全置中
              child: Text(
                '月',
                style: TextStyle(fontSize: 24.sp), // 縮小文字大小
                textAlign: TextAlign.center,
              ),
            ),
          ),
          ButtonSegment<Calendar>(
            value: Calendar.week,
            label: Container(
              width: buttonW,
              height: buttonH, // 固定按鈕高度
              alignment: Alignment.center,
              child: Text(
                '周',
                style: TextStyle(fontSize: 24.sp), // 縮小文字大小
                textAlign: TextAlign.center,
              ),
            ),
          ),
          ButtonSegment<Calendar>(
            value: Calendar.day,
            label: Container(
              width: buttonW,
              height: buttonH, // 固定按鈕高度
              alignment: Alignment.center,
              child: Text(
                '日',
                style: TextStyle(fontSize: 24.sp), // 縮小文字大小
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
        showSelectedIcon: false,
        selected: <Calendar>{calendarView},
        onSelectionChanged: (Set<Calendar> newSelection) {
          setState(() {
            calendarView = newSelection.first;
          });
          widget.onSelectionChanged?.call(calendarView);
        },
        style: SegmentedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          backgroundColor: Colors.transparent,
          foregroundColor: MyColors.brown_57_54_18,
          side:  BorderSide(color: MyColors.brown_57_54_18, width: 1.0),
          selectedBackgroundColor:  MyColors.brown_57_54_18,
          selectedForegroundColor: Colors.white,
          padding: EdgeInsets.zero, // 移除所有 padding，讓背景完全跟隨外框
          minimumSize: Size.zero, // 允許更小的最小尺寸
          tapTargetSize: MaterialTapTargetSize.shrinkWrap, // 緊縮點擊區域
        ),
      ),
    );
  }
}

