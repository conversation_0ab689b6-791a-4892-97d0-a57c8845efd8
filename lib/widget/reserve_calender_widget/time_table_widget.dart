import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/model/reserve_calendar_model/time_table_event_model.dart';
import 'package:sbar_pos/model/reserve_calendar_model/time_table_event_data_time_model.dart';
import 'package:sbar_pos/model/reserve_calendar_model/timetable_item.dart';
import 'package:sbar_pos/screen/reserve_calendar/provider/timetable_provider.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/timetable_controller.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/timetable_horizontal.dart';
import 'package:sbar_pos/tools/timeofday_transform.dart';
import 'package:sbar_pos/widget/hint/empty_hint.dart';

import '../../provider/reserve_calendar_provider.dart';

class TimeTableWidget extends StatefulWidget {
  final int startHour;
  final int endHour;
  final double? cellHeight;
  final double? timelineWidth;
  final double? cellWidth;
  final Function(TimeTableEventDataTimeModel event, String personName)? onEventTap;

  const TimeTableWidget({
    super.key,
    this.startHour = 10,
    this.endHour = 24,
    this.cellHeight,
    this.timelineWidth,
    this.cellWidth,
    this.onEventTap,
  });

  @override
  State<TimeTableWidget> createState() => _TimeTableWidgetState();
}

class _TimeTableWidgetState extends State<TimeTableWidget> {
  late TimetableController _controller;
  List<Person<TimeTableEventModel>> _people = [];

  @override
  void initState() {
    super.initState();
    _controller = TimetableController(
      start: DateTime.now(),
      startHour: widget.startHour,
      endHour: widget.endHour,
      cellHeight: widget.cellHeight ?? 60.0,
      headerHeight: 20.0,
      timelineWidth: widget.timelineWidth ?? 80.0,
      cellWidth: widget.cellWidth ?? 180.0,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        final targetDate = vm.currentDateForDay;
        final normalizedDate = DateTime(targetDate.year, targetDate.month, targetDate.day);
        final dayEvents = vm.getEventsByDate(normalizedDate);

        if (dayEvents.isEmpty) {
          // 當天沒有資料
          return EmptyHint(hint: '當日無資料');
        }

        _people = dayEvents.entries.map((entry) {
          final personName = entry.key;
          final eventDataList = entry.value;

          final items = eventDataList.map((eventData) {
            final startTOD = TimeOfDayTransform(dateTime: eventData.startTime);
            final endTOD = TimeOfDayTransform(dateTime: eventData.endTime);

            final startTime = DateTime(
              targetDate.year,
              targetDate.month,
              targetDate.day,
              startTOD.stringToTimeDay.hour,
              startTOD.stringToTimeDay.minute,
            );

            final endTime = DateTime(
              targetDate.year,
              targetDate.month,
              targetDate.day,
              endTOD.stringToTimeDay.hour,
              endTOD.stringToTimeDay.minute,
            );

            return TimetableItem<TimeTableEventModel>(
              startTime,
              endTime,
              data: TimeTableEventModel(
                title: eventData.title,
                clientName: eventData.clientName,
                status: eventData.status,
                price: eventData.price,
                deposit: eventData.depoist,
                orderNumber: eventData.orderNumber,
                phoneNumber: eventData.phoneNumber,
              ),
            );
          }).toList();

          return Person<TimeTableEventModel>(name: personName, items: items);
        }).toList();

        return TimetableHorizontal<TimeTableEventModel>(
          controller: _controller,
          people: _people,
          useTimetableEventCard: true,
          eventCardBackgroundColor: const Color.fromARGB(255, 255, 255, 255),
          eventCardBorderColor: const Color.fromARGB(255, 229, 233, 219),
          eventCardType: CardType.rounded,
          onCardTap: (item, name) => _handleCardTap(item, name, dayEvents),
          timeLabelBuilder: (time) => Text("${time.hour.toString().padLeft(2,'0')}:00", style: const TextStyle(fontSize:12)),
          personLabelBuilder: (name) => Text(
            name,
            style: const TextStyle(
              fontSize: 20,
              color: Color.fromARGB(255, 135, 142, 104),
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      },
    );
  }

  void _handleCardTap(
      TimetableItem<TimeTableEventModel> timetableItem,
      String personName,
      Map<String, List<TimeTableEventDataTimeModel>> dayEvents,
      ) {
    if (widget.onEventTap != null && timetableItem.data != null) {
      final personEvents = dayEvents[personName] ?? [];
      final matchingEvent = personEvents.firstWhere(
            (event) =>
        event.title == timetableItem.data?.title &&
            event.clientName == timetableItem.data?.clientName,
        orElse: () => TimeTableEventDataTimeModel(
          title: timetableItem.data?.title ?? '',
          clientName: timetableItem.data?.clientName ?? '',
          status: timetableItem.data?.status ?? '',
          startTime: '${timetableItem.start.hour}:${timetableItem.start.minute}',
          endTime: '${timetableItem.end.hour}:${timetableItem.end.minute}',
        ),
      );

      widget.onEventTap!(matchingEvent, personName);
    }
  }
}
