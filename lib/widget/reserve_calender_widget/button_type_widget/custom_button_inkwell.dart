import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomButtonInkwell extends StatefulWidget{

  final String text;
  final VoidCallback? onPressed;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;
  final double? textSize;
  final double? borderRadius;
  final EdgeInsets? padding;
  final Color? borderColor;
  final double? borderWidth;

  const CustomButtonInkwell({
    super.key,
    required this.text,
    this.onPressed,
    this.textColor,
    this.backgroundColor,
    this.width,
    this.height,
    this.textSize,
    this.borderRadius,
    this.padding,
    this.borderColor,
    this.borderWidth,
  });

  @override
  State<CustomButtonInkwell> createState() => _CustomButtonInkwellState();
}

class _CustomButtonInkwellState extends State<CustomButtonInkwell>{

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width ?? double.infinity,
      height: widget.height ?? 48.h,
      decoration: BoxDecoration(
        color: widget.backgroundColor ?? Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(widget.borderRadius ?? 25),
        border: widget.borderColor != null
            ? Border.all(
          color: widget.borderColor!,
          width: widget.borderWidth ?? 1.0,
        )
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: widget.onPressed,
          borderRadius: BorderRadius.circular(widget.borderRadius ?? 25),
          child: Container(
            padding: widget.padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
            child: Center(
              child: Text(
                widget.text,
                style: TextStyle(
                  fontSize: widget.textSize ?? 16.sp,
                  fontWeight: FontWeight.w500,
                  color: widget.textColor ?? Color(0xFF666666),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}