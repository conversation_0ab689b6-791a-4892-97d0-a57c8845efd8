import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';

import '../../../../../resource/MyColor.dart';
import '../../../../../util/MyRoutes.dart';
import '../../../../../util/StringUtils.dart';
import '../../../../../widget/container_with_radius.dart';
import '../../../screen/member/member_info/member_info_main_screen.dart';
import '../../../screen/start/my_app.dart';

class CourseDateTop extends StatefulWidget {
  const CourseDateTop({super.key});

  @override
  State<CourseDateTop> createState() => _CourseDateTopState();
}

class _CourseDateTopState extends State<CourseDateTop> {

  @override
  Widget build(BuildContext context) {
    final titleStyle = TextStyle(
      fontSize: 32.sp,
      color: MyColors.green_129_128_94,
    );
    return Consumer<ReserveCalendarScreenProvider>(
          builder: (context, vm, _){
            print('vm.selectCalender: ${vm.selectCalender}');

            return Padding(
              padding:  EdgeInsets.only(top: 0.h,left: 41.w,right: 41.w,bottom: 20.h),
              child: Column(

                children: [

                  Divider(thickness: 1, height: 1),
                  SizedBox(height: 23.5.h),

                  //訂單編號
                  Align(
                      alignment: Alignment.centerLeft,
                      child: Text('訂單編號：${vm.getCalendarCourseOrderData(vm.selectCalender)?.orderNumber ?? '-'}', style: titleStyle)),
                  SizedBox(height: 24.h),

                  //課程名稱
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                      vm.getCalendarCourseOrderData(vm.selectCalender)?.courseName ?? '課程名稱',
                      style: titleStyle.copyWith(
                        fontSize: 28.sp,
                        color: MyColors.brown_57_54_18,
                        overflow: TextOverflow.ellipsis,
                      ),
                      maxLines: 2,
                    ),
                  ),
                  SizedBox(height: 29.h),


                  //會員
                  InkWell(
                    onTap: (){
                      //導向基本資料？
                      //TODO:若要導向基本資料的話要把provider資料抽換掉
                      //TODO:更新會員資訊
                      // TODO:                     vm.setMemberInfo(
                      //TODO:                         avatar: avatar,
                      //TODO:                         name: name,
                      //TODO:                         phone: phone,
                      //TODO:                         birthday: birthday,
                      //TODO:                         email: eMail,
                      //TODO:                       );

                      pushTo(
                        PageName.memberInfoMain.toString(),
                        builder: (context) => MemberInfoMainScreen(initIndex: 0),
                      );
                    },
                    child: Row(
                      children: [
                        Text(
                            '會員：',
                            style: titleStyle
                        ),

                        //會員名稱
                        Text(
                          vm.getCalendarCourseOrderData(vm.selectCalender)?.memberName ?? '-',
                          style: titleStyle.copyWith(
                              fontSize: 28.sp,
                              color: MyColors.brown_57_54_18,
                              overflow: TextOverflow.ellipsis,
                              fontWeight: FontWeight.w700
                          ),
                          maxLines: 2,
                        ),

                        Spacer(),

                        Icon(Icons.arrow_forward_ios_rounded,size: 30.w,)
                      ],
                    ),
                  ),

                  SizedBox(height: 6.h,),

                  ///會員手機
                  Row(
                    children: [

                      //填充用
                      Opacity(
                        opacity: 0,
                        child: Text(
                            '會員：',
                            style: titleStyle
                        ),
                      ),

                      //會員名稱
                      Text(
                          vm.getCalendarCourseOrderData(vm.selectCalender)?.phoneNumber ?? '-',
                          style: titleStyle
                      ),


                    ],
                  ),

                  SizedBox(height: 26.h,),

                  ///美容師
                  Row(
                    children: [
                      Text(
                          '美容師：',
                          style: titleStyle
                      ),

                      //會員名稱
                      Text(
                          vm.getCalendarCourseOrderData(vm.selectCalender)?.beauticianName ?? '-',
                          style: titleStyle
                      ),

                      SizedBox(width: 16.w,),
                    ],
                  ),

                  SizedBox(height: 30.h,),

                  ///日期
                  Row(
                    children: [
                      Text(
                          '日期：${vm.getCalendarCourseOrderData(vm.selectCalender)?.date} ${vm.getCalendarCourseOrderData(vm.selectCalender)?.startTime ?? '-'}~${vm.getCalendarCourseOrderData(vm.selectCalender)?.endTime ?? '-'}' ,
                          style: titleStyle.copyWith(fontSize: 25.sp)
                      ),
                    ],
                  ),

                  SizedBox(height: 29.h,),

                  ///金額
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Text(
                        '\$${StringUtils.formatMoneyForDouble(vm.getCalendarCourseOrderData(vm.selectCalender)?.price ?? 0)}',
                        style: titleStyle.copyWith(fontSize: 25.sp,color: MyColors.orange_240_149_68,fontWeight: FontWeight.w700)
                    ),
                  ),

                  SizedBox(height: 45.h,),

                  ///付款按鈕
                  _baseBtn((){},'付款'),
                  SizedBox(height: 13.h,),

                  ///取消預約按鈕
                  _baseBtn((){},'取消預約'),
                  SizedBox(height: 13.h,),

                  ///已到店按鈕
                  _baseBtn((){},'已到店'),
                  SizedBox(height: 13.h,),

                  ///No Show按鈕
                  _baseBtn((){},'No Show'),
                  SizedBox(height: 13.h,),

                  ///編輯按鈕
                  _baseBtn((){},'編輯'),
                  SizedBox(height: 13.h,),
                ],
              ),
            );
          }

    );
  }
}

///base btn
Widget _baseBtn(VoidCallback onTap, String title, ) {
  return ContainerWithRadius(
    onTap: onTap,
    isHaveBorder: true,
    w: 460.w,
    h: 63.h,
    r: 20.r,
    color: Colors.white,
    boxShadow: [
      BoxShadow(
        color: const Color(0x14000000),
        offset: const Offset(3, 6),
        blurRadius: 10.r,
      ),
    ],
    child: Center(
      child: Text(
        title,
        style: TextStyle(color: MyColors.green_129_128_94, fontSize: 25.sp),
      ),
    ),
  );
}
