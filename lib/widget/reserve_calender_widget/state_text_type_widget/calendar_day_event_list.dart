import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';

import '../../../../../model/reserve_calendar_model/course_order_details_data.dart';
import '../calendar_event_card_listview.dart';


class CalendarDayEventList extends StatelessWidget {
  const CalendarDayEventList({super.key});

  @override
  Widget build(BuildContext context) {

    return Consumer<ReserveCalendarScreenProvider>(builder: (context,vm,_){

      return CalendarEventCardListView(
        events:  vm.calendarEventDataList,
        // 不設定 date 參數，自動顯示今天的事件
        date: vm.calenderMomEventDataModel?.date,
        onCardTap: (eventData) {
          print('點擊了事件：${vm.calenderMomEventDataModel?.date}');
          print('點擊了事件：${eventData.memberName}');
          print('點擊了事件：${eventData.price}');
          print('點擊了事件 date：${vm.courseOrderDetailsData?.date}');

          print('點擊事件Calendar: ${vm.selectCalender}');


          vm.setCheckEvent(true);//_buildContentWidget替換日曆（月）的狀態欄顯示的資訊物件

          vm.setCalendarCourseOrderData(Calendar.month, CourseOrderDetailsData(
              memberName: eventData.memberName,
              price: double.tryParse(eventData.price) ?? 0,
              courseName: eventData.title,
              beauticianName: eventData.beautician,
              startTime: eventData.startTime,
              endTime: eventData.endTime,
              date: vm.getCalendarCourseOrderData(vm.selectCalender)?.date ?? eventData.date,
              week: vm.getCalendarCourseOrderData(vm.selectCalender)?.week,
          ));

        },
      );
    });

  }
}