import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/tools/calendar_gridpainter.dart';
import '../../provider/reserve_calendar_provider.dart';
import '../../screen/reserve_calendar/provider/custompaint_calendar.dart';

//使用 CustomPaintCalendarForMonth 的日曆組件

class CustomPaintCalendarForMonth extends StatefulWidget {
  final Color gridLineColor;
  final double gridLineWidth;
  final bool showGridLines;
  final TextStyle? headerTextStyle;
  final TextStyle? weekdayTextStyle;
  final TextStyle? cellTextStyle;
  final double cellHeight;
  final bool showWeekdayGrid;
  final void Function(DateTime date)? onDateSelected;

  const CustomPaintCalendarForMonth({
    super.key,
    this.gridLineColor = const Color(0xFFC5C5C5),
    this.gridLineWidth = 1.5,
    this.showGridLines = true,
    this.headerTextStyle,
    this.weekdayTextStyle,
    this.cellTextStyle,
    this.cellHeight = 50.0,
    this.showWeekdayGrid = false,
    this.onDateSelected,
  });

  @override
  State<CustomPaintCalendarForMonth> createState() =>
      _CustomPaintCalendarForMonthState();
}

class _CustomPaintCalendarForMonthState
    extends State<CustomPaintCalendarForMonth> {
  late ReserveCalendarScreenProvider vm;
  late CalendarController controller;


  DateTime? _selectedDate; // 記錄選中的日期

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    vm = context.watch<ReserveCalendarScreenProvider>();
    controller = context.read<CalendarController>();

    _selectedDate = vm.currentDateForMonth;

    // 初始化 PageController
    vm.pageController ??= PageController(
      initialPage: vm.monthToPage(vm.currentDateForMonth),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _syncDateToController(_selectedDate!);
    });
  }





  void _syncDateToController(DateTime date) {
    controller.selectDate(date);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildWeekdays(),
        Expanded(
          child:

          PageView.builder(
            physics: NeverScrollableScrollPhysics(),  //這邊先不要做pageView，問題太多了，沒時間搞
            controller: vm.pageController,
            onPageChanged: (page) {
              final newMonth = vm.pageToMonth(page);

              // 保留選中的日期，若跨月就取第一天
              DateTime candidateDate;
              if (_selectedDate != null &&
                  _selectedDate!.year == newMonth.year &&
                  _selectedDate!.month == newMonth.month) {
                candidateDate = _selectedDate!;
              } else {
                candidateDate = DateTime(newMonth.year, newMonth.month, 1);
              }

              _selectedDate = candidateDate;
              vm.updateCurrentDateForMonth(candidateDate);
            },
            itemBuilder: (context, pageIndex) {
              final month = vm.pageToMonth(pageIndex);
              return _buildMonthGrid(month);
            },
          )

        ),
      ],
    );
  }

  Widget _buildWeekdays() {
    final weekdays = ['週一', '週二', '週三', '週四', '週五', '週六', '週日'];
    return SizedBox(
      height: 40,
      child: Stack(
        children: [
          if (widget.showGridLines && widget.showWeekdayGrid)
            CustomPaint(
              painter: CalendarGridPainter(
                lineColor: widget.gridLineColor,
                lineWidth: widget.gridLineWidth,
                rows: 1,
                cols: 7,
              ),
              size: const Size(double.infinity, 40),
            ),
          Row(
            children: weekdays
                .map(
                  (label) => Expanded(
                    child: Center(
                      child: Text(
                        label,
                        style:
                            widget.weekdayTextStyle ??
                            const TextStyle(
                              fontWeight: FontWeight.w600,
                              color: Color(0xFFA4AB8C),
                              fontSize: 20,
                            ),
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMonthGrid(DateTime month) {
    final daysInMonth = _getDaysInMonth(month);
    final rows = (daysInMonth.length / 7).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        final availableHeight = constraints.maxHeight;
        final actualCellHeight = availableHeight / rows;

        return Stack(
          children: [
            if (widget.showGridLines)
              CustomPaint(
                painter: CalendarGridPainter(
                  lineColor: widget.gridLineColor,
                  lineWidth: widget.gridLineWidth,
                  rows: rows,
                  cols: 7,
                ),
                size: Size(double.infinity, availableHeight),
              ),
            Consumer<ReserveCalendarScreenProvider>(
              builder: (context, vm, child) {
                return GridView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 7,
                    mainAxisExtent: actualCellHeight,
                  ),
                  itemCount: daysInMonth.length,
                  itemBuilder: (context, index) {
                    final date = daysInMonth[index];
                    final isSelected =
                        _selectedDate != null &&
                        _isSameDay(date, _selectedDate!);
                    final isToday = _isSameDay(date, DateTime.now());
                    final isCurrentMonth = date.month == month.month;

                    final dateEvents = vm.calendarEventDataList[date] ?? [];

                    return GestureDetector(
                      onTap: () {
                        _selectedDate = date;
                        vm.updateCurrentDateForMonth(date);
                        controller.selectDate(date);
                        widget.onDateSelected?.call(date);
                        vm.setCalendarEventDataList(date); // 更新右側事件列表
                        setState(() {});
                      },
                      child: Container(
                        margin: const EdgeInsets.all(1),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.green.withOpacity(0.3)
                              : Colors.white,
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Container(
                                  width: 30,
                                  height: 26,
                                  decoration: BoxDecoration(
                                    color: isToday
                                        ? const Color(0xFF81805E)
                                        : Colors.transparent,
                                  ),
                                  child: Center(
                                    child: Text(
                                      '${date.day}',
                                      style:
                                          widget.cellTextStyle ??
                                          TextStyle(
                                            color: isToday
                                                ? Colors.white
                                                : (isCurrentMonth ? const Color(0xFF939274) : Colors.grey),
                                            fontSize: 20,
                                          ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 2),
                            Expanded(
                              child: Center(
                                child: Text(
                                  dateEvents.isEmpty
                                      ? '無預約'
                                      : '${dateEvents.length}筆',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: isCurrentMonth
                                        ? const Color(0xFF818B64)
                                        : Colors.grey,
                                    fontWeight: dateEvents.isEmpty  ? FontWeight.w400 : FontWeight.w700
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ],
        );
      },
    );
  }

  List<DateTime> _getDaysInMonth(DateTime month) {
    final firstDay = DateTime(month.year, month.month, 1);
    final lastDay = DateTime(month.year, month.month + 1, 0);
    final firstWeekday = (firstDay.weekday - 1) % 7;

    final days = <DateTime>[];

    // 前面補空格 (上個月的尾巴)
    for (int i = firstWeekday - 1; i >= 0; i--) {
      days.add(firstDay.subtract(Duration(days: i + 1)));
    }

    // 當月日期
    for (int day = 1; day <= lastDay.day; day++) {
      days.add(DateTime(month.year, month.month, day));
    }

    // 後面補空格 (下個月的開頭)
    while (days.length % 7 != 0) {
      days.add(lastDay.add(Duration(days: days.length - days.indexOf(lastDay))));
    }

    // 保證固定 6 週 (42 格)
    while (days.length < 42) {
      days.add(days.last.add(const Duration(days: 1)));
    }

    return days;
  }


  bool _isSameDay(DateTime date1, DateTime date2) =>
      date1.year == date2.year &&
      date1.month == date2.month &&
      date1.day == date2.day;
}
