
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/model/reserve_calendar_model/timetable_item.dart';
import 'package:sbar_pos/screen/reserve_calendar/provider/timetable_provider.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/timetable_controller.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/time_table_event_card.dart';

import '../../provider/reserve_calendar_provider.dart';

class TimetableHorizontal<T> extends StatefulWidget {
  final TimetableController? controller;
  final List<Person<T>>? people;
  final Widget Function(TimeOfDay)? timeLabelBuilder;
  final Widget Function(String name)? personLabelBuilder;
  final Widget Function(TimetableItem<T>)? itemBuilder;

  // TimetableEventCard 支持
  final bool useTimetableEventCard;
  final Color? eventCardBackgroundColor;
  final Color? eventCardBorderColor;
  final CardType eventCardType;

  // Provider 支持（可選）
  final bool enableProvider;
  final TimetableProvider<T>? provider;

  // 回調函數
  final OnCardTapCallback<T>? onCardTap;

  const TimetableHorizontal({
    super.key,
    this.controller,
    this.people,
    this.itemBuilder,
    this.timeLabelBuilder,
    this.personLabelBuilder,

    // TimetableEventCard 參數
    this.useTimetableEventCard = false,
    this.eventCardBackgroundColor,
    this.eventCardBorderColor,
    this.eventCardType = CardType.rounded,

    // Provider 參數（可選）
    this.enableProvider = false,
    this.provider,

    // 回調函數
    this.onCardTap,
  });

  @override
  State<TimetableHorizontal<T>> createState() => _TimetableHorizontalState<T>();
}

class _TimetableHorizontalState<T> extends State<TimetableHorizontal<T>> {
  late TimetableController _controller;
  TimetableProvider<T>? _provider;

  final ScrollController _headerScrollController = ScrollController();
  final ScrollController _horizontalContentScrollController = ScrollController();
  final ScrollController _verticalScrollController = ScrollController();

  bool _isProviderOwned = false;

  @override
  void initState() {
    super.initState();
    _initializeComponents();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_horizontalContentScrollController.hasClients) {
        _horizontalContentScrollController.jumpTo(0);
      }
    });

    _horizontalContentScrollController.addListener(_syncHeaderScroll);
  }

  void _initializeComponents() {
    //這邊先寫固定10點到24點，有需要改再說，要改的話TimeTableWidget也要改
    _controller = widget.controller ??
        TimetableController(
          startHour: 10,
          endHour: 24,
          cellHeight: 60,
          headerHeight: 20,
          timelineWidth: 80,
          cellWidth: 180,
        );

    // 如果啟用 Provider，初始化 Provider
    if (widget.enableProvider) {
      if (widget.provider != null) {
        _provider = widget.provider!;
        _isProviderOwned = false;
      } else {
        _provider = TimetableProvider<T>(
          controller: _controller,
          initialPeople: widget.people ?? [],
          eventCardBackgroundColor: widget.eventCardBackgroundColor,
          eventCardBorderColor: widget.eventCardBorderColor,
          eventCardType: widget.eventCardType,
          useTimetableEventCard: widget.useTimetableEventCard,
        );
        _isProviderOwned = true;
      }
    }
  }

  void _syncHeaderScroll() {
    if (_headerScrollController.hasClients &&
        _horizontalContentScrollController.hasClients) {
      final offset = _horizontalContentScrollController.offset;
      final max = _headerScrollController.position.maxScrollExtent;
      final min = _headerScrollController.position.minScrollExtent;
      final safeOffset = offset.clamp(min, max);
      if (_headerScrollController.offset != safeOffset) {
        _headerScrollController.jumpTo(safeOffset);
      }
    }
  }

  @override
  void dispose() {
    _horizontalContentScrollController.removeListener(_syncHeaderScroll);
    _headerScrollController.dispose();
    _horizontalContentScrollController.dispose();
    _verticalScrollController.dispose();

    if (_isProviderOwned && _provider != null) {
      _provider!.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.enableProvider && _provider != null) {
      return ChangeNotifierProvider<TimetableProvider<T>>.value(
        value: _provider!,
        child: Consumer<TimetableProvider<T>>(
          builder: (context, provider, child) => _buildTimetableContent(
            controller: provider.controller,
            people: provider.mergedPeople,
            useTimetableEventCard: provider.useTimetableEventCard,
            eventCardBackgroundColor: provider.eventCardBackgroundColor,
            eventCardBorderColor: provider.eventCardBorderColor,
            eventCardType: provider.eventCardType,
          ),
        ),
      );
    } else {
      // 舊式不使用 Provider 的方式
      return _buildTimetableContent(
        controller: _controller,
        people: _mergePeople(widget.people ?? []),
        useTimetableEventCard: widget.useTimetableEventCard,
        eventCardBackgroundColor: widget.eventCardBackgroundColor,
        eventCardBorderColor: widget.eventCardBorderColor,
        eventCardType: widget.eventCardType,
      );
    }
  }

  List<Person<T>> _mergePeople(List<Person<T>> people) {
    final Map<String, List<TimetableItem<T>>> map = {};
    for (final person in people) {
      map.putIfAbsent(person.name, () => []).addAll(person.items);
    }
    return map.entries.map((e) => Person(name: e.key, items: e.value)).toList();
  }

  Widget _buildTimetableContent({
    required TimetableController controller,
    required List<Person<T>> people,
    required bool useTimetableEventCard,
    required Color? eventCardBackgroundColor,
    required Color? eventCardBorderColor,
    required CardType eventCardType,
  }) {
    final startHour = controller.startHour ;
    final endHour = controller.endHour;
    final cellWidth = controller.cellWidth ?? 80.0;
    final hours = List.generate(endHour - startHour, (i) {
      return TimeOfDay(hour: startHour + i, minute: 0);
    });

    final contentWidth = cellWidth * hours.length;
    final personListWidth = controller.timelineWidth * 2; // 改工作員名單的寬度

    return Column(
      children: [
        // 時間軸 header
        _buildTimeHeader(
          hours: hours,
          cellWidth: cellWidth,
          controller: controller,
          personListWidth: personListWidth,
        ),

        const SizedBox(height: 10),

        // 主內容區域
        Expanded(
          child: SingleChildScrollView(
            controller: _verticalScrollController,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // 人員名單（固定）
                _buildPersonList(
                  people: people,
                  controller: controller,
                  personListWidth: personListWidth,
                ),

                // 時間格與事件內容（可左右滑動）
                Expanded(
                  child: SingleChildScrollView(
                    controller: _horizontalContentScrollController,
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      children: people
                          .map((person) => _buildPersonRow(
                        person: person,
                        hours: hours,
                        cellWidth: cellWidth,
                        contentWidth: contentWidth,
                        controller: controller,
                        startHour: startHour,
                        useTimetableEventCard: useTimetableEventCard,
                        eventCardBackgroundColor:
                        eventCardBackgroundColor,
                        eventCardBorderColor: eventCardBorderColor,
                        eventCardType: eventCardType,
                      ))
                          .toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTimeHeader({
    required List<TimeOfDay> hours,
    required double cellWidth,
    required TimetableController controller,
    required double personListWidth,
  }) {
    return Row(
      children: [
        SizedBox(
          width: personListWidth,
          child: const Center(
            child: Text(
              '美容師/時間',
              style: TextStyle(
                fontSize: 15,
                color: Color.fromARGB(255, 152, 160, 123),
              ),
            ),
          ),
        ),
        Expanded(
          child: SingleChildScrollView(
            controller: _headerScrollController,
            scrollDirection: Axis.horizontal,
            physics: const NeverScrollableScrollPhysics(),
            child: Padding(
              padding: EdgeInsets.only(left: cellWidth / 10),
              child: SizedBox(
                width: cellWidth * hours.length,
                height: controller.headerHeight,
                child: Stack(
                  children: [
                    // 畫格線
                    Row(
                      children: hours.map((_) {
                        return Container(
                          width: cellWidth,
                          height: controller.headerHeight,
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.transparent,
                                width: 0.5,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),

                    // 時間標籤
                    ...hours.asMap().entries.map((entry) {
                      final i = entry.key;
                      final time = entry.value;
                      return Positioned(
                        left: i * cellWidth,
                        bottom: 0,
                        child: SizedBox(
                          width: cellWidth,
                          child: Row(
                            children: [
                              Transform.translate(
                                offset: const Offset(-16, 0),
                                child: widget.timeLabelBuilder?.call(time) ??
                                    Text(
                                      time.format(context),
                                      style: const TextStyle(
                                        fontSize: 15,
                                        color: Color.fromARGB(255, 123, 130, 92),
                                      ),
                                    ),
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                '15',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color.fromARGB(255, 128, 135, 98),
                                ),
                              ),
                              const Spacer(flex: 1),
                              const Text(
                                '30',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color.fromARGB(255, 128, 135, 98),
                                ),
                              ),
                              const Spacer(flex: 1),
                              const Text(
                                '45',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color.fromARGB(255, 128, 135, 98),
                                ),
                              ),
                              const SizedBox(width: 38),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPersonList({
    required List<Person<T>> people,
    required TimetableController controller,
    required double personListWidth,
  }) {
    return Column(
      children: people.map((person) {
        return Container(
          width: personListWidth,
          height: controller.cellHeight,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          decoration: BoxDecoration(
            color: const Color(0xFFFFF8DD),
            border: Border.all(
              color: Colors.grey.shade400,
              width: 0.5,
            ),
          ),
          child: widget.personLabelBuilder?.call(person.name) ??
              Text(person.name),
        );
      }).toList(),
    );
  }

  Widget _buildPersonRow({
    required Person<T> person,
    required List<TimeOfDay> hours,
    required double cellWidth,
    required double contentWidth,
    required TimetableController controller,
    required int startHour,
    required bool useTimetableEventCard,
    required Color? eventCardBackgroundColor,
    required Color? eventCardBorderColor,
    required CardType eventCardType,
  }) {
    return SizedBox(
      height: controller.cellHeight,
      width: contentWidth,
      child: Stack(
        children: [
          // 背景格線
          Row(
            children: hours.map((_) {
              return Container(
                width: cellWidth,
                height: controller.cellHeight,
                decoration: BoxDecoration(
                  border: Border(
                    top: BorderSide(
                      color: Colors.grey.shade300,
                      width: 0.5,
                    ),
                    bottom: BorderSide(
                      color: Colors.grey.shade300,
                      width: 0.5,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          // 事件卡片
          Padding(
            padding: EdgeInsets.only(left: cellWidth / 10),
            child: Stack(
              children: person.items.map((event) {
                final start = event.start;
                final left = ((start.hour + start.minute / 60.0) - startHour) *
                    cellWidth;
                final width =
                    event.duration.inMinutes / 60.0 * cellWidth;

                return Positioned(
                  left: left,
                  top: 2,
                  height: controller.cellHeight - 4,
                  width: width,
                  child: _buildEventCard(
                    event: event,
                    personName: person.name,
                    useTimetableEventCard: useTimetableEventCard,
                    eventCardBackgroundColor: eventCardBackgroundColor,
                    eventCardBorderColor: eventCardBorderColor,
                    eventCardType: eventCardType,
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventCard({
    required TimetableItem<T> event,
    required String personName,
    required bool useTimetableEventCard,
    required Color? eventCardBackgroundColor,
    required Color? eventCardBorderColor,
    required CardType eventCardType,
  }) {
    if (useTimetableEventCard) {
      return TimetableEventCard<T>(
        event: event,
        personName: personName,
        backgroundColor: eventCardBackgroundColor,
        borderColor: eventCardBorderColor,
        cardType: eventCardType,
        onTap: widget.onCardTap,
      );
    } else {
      return widget.itemBuilder?.call(event) ??
          Container(
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 202, 202, 188),
              borderRadius: BorderRadius.circular(4),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 6),
            child: Center(
              child: Text(
                event.data?.toString() ?? '',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          );
    }
  }
}
