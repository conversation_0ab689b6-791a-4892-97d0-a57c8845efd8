import 'package:flutter/material.dart';
import 'package:sbar_pos/model/reserve_calendar_model/time_table_event_model.dart';
import 'package:sbar_pos/model/reserve_calendar_model/timetable_item.dart';

import '../../provider/reserve_calendar_provider.dart';



typedef OnCardTapCallback<T> = void Function(TimetableItem<T> event, String personName);

class TimetableEventCard<T> extends StatefulWidget {
  final TimetableItem<T> event;
  final String personName;
  final Color? backgroundColor;
  final Color? borderColor;
  final CardType cardType;
  final OnCardTapCallback<T>? onTap;

  const TimetableEventCard({
    Key? key,
    required this.event,
    required this.personName,
    this.backgroundColor,
    this.borderColor,
    this.cardType = CardType.rounded,
    this.onTap,
  }) : super(key: key);

  @override
  State<TimetableEventCard<T>> createState() => _TimetableEventCardState<T>();
}

class _TimetableEventCardState<T> extends State<TimetableEventCard<T>> {
  bool _isHovered = false;

  void _handleTap() {
    if (widget.onTap != null) {
      widget.onTap!(widget.event, widget.personName);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: _handleTap,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            color: _getBackgroundColor(),
            borderRadius: _getBorderRadius(),
            border: Border.all(
              color: widget.borderColor ?? Colors.blue.shade300,
              width: _isHovered ? 2 : 1,
            ),
            boxShadow: _isHovered ? [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          padding: const EdgeInsets.all(10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 上排綠色標題
              Expanded(
                flex: 1,
                child: Container(
                  width: double.infinity,
                  child: Text(
                    _getTitle(),
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color.fromARGB(255, 160, 160, 133),
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              // 下排左邊教師人名，右邊狀態
              Expanded(
                flex: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // 左邊教師人名（黑色）
                    Expanded(
                      child: Text(
                        _getTeacherName(),
                        style: const TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // 右邊狀態
                    Text(
                      _getStatus(),
                      style: const TextStyle(
                        fontSize: 13,
                        color: Colors.red,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getBackgroundColor() {
    Color baseColor = widget.backgroundColor ?? Colors.blue.shade100;
    return _isHovered ? baseColor.withOpacity(0.9) : baseColor;
  }

  BorderRadius? _getBorderRadius() {
    switch (widget.cardType) {
      case CardType.square:
        return BorderRadius.zero;
      case CardType.rounded:
        return BorderRadius.circular(1);
      case CardType.circle:
        return BorderRadius.circular(20);
    }
  }

  String _getTitle() {
    if (widget.event.data is TimeTableEventModel) {
      return (widget.event.data as TimeTableEventModel).title;
    } else if (widget.event.data is String) {
      return widget.event.data as String;
    }
    return "課程名稱";
  }

  String _getTeacherName() {
    if (widget.event.data is TimeTableEventModel) {
      return (widget.event.data as TimeTableEventModel).clientName;
    }
    return widget.personName;
  }

  String _getStatus() {
    if (widget.event.data is TimeTableEventModel) {
      return (widget.event.data as TimeTableEventModel).status;
    }
    return "No Show";
  }
}