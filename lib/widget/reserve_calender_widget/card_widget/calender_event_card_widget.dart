import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:sbar_pos/model/reserve_calendar_model/event_card_model.dart';

class CalenderEventCardWidget extends StatefulWidget {
  final EventCardModel event;
  final VoidCallback? onTap;

  const CalenderEventCardWidget({super.key, required this.event, this.onTap});

  @override
  State<CalenderEventCardWidget> createState() => _CalenderEventCardWidgetState();
}

class _CalenderEventCardWidgetState extends State<CalenderEventCardWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 2),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: GestureDetector(
          // 替換 InkWell
          onTap: widget.onTap,
          child: Container(
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(14),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // 課程名稱
                              Text(
                                widget.event.title,
                                style: TextStyle(
                                  fontSize: 21,
                                  fontWeight: FontWeight.w500,
                                  color: Color(0xff393612),
                                ),
                              ),
                              // 箭頭圖標
                              Icon(
                                Icons.chevron_right,
                                color: Colors.grey[400],
                                size: 35,
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 8),

                        // 會員
                        Row(
                          children: [
                            Text(
                              '會員：',
                              style: TextStyle(
                                fontSize: 21,
                                color: Color(0xff81805E),
                              ),
                            ),
                            Text(
                              widget.event.memberName,
                              style: const TextStyle(
                                fontSize: 21,
                                fontWeight: FontWeight.w500,
                                overflow: TextOverflow.ellipsis
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // 美容師
                        Row(
                          children: [
                            Text(
                              '美容師：',
                              style: TextStyle(
                                fontSize: 21,
                                color: Color(0xff81805E),
                              ),
                            ),
                            Text(
                              widget.event.beautician,
                              style: const TextStyle(
                                fontSize: 21,
                                fontWeight: FontWeight.w500,
                                color: Color(0xff79835A)
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),

                        // 日期
                        Row(
                          children: [
                            Text(
                              '日期：',
                              style: TextStyle(
                                fontSize: 17,
                                color: Color(0xff81805E),
                              ),
                            ),
                            Text(
                              widget.event.date,
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w500,
                                color: Color(0xff81805E),
                              ),
                            ),

                             SizedBox(width: 4),

                            Text(
                              '${widget.event.startTime} ~ ${widget.event.endTime}',
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.w500,
                                color: Color(0xff81805E),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),

                        // 價格和狀態
                        Row(
                          children: [
                            Text(
                              '\$${widget.event.price}',
                              style: const TextStyle(
                                fontSize: 17,
                                fontWeight: FontWeight.bold,
                                color: Color(0xffF09544),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
