import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import '../../../../../widget/hint/empty_hint.dart';
import '../../../../../provider/reserve_calendar_provider.dart';
import '../../../resource/MyColor.dart';

class DatePickerTitleWidgetForWeek extends StatefulWidget {
  final DateTime? firstDate;
  final DateTime? lastDate;
  final ValueChanged<DateTime>? onDateChanged;

  const DatePickerTitleWidgetForWeek({
    super.key,
    this.firstDate,
    this.lastDate,
    this.onDateChanged,
  });

  @override
  State<DatePickerTitleWidgetForWeek> createState() => _DatePickerTitleWidgetForWeekState();
}

class _DatePickerTitleWidgetForWeekState extends State<DatePickerTitleWidgetForWeek> {
  void _goToDate(DateTime newDate, ReserveCalendarScreenProvider vm) {
    vm.updateCurrentDateForWeek(newDate);

    vm.setStateWidgetView(Column(
      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.3,
        ),
        EmptyHint(fontSize: 25.sp,hint: '點擊左側課程標籤可顯示更多資訊'),
      ],
    )); //清空右側資訊欄

    widget.onDateChanged?.call(newDate);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        final currentDate = vm.currentDateForWeek;
        final now = DateTime.now();
        final firstDate = widget.firstDate ?? DateTime(now.year - 5, 1, 1);
        final lastDate = widget.lastDate ?? DateTime(now.year + 5, 12, 31);

        final canGoPrev = currentDate.isAfter(firstDate);
        final canGoNext = currentDate.isBefore(lastDate);

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: canGoPrev
                  ? () {
                final newDate = currentDate.subtract(const Duration(days: 1));
                _goToDate(newDate, vm);
              }
                  : null,
            ),
            Text(
              "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')}",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold,color: MyColors.brown_57_54_18),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: canGoNext
                  ? () {
                final newDate = currentDate.add(const Duration(days: 1));
                _goToDate(newDate, vm);
              }
                  : null,
            ),
          ],
        );
      },
    );
  }
}

