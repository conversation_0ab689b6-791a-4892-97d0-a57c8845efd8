
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../../provider/reserve_calendar_provider.dart';
import '../../../resource/MyColor.dart';

class DatePickerTitleWidgetForMonth extends StatefulWidget {
  final DateTime? firstDate;
  final DateTime? lastDate;
  final ValueChanged<DateTime>? onDateChanged;

  const DatePickerTitleWidgetForMonth({
    super.key,
    this.firstDate,
    this.lastDate,
    this.onDateChanged,
  });

  @override
  State<DatePickerTitleWidgetForMonth> createState() => _DatePickerTitleWidgetForMonthState();
}

class _DatePickerTitleWidgetForMonthState extends State<DatePickerTitleWidgetForMonth> {
  void _goToNextMonth(ReserveCalendarScreenProvider vm) {
    final current = vm.currentDateForMonth;
    final nextMonth = DateTime(current.year, current.month + 1, current.day);
    final lastDate = widget.lastDate ?? DateTime(current.year + 5, 12, 31);

    // 確保不超過最後日期
    final newDate = nextMonth.isAfter(lastDate) ? lastDate : nextMonth;
    vm.updateCurrentDateForMonth(newDate);
    widget.onDateChanged?.call(newDate);
  }

  void _goToPrevMonth(ReserveCalendarScreenProvider vm) {
    final current = vm.currentDateForMonth;
    final prevMonth = DateTime(current.year, current.month - 1, current.day);
    final firstDate = widget.firstDate ?? DateTime(current.year - 5, 1, 1);

    // 確保不早於第一日期
    final newDate = prevMonth.isBefore(firstDate) ? firstDate : prevMonth;
    vm.updateCurrentDateForMonth(newDate);
    widget.onDateChanged?.call(newDate);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        final currentDate = vm.currentDateForMonth;
        final now = DateTime.now();
        final firstDate = widget.firstDate ?? DateTime(now.year - 5, 1, 1);
        final lastDate = widget.lastDate ?? DateTime(now.year + 5, 12, 31);

        final canGoPrev = currentDate.isAfter(firstDate);
        final canGoNext = currentDate.isBefore(lastDate);

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: canGoPrev ? () => _goToPrevMonth(vm) : null,
            ),
            Text(
              "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}",
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold,color: MyColors.brown_57_54_18),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: canGoNext ? () => _goToNextMonth(vm) : null,
            ),
          ],
        );
      },
    );
  }
}
