// date_picker_title_for_day.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/widget/hint/empty_hint.dart';
import '../../../../../provider/reserve_calendar_provider.dart';
import '../../../resource/MyColor.dart';

class DatePickerTitleWidgetForDay extends StatelessWidget {
  final DateTime? firstDate;
  final DateTime? lastDate;
  final ValueChanged<DateTime>? onDateChanged;

  const DatePickerTitleWidgetForDay({
    super.key,
    this.firstDate,
    this.lastDate,
    this.onDateChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        final currentDate = vm.currentDateForDay;
        final now = DateTime.now();
        final first = firstDate ?? DateTime(now.year - 5, 1, 1);
        final last = lastDate ?? DateTime(now.year + 5, 12, 31);

        final canPrev = currentDate.isAfter(first);
        final canNext = currentDate.isBefore(last);

        void goToDate(DateTime date) {
          vm.updateCurrentDateForDay(date);
          vm.setCalendarEventDataList(date);
          vm.setStateWidgetView(Column(
            children: [
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.3,
              ),
              EmptyHint(fontSize: 25.sp,hint: '點擊左側課程標籤可顯示更多資訊'),
            ],
          )); //清空右側資訊欄
         onDateChanged?.call(date);
        }

        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.chevron_left),
              onPressed: canPrev
                  ? () => goToDate(currentDate.subtract(const Duration(days: 1)))
                  : null,
            ),
            InkWell(
              onTap: () {},
              child: Text(
                "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')}",
                style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold,color: MyColors.brown_57_54_18),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: canNext
                  ? () => goToDate(currentDate.add(const Duration(days: 1)))
                  : null,
            ),
          ],
        );
      },
    );
  }
}
