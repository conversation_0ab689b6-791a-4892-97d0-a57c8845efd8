import 'dart:math';
import 'package:flutter/material.dart';

class TimetableController {
  TimetableController({
    int initialColumns = 3,
    DateTime? start,
    int? startHour,
    int? endHour,
    double? cellHeight,
    double? headerHeight,
    double? timelineWidth,
    double? cellWidth,
    int? visibleHourCount,
    Function(TimetableControllerEvent)? onEvent,
  }) {
    _columns = initialColumns;
    _start = DateUtils.dateOnly(start ?? DateTime.now());
    _startHour = startHour ?? 0;
    _endHour = endHour ?? 23;
    _cellHeight = cellHeight ?? 50;
    _headerHeight = headerHeight ?? 50;
    _timelineWidth = timelineWidth ?? 50;
    _cellWidth = cellWidth;
    _visibleHourCount = visibleHourCount;
    _visibleDateStart = _start;
    if (onEvent != null) addListener(onEvent);
  }

  // Private Fields
  late DateTime _start;
  late int _startHour;
  late int _endHour;
  int _columns = 3;
  double _cellHeight = 50.0;
  double _headerHeight = 150.0;
  double _timelineWidth = 50.0;
  double? _cellWidth;
  int? _visibleHourCount;
  late DateTime _visibleDateStart;

  final Map<int, Function(TimetableControllerEvent)> _listeners = {};

  // Public Getters / Setters
  DateTime get start => _start;
  set start(DateTime value) {
    _start = DateUtils.dateOnly(value);
    dispatch(TimetableStartChanged(_start));
  }

  int get startHour => _startHour;
  set startHour(int value) {
    assert(value >= 0 && value < 24);
    _startHour = value;
    dispatch(TimetableStartHourChanged(value));
  }

  int get endHour => _endHour;
  set endHour(int value) {
    assert(value >= 0 && value <= 24);
    _endHour = value;
    dispatch(TimetableEndHourChanged(value));
  }

  int get columns => _columns;
  double get cellHeight => _cellHeight;
  double get headerHeight => _headerHeight;
  double get timelineWidth => _timelineWidth;
  double? get cellWidth => _cellWidth;
  set cellWidth(double? value) => _cellWidth = value;

  int? get visibleHourCount => _visibleHourCount;
  set visibleHourCount(int? value) => _visibleHourCount = value;

  DateTime get visibleDateStart => _visibleDateStart;
  bool get hasListeners => _listeners.isNotEmpty;

  // Listener Control
  int addListener(Function(TimetableControllerEvent)? listener) {
    if (listener == null) return -1;
    final id = _listeners.isEmpty ? 0 : _listeners.keys.reduce(max) + 1;
    _listeners[id] = listener;
    return id;
  }

  void removeListener(int id) => _listeners.remove(id);
  void clearListeners() => _listeners.clear();

  void dispatch(TimetableControllerEvent event) {
    for (var listener in _listeners.values) {
      listener(event);
    }
  }

  // External Control Methods
  void jumpTo(DateTime date) {
    dispatch(TimetableJumpToRequested(date));
  }

  void setColumns(int i) {
    if (i == _columns) return;
    _columns = i;
    dispatch(TimetableColumnsChanged(i));
  }

  void setCellHeight(double height) {
    if (height == _cellHeight) return;
    if (height <= 0) return;
    _cellHeight = min(height, 1000);
    dispatch(TimetableCellHeightChanged(height));
  }

  void updateVisibleDate(DateTime date) {
    _visibleDateStart = date;
    dispatch(TimetableVisibleDateChanged(date));
  }
}

// Events
abstract class TimetableControllerEvent {}

class TimetableCellHeightChanged extends TimetableControllerEvent {
  final double height;
  TimetableCellHeightChanged(this.height);
}

class TimetableColumnsChanged extends TimetableControllerEvent {
  final int columns;
  TimetableColumnsChanged(this.columns);
}

class TimetableJumpToRequested extends TimetableControllerEvent {
  final DateTime date;
  TimetableJumpToRequested(this.date);
}

class TimetableStartChanged extends TimetableControllerEvent {
  final DateTime start;
  TimetableStartChanged(this.start);
}

class TimetableStartHourChanged extends TimetableControllerEvent {
  final int startHour;
  TimetableStartHourChanged(this.startHour);
}

class TimetableEndHourChanged extends TimetableControllerEvent {
  final int endHour;
  TimetableEndHourChanged(this.endHour);
}

class TimetableVisibleDateChanged extends TimetableControllerEvent {
  final DateTime start;
  TimetableVisibleDateChanged(this.start);
}