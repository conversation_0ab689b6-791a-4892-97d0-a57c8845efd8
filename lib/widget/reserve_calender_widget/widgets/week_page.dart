import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../../../../model/reserve_calendar_model/models/event.dart';
import '../../../../../../../model/reserve_calendar_model/models/time_point.dart';
import '../../../../../../../provider/reserve_calendar_provider.dart';
import 'date_tile.dart';
import 'event_column.dart';

class WeekPage extends StatefulWidget {
  final ScrollController mainController;
  final double height;
  final double adjustedHeight;
  final Map<DateTime, List<Event>> events;
  final Function(Event)? onEventTap;

  const WeekPage({
    super.key,
    required this.mainController,
    required this.height,
    required this.adjustedHeight,
    required this.events,
    this.onEventTap,
  });

  @override
  State<WeekPage> createState() => _WeekPageState();
}

class _WeekPageState extends State<WeekPage> {

  @override
  void dispose() {
    final calenderVm = context.read<ReserveCalendarScreenProvider>();
    calenderVm.weekScrollController?.dispose();
    super.dispose();
  }

  // 動態生成日期列表，至少包含 currentDate
  List<DateTime> getWeekDates(DateTime start, DateTime current, {int minDays = 7}) {
    List<DateTime> dates = [];
    DateTime monday = start.subtract(Duration(days: start.weekday - 1));

    int extraDays = (current.difference(monday).inDays + 1) - minDays;
    int totalDays = minDays + (extraDays > 0 ? extraDays : 0);

    for (int i = 0; i < totalDays; i++) {
      dates.add(monday.add(Duration(days: i)));
    }
    return dates;
  }

  Map<DateTime, int> _calculateMaxOverlaps(List<DateTime> weekDates, Map<DateTime, List<Event>> events) {
    Map<DateTime, int> maxOverlaps = {};
    for (DateTime date in weekDates) {
      DateTime dateKey = DateTime(date.year, date.month, date.day);
      List<Event> dayEvents = events[dateKey] ?? [];
      int maxCount = _getMaxConcurrentEvents(dayEvents);
      maxOverlaps[date] = maxCount;
    }
    return maxOverlaps;
  }

  int _getMaxConcurrentEvents(List<Event> dayEvents) {
    if (dayEvents.isEmpty) return 0;
    List<TimePoint> timePoints = [];
    for (Event event in dayEvents) {
      timePoints.add(TimePoint(event.start, true));
      timePoints.add(TimePoint(event.end, false));
    }
    timePoints.sort((a, b) {
      int t = a.time.compareTo(b.time);
      if (t != 0) return t;
      return a.isStart ? 1 : -1;
    });
    int currentCount = 0, maxCount = 0;
    for (TimePoint p in timePoints) {
      if (p.isStart) {
        currentCount++;
        maxCount = currentCount > maxCount ? currentCount : maxCount;
      } else {
        currentCount--;
      }
    }
    return maxCount;
  }

  bool _eventsOverlap(Event a, Event b) {
    return a.start.isBefore(b.end) && b.start.isBefore(a.end);
  }

  bool _checkAllEventsOverlap(List<Event> dayEvents) {
    if (dayEvents.length <= 1) return true;
    DateTime earliest = dayEvents.first.start;
    DateTime latest = dayEvents.first.end;
    for (Event e in dayEvents) {
      if (e.start.isBefore(earliest)) earliest = e.start;
      if (e.end.isAfter(latest)) latest = e.end;
    }
    for (Event e in dayEvents) {
      if (!_eventsOverlap(e, Event(earliest, latest, ""))) return false;
    }
    return true;
  }

  // 自動滾動到指定日期
  void scrollToDate(List<DateTime> weekDates, Map<DateTime, double> columnWidths, DateTime target) {
    final calenderVm = context.read<ReserveCalendarScreenProvider>();
    double offset = 0;
    for (DateTime date in weekDates) {
      if (date == target) break;
      offset += columnWidths[date] ?? 0;
    }
    calenderVm.weekScrollController?.animateTo(
      offset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  ScrollController _createSyncedController() {
    final controller = ScrollController(initialScrollOffset: widget.mainController.offset);
    controller.addListener(() {
      if (widget.mainController.hasClients && controller.offset != widget.mainController.offset) {
        widget.mainController.jumpTo(controller.offset);
      }
    });
    widget.mainController.addListener(() {
      if (controller.hasClients && widget.mainController.offset != controller.offset) {
        controller.jumpTo(widget.mainController.offset);
      }
    });
    return controller;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        final calenderVm = context.read<ReserveCalendarScreenProvider>();
        DateTime startDate = vm.weekStartDate;
        DateTime currentDate = vm.currentDateForWeek;

        // 生成日期列表，至少包含當前選中日期
        List<DateTime> weekDates = getWeekDates(startDate, currentDate, minDays: 7);
        Map<DateTime, int> maxOverlaps = _calculateMaxOverlaps(weekDates, widget.events);

        double baseWidth = (MediaQuery.of(context).size.width - 80) / 7;
        double totalWidth = 0;
        Map<DateTime, double> columnWidths = {};

        for (DateTime date in weekDates) {
          int maxOverlap = maxOverlaps[date] ?? 0;
          double colWidth = baseWidth;
          if (maxOverlap >= 2) {
            double eventWidth = baseWidth / 2;
            DateTime dateKey = DateTime(date.year, date.month, date.day);
            List<Event> dayEvents = widget.events[dateKey] ?? [];
            bool allOverlap = _checkAllEventsOverlap(dayEvents);
            if (allOverlap && dayEvents.length <= 2) {
              colWidth = baseWidth;
            } else if (dayEvents.length > 2) {
              colWidth = baseWidth + (eventWidth * (maxOverlap - 2));
            }
          }
          columnWidths[date] = colWidth;
          totalWidth += colWidth;
        }

        return Container(
          color:  Color(0xFFF3F6E4),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: calenderVm.weekScrollController,
            child: SizedBox(
              width: totalWidth,
              child: Column(
                children: [
                  // 日期列
                  Container(
                    height: 60,
                    color: const Color(0xE2F1F7DE),
                    child: Row(
                      children: weekDates.map((date) {
                        return GestureDetector(
                          onTap: () {
                            // 更新 ViewModel
                            vm.updateCurrentDateForWeek(date);
                            // 自動滾動到選中日期
                            scrollToDate(weekDates, columnWidths, date);
                          },
                          child: Container(
                            width: columnWidths[date]!,
                            color: const Color(0xFFF1F7DE),
                            child: DateTile(date: date),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                  Container(height: 1, color: const Color(0xE2F1F7DE)),
                  // 事件列
                  Expanded(
                    child: SingleChildScrollView(
                      controller: _createSyncedController(),
                      child: SizedBox(
                        height: widget.adjustedHeight,
                        child: Row(
                          children: weekDates.map((date) {
                            return SizedBox(
                              width: columnWidths[date]!,
                              child: EventColumn(
                                date: date,
                                height: widget.height,
                                adjustedHeight: widget.adjustedHeight,
                                events: widget.events,
                                columnWidth: columnWidths[date]!,
                                baseWidth: baseWidth,
                                onEventTap: widget.onEventTap,
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}


