import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../../../../../model/reserve_calendar_model/models/event.dart';
import '../../../screen/reserve_calendar/provider/calendar_provider.dart';
import 'hour_column.dart';
import 'week_view.dart';

class WeekCalendarTable extends StatefulWidget {
  final List<Event> events;
  final Function(Event)? onEventTap; // 新增回調參數
  //final int setHours
  final Map<DateTime, List<Event>>? eventsMap; // 新增支援 Map 格式
  final Map<DateTime, List<Event>>? eventsMapWithTime; // 新增支援只有時間的 Map 格式


   const WeekCalendarTable({super.key,
     this.events = const [],
     this.onEventTap,
     this.eventsMap,
     this.eventsMapWithTime,
   });
  //const WeekCalendarTable({super.key});

  @override
  State<WeekCalendarTable> createState() => _WeekCalendarTableState();
}

class _WeekCalendarTableState extends State<WeekCalendarTable> {
  late PageController pageController;
  late ScrollController mainController;
  int pageIndex = 100;//設定初始要顯示的頁面
  double timeHeighPx = 130.0;//設定時間高度間隔
  String focusedMonth = DateFormat.MMMM().format(DateTime.now());

  double get containerHeight => 24 * timeHeighPx;
   int initHour = 9;//初始預設時間

  @override
  void initState() {
    super.initState();
    pageController = PageController(
      keepPage: false,
      initialPage: pageIndex,
      viewportFraction: 1.0,
    );
    mainController = ScrollController(
      initialScrollOffset: initHour * (containerHeight / 24) - 10,
    );


    // 在 initState 中設置事件到 Provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final vm = context.read<CalendarProvider>();

      // 根據優先級設定事件資料
      if (widget.eventsMapWithTime != null) {
        vm.setEventMapWithTime(widget.eventsMapWithTime!); // 需要此方法
      } else if (widget.eventsMap != null) {
        vm.setEventMap(widget.eventsMap!);
      } else {
        vm.setEvent(widget.events);
      }
    });
    // 原本方法
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   final vm = context.read<CalendarProvider>();
    //   vm.setEvent(reserve_calender_widget.events);
    // });
  }

  // @override
  // void didUpdateWidget(WeekCalendarTable oldWidget) {
  //   super.didUpdateWidget(oldWidget);
  //   // 當 reserve_calender_widget.events 改變時，更新 Provider
  //   if (oldWidget.events != reserve_calender_widget.events) {
  //     final vm = context.read<CalendarProvider>();
  //     vm.setEvent(reserve_calender_widget.events);
  //   }
  // }

  @override
  void didUpdateWidget(WeekCalendarTable oldWidget) {
    super.didUpdateWidget(oldWidget);

    //監聽三種資料格式的變化
    if (oldWidget.events != widget.events ||
        oldWidget.eventsMap != widget.eventsMap ||
        oldWidget.eventsMapWithTime != widget.eventsMapWithTime) {
      final vm = context.read<CalendarProvider>();

      //相同的優先級邏輯
      if (widget.eventsMapWithTime != null) {
        vm.setEventMapWithTime(widget.eventsMapWithTime!);
      } else if (widget.eventsMap != null) {
        vm.setEventMap(widget.eventsMap!);
      } else {
        vm.setEvent(widget.events);
      }
    }
  }



  @override
  void dispose() {
    pageController.dispose();
    mainController.dispose();
    super.dispose();
  }

  DateTime getFocusedDate(int index) {
    return DateTime.now().add(Duration(days: (index - 100) * 7));
  }

  void setPageIndex(int index) {
    setState(() {
      pageIndex = index;
      focusedMonth = DateFormat.MMMM().format(getFocusedDate(index));
    });
  }

  void setToday() {
    pageController.animateToPage(
      100,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final events=context.watch<CalendarProvider>().events;

    return Scaffold(
      body: Row(
        children: [
          HourColumn(scrollController: mainController, height: containerHeight),
          Expanded(
            child:

            WeekView(
              pageController: pageController,
              mainController: mainController,
              onPageChanged: setPageIndex,
              height: containerHeight,
              events: events,
              onEventTap: widget.onEventTap,
              //events: events,
            ),
          ),
        ],
      ),
    );
  }
}