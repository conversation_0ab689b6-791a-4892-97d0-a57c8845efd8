import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../../../../widget/hint/empty_hint.dart';
import '../../../../../../../provider/reserve_calendar_provider.dart';

class DateTile extends StatelessWidget {
  final DateTime date;

  const DateTile({super.key, required this.date});

  String _getChineseWeekday(DateTime date) {
    const weekdays = ['週一','週二','週三','週四','週五','週六','週日'];
    return weekdays[date.weekday - 1];
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        bool isSelected = vm.currentDateForWeek.year == date.year &&
            vm.currentDateForWeek.month == date.month &&
            vm.currentDateForWeek.day == date.day;

        Color primary = const Color(0xFF7E8760);
        Color onPrimary = Theme.of(context).colorScheme.onPrimary;
        Color onSurface = const Color(0xFF7E8760);

        return GestureDetector(
          onTap: () {
            vm.updateCurrentDateForWeek(date);
            vm.setStateWidgetView(Column(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.3,
                ),
                EmptyHint(fontSize: 25.sp,hint: '點擊左側課程標籤可顯示更多資訊'),
              ],
            )); //清空右側資訊欄
          },
          child: Container(
            color: Colors.transparent,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _getChineseWeekday(date),
                  style: TextStyle(
                    color: isSelected ? primary : onSurface,
                    fontSize: 18,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                const SizedBox(width: 3),
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(5),
                    color: isSelected ? primary : Colors.transparent,
                  ),
                  child: Center(
                    child: Text(
                      date.day.toString(),
                      style: TextStyle(
                        fontSize: 20,
                        color: isSelected ? onPrimary : onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
