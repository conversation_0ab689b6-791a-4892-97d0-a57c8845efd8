import 'package:flutter/material.dart';
import '../../../../../../../model/reserve_calendar_model/models/event.dart';
import 'week_page.dart';

class WeekView extends StatelessWidget {
  final PageController pageController;
  final ScrollController mainController;
  final Function(int) onPageChanged;
  final double height;
  //final List<Event> events;
  final Map<DateTime,List<Event>> events;
  final Function(Event)? onEventTap; // 新增參數

  const WeekView({
    super.key,
    required this.pageController,
    required this.mainController,
    required this.onPageChanged,
    required this.height,
    //this.events = const [],
    required this.events,
    this.onEventTap
  });

  @override
  Widget build(BuildContext context) {
    const double topPadding = 10.0;
    const double bottomPadding = 10.0;
    final double adjustedHeight = height + topPadding + bottomPadding;

    return PageView.builder(
      physics: const NeverScrollableScrollPhysics(),
      controller: pageController,
      onPageChanged: onPageChanged,
      itemBuilder: (context, index) {
        DateTime startDate = DateTime.now().add(
          Duration(days: (index - 100) * 7),
        );
        return

          WeekPage(
            mainController: mainController,
            height: height,
            adjustedHeight: adjustedHeight,
            events: events,
            onEventTap: onEventTap, // 傳遞給 WeekPage
          );
      },
    );
  }
}