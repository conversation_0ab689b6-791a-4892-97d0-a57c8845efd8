import 'package:flutter/material.dart';
import '../../../../../../../model/reserve_calendar_model/models/event.dart';
import '../../../util/reserve_calender_utils/event_layout.dart';
import 'event_tile.dart';

class EventColumn extends StatelessWidget {
  final DateTime date;
  final double height;
  final double adjustedHeight;
  //final List<Event> events;

  final Map<DateTime,List<Event>> events;

  final double columnWidth;
  final double baseWidth;// 新增參數：基本寬度
  final Function(Event)? onEventTap; // 新增參數

  const EventColumn({
    super.key,
    required this.date,
    required this.height,
    required this.adjustedHeight,
    required this.events,
    required this.columnWidth,
    required this.baseWidth,// 新增參數：基本寬度
    this.onEventTap,
  });

  // List<Event> _getEventsForDay() {
  //   return events.where((event) {
  //     return event.start.year == date.year &&
  //         event.start.month == date.month &&
  //         event.start.day == date.day;
  //   }).toList();
  // }

  List<Event> _getEventsForDay() {

    DateTime dateKey=DateTime(date.year,date.month,date.day);
    return events[dateKey] ?? [];
  }

  bool _eventsOverlap(Event a, Event b) {
    return a.start.isBefore(b.end) && b.start.isBefore(a.end);
  }

  List<EventLayout> _calculateEventLayouts(List<Event> dayEvents) {
    if (dayEvents.isEmpty) return [];

    dayEvents.sort((a, b) {
      int startCompare = a.start.compareTo(b.start);
      if (startCompare != 0) return startCompare;
      return a.end.compareTo(b.end);
    });

    List<EventLayout> layouts = [];
    List<List<Event>> lanes = [];

    for (Event event in dayEvents) {
      int assignedLane = -1;

      for (int i = 0; i < lanes.length; i++) {
        bool canUseLane = true;

        for (Event laneEvent in lanes[i]) {
          if (_eventsOverlap(event, laneEvent)) {
            canUseLane = false;
            break;
          }
        }

        if (canUseLane) {
          assignedLane = i;
          break;
        }
      }

      if (assignedLane == -1) {
        lanes.add([]);
        assignedLane = lanes.length - 1;
      }

      lanes[assignedLane].add(event);
    }

    int totalLanes = lanes.length;
    //double eventWidth = columnWidth / totalLanes;

    double eventWidth;
    if (totalLanes >= 2) {
      // 當有兩個以上同時間事件時，事件寬度變為基本寬度的一半
      eventWidth = baseWidth / 2;
    } else {
      // 單個事件使用完整欄位寬度
      // eventWidth = columnWidth - 4;
      // eventWidth = columnWidth;
      eventWidth = columnWidth-2;
    }

    for (int laneIndex = 0; laneIndex < lanes.length; laneIndex++) {
      for (Event event in lanes[laneIndex]) {
        double leftOffset = laneIndex * eventWidth;
        double adjustedWidth = eventWidth;
        bool isLeftmostEvent=(laneIndex == 0);
        bool isRightmostEvent=(laneIndex==totalLanes-1);

        if(isLeftmostEvent){
          //leftOffset +=1;
          adjustedWidth -=1;
        }
        if(isRightmostEvent && totalLanes >=2){
          adjustedWidth-=1;
        }

        layouts.add(
          EventLayout(
            event: event,
            leftOffset: leftOffset,
             //width: eventWidth - 4,
            // width: eventWidth,
              width: adjustedWidth,
            isLeftmost: isLeftmostEvent,
            isRightmost: isRightmostEvent
          ),
        );
      }
    }

    return layouts;
  }

  @override
  Widget build(BuildContext context) {
    final dayEvents = _getEventsForDay();
    final eventLayouts = _calculateEventLayouts(dayEvents);
    const double topPadding = 10.0;
    final hourHeight = height / 24;

    return Container(
      decoration: BoxDecoration(
        border: Border(
          right: BorderSide(color: const Color(0xFFEDEDEC), width: 1),
        ),
      ),
      child: Container(
        color: const Color(0xFFFCFFF5),//中間事件區顏色
        height: adjustedHeight,
        child: Stack(
          children: [
            for (int i = 0; i < 24; i++)
              Positioned(
                top: topPadding + i * hourHeight,
                left: 0,
                right: 0,
                child: Container(height: 1),
              ),
            ...eventLayouts.map(
              (layout) => _buildEventTile(layout, hourHeight, topPadding),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEventTile(
    EventLayout layout,
    double hourHeight,
    double topPadding,
  ) {
    final startHour =
        layout.event.start.hour + layout.event.start.minute / 60.0;
    final duration =
        layout.event.end.difference(layout.event.start).inMinutes / 60.0;
    final top = topPadding + startHour * hourHeight;
    final eventHeight = duration * hourHeight;

    return Positioned(
      //left: layout.leftOffset + 2,
      left: layout.leftOffset,
      width: layout.width,
      top: top,
      height: eventHeight,
      // child: EventTile(event: layout.event),
      child: EventTile(
        event: layout.event,
        onTap: onEventTap != null
            ? () => onEventTap!(layout.event)
            : null, // 如果有提供 onEventTap，就使用它
      ),
    );
  }
}
