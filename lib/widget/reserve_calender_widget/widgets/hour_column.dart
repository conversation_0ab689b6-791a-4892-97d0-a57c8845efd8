import 'package:flutter/material.dart';

class HourColumn extends StatelessWidget {
  final ScrollController scrollController;
  final double height;

  const HourColumn({
    super.key,
    required this.scrollController,
    required this.height,
  });

  @override
  Widget build(BuildContext context) {
    const double topPadding = 10.0;
    const double bottomPadding = 10.0;
    final double adjustedHeight = height + topPadding + bottomPadding;

    return Row(
      children: [
        Column(
          children: [
            Container(width: 80, height: 60, color: Color(0xE2F1F7DE)),
            Container(width: 80, height: 1, color: const Color(0xFFEDEDEC)),
            Expanded(
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                controller: scrollController,
                child: Container(
                  width: 80,
                  height: adjustedHeight,
                  color: const Color(0xFFFDFFF5),
                  child: Stack(
                    children: [
                      for (int i = 0; i < 24; i++)
                        Positioned(
                          top: topPadding + i * (height / 24) - 8,
                          left: 0,
                          right: 0,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 10),
                            child: Container(
                              alignment: Alignment.centerRight,
                              child: Text(
                                '${i.toString().padLeft(2, '0')}:00',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF848C68),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      for (int hour = 0; hour < 24; hour++)
                        for (int minute in [15, 30, 45])
                          Positioned(
                            top: topPadding +
                                (hour * (height / 24)) +
                                (minute / 60.0) * (height / 24) -
                                6,
                            left: 0,
                            right: 0,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 10),
                              child: Container(
                                alignment: Alignment.centerRight,
                                child: Text(
                                  minute.toString(),
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Color(0xFF7A835C),
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                              ),
                            ),
                          ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        Column(
          children: [
            const SizedBox(height: 60),
            Expanded(child: Container(width: 1, color: const Color(0xFFEDEDEC))),
          ],
        ),
      ],
    );
  }
}