import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import '../../../../../../../model/reserve_calendar_model/models/event.dart';
import '../../../screen/reserve_calendar/provider/calendar_provider.dart';

class EventTile extends StatelessWidget {
  final Event event;
  final VoidCallback? onTap; // 新增 onTap 參數
  static const Color defaultEventColor = Color.fromARGB(255, 255, 255, 255);

  const EventTile({super.key, required this.event,this.onTap});

  String _truncateText(String text, int minChars) {
    if (text.length > minChars) {
      return '${text.substring(0, minChars)}...';
    }
    return text;
  }

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final isPast = event.end.isBefore(now);

    return LayoutBuilder(
      builder: (context, constraints) {
        final isNarrow = constraints.maxWidth < 100;

        return GestureDetector(
          onTap: onTap ?? () {
            // 點擊事件發送資訊到 Provider
            try {
              Provider.of<CalendarProvider>(context, listen: false)
                  .selectEvent(event);

            } catch (e) {
              print('Error selecting event: $e');
            }
          },
          child: Container(
             //margin: const EdgeInsets.symmetric(horizontal: 1),
            margin: EdgeInsets.zero,
            decoration: BoxDecoration(
              color: isPast
                  ? defaultEventColor.withOpacity(0.3)
                  : defaultEventColor.withOpacity(0.7),
              borderRadius: BorderRadius.circular(1),
              border: Border.all(color:  Color.fromARGB(255, 157, 193, 65), width: 0.5),

            ),
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  //mainAxisAlignment: MainAxisAlignment.center,
                  //mainAxisSize: MainAxisSize.min,
                  children: [

                    SizedBox(height: 3,),

                    Text(
                      isNarrow ? _truncateText(event.title, 2) : event.title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF81805D),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                    if (event.member != null && constraints.maxHeight > 60) ...[
                      const SizedBox(height: 5),
                      Text(
                        isNarrow
                            ? _truncateText(event.member!, 3)
                            : event.member!,
                        style: const TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontWeight: FontWeight.bold
                        ),
                        maxLines: isNarrow ? 1 : 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ],
                    if (event.beautician != null &&
                        constraints.maxHeight > 60) ...[
                      const SizedBox(height: 5),
                      Text(
                        isNarrow
                            ? _truncateText(event.beautician!, 3)
                            : event.beautician!,
                        style: const TextStyle(
                          fontSize: 20,
                          color: Color(0xFF383613),
                        ),
                        maxLines: isNarrow ? 1 : 2,
                        overflow: TextOverflow.ellipsis,
                        textAlign: TextAlign.center,
                      ),
                    ],
                    if (constraints.maxHeight > 40) ...[
                      const SizedBox(height: 2),
                      Text(
                        '${DateFormat.Hm().format(event.start)} - ${DateFormat.Hm().format(event.end)}',
                        style: const TextStyle(
                          fontSize: 10,
                          color: Colors.white70,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}