import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import '../state_text_type_widget/calendar_day_event_list.dart';
import '../state_text_type_widget/cours_date_top.dart';

class StateTable extends StatefulWidget {
  const StateTable({super.key});

  @override
  State<StateTable> createState() => _StateTableState();
}

class _StateTableState extends State<StateTable> {
  @override
  Widget build(BuildContext context) {
    return SafeArea(child: _stateTable());
  }

  // 日期時間頂部區塊
  Widget _courseDateTopTime(ReserveCalendarScreenProvider vm) {
    final titleStyle = TextStyle(
      fontSize: 32.sp,
      color: MyColors.green_129_128_94,
    );

    // 取得目前日期
    final currentDate = vm.getCurrentDate();



    // 格式化日期為 YYYY-MM-DD
    final dateText =
        "${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')}";

    // 取得星期文字
    final weekText = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"][currentDate.weekday % 7];

    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            dateText,
            style: titleStyle,
          ),
          SizedBox(height: 6.h),
          Text(
            weekText,
            style: titleStyle,
          ),
        ],
      ),
    );
  }


  Widget _buildContentWidget(ReserveCalendarScreenProvider vm) {
      if (vm.selectCalender == Calendar.month) {

        return Expanded(child: IndexedStack(
          index: vm.CheckEvent? 1:0 ,
          children: [
            CalendarDayEventList(), // index 0
            _calenderReservationFull(vm), // index 1
          ],
        ));
      } else {

        print('datasize: ${vm.getCalendarEventDataSize(vm.selectCalender)}');

        //確認周和日有資料時才會顯示狀態欄
        if(vm.getCalendarEventDataSize(vm.selectCalender)>0){
          return vm.stateWidgetView;
        }else{
          return Container();
        }


        //return vm.stateWidgetView;

      }
    }

  Widget _calenderReservationFull(ReserveCalendarScreenProvider vm) {
    return Column(
      children: [
        SizedBox(
          child: Padding(
            padding: EdgeInsets.all(5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  onPressed: () {
                    vm.setCheckEvent(false);
                    //Navigator.pop(context);

                  },
                  icon: Icon(Icons.chevron_left, size: 50),
                ),

                SizedBox(width: 1,)
              ],
            ),
          ),
        ),
        Expanded(child: CourseDateTop()),
      ],
    );
  }

  Widget _stateTable() {
    return Consumer<ReserveCalendarScreenProvider>(
      builder: (context, vm, _) {
        return ColoredBox(
          color: Color(0xFFF2F6E3),
          child: SizedBox(
            // width: 468.w,
             width: 475.w,
            //width: 500.w,
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 60,bottom: 30,left: 34,right: 34),
                  child: _courseDateTopTime(vm),
                ),
                _buildContentWidget(vm),
              ],
            ),
          ),
        );
      },
    );
  }
}
