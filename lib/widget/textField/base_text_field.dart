import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import 'my_text_field.dart';

class BaseTextField extends StatelessWidget {
  final String hint;
  final TextEditingController controller;
  final  double? w;
  final  double? h;
  final Widget? leadWidget;
  final Widget? tailWidget;
  final VoidCallback? onTap;
  final EdgeInsets? enterTextPadding;
  final EdgeInsets? tailWidgetPadding;
  final EdgeInsets? leadWidgetPadding;
  final bool? isReadOnly;
  final Function(String)? onChanged;
  final bool? isOnlyNum;
  final TextInputType? keyboardType; //鍵盤樣式鎖定
  final List<TextInputFormatter>? inputFormatters; //正則鎖定只能輸入數字用
  final TextAlign? textAlign;
  final Function()? tailWidgetOnTap;
  const BaseTextField({super.key, required this.hint, required this.controller, this.w, this.leadWidget, this.onTap, this.enterTextPadding, this.isReadOnly, this.isOnlyNum, this.tailWidget, this.tailWidgetPadding, this.keyboardType, this.inputFormatters, this.onChanged, this.leadWidgetPadding, this.h, this.textAlign, this.tailWidgetOnTap});

  @override
  Widget build(BuildContext context) {
    return _baseTextField();
  }

  Widget _baseTextField() {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: w ?? 191.w,
        height: h ?? 54.h,
        child: Expanded(
          child: MyTextField(
            tailWidgetOnTap: tailWidgetOnTap,
            onChanged: onChanged,
            keyboardType:keyboardType,
            inputFormatters: inputFormatters,
            tailWidgetPadding: tailWidgetPadding,
            tailWidget: tailWidget,
            isOnlyNum: isOnlyNum,
            isSingleLine: true,
            readOnly: isReadOnly,
            leadWidget: leadWidget,
            leadWidgetPadding: leadWidgetPadding ?? EdgeInsets.only(left: 15.w),
            onTap: onTap,
            style: TextStyle(fontSize: 23.sp, color: MyColors.brown_57_54_18),
            borderColor: MyColors.grey_235_235_235,
            hint: hint,
            textAlign: textAlign ?? TextAlign.start,
            enterTextPadding: enterTextPadding,
            hintStyle: TextStyle(fontSize: 23.sp, color: MyColors.brown_57_54_18),
            controller: controller,
            border: 20.r,
            height: h ?? 54.h,
          ),
        ),
      ),
    );
  }
}
