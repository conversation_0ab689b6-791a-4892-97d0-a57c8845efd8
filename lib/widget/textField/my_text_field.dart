import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../resource/MyColor.dart';

class MyTextField extends StatelessWidget {
  final TextEditingController controller;
  final String? hint;
  final TextStyle? hintStyle;
  final TextStyle? style;
  final bool? textAlignVerticalTop; //是否讓光標從左上開始
  final Function(String)? onChanged;
  final bool? obscureText; //是否屏蔽文字
  final InputDecoration? inputDecoration;
  final TextAlign? textAlign;
  final double height;
  final double border;
  final bool? readOnly;
  final Color? borderColor;
  final Color? underlineColor;
  final Color? focusUnderlineColor;
  final Color? color;
  final VoidCallback? onTap;
  final bool? isSingleLine;
  final int? maxLength; //字數限制
  final bool? isShowTextCount; //是否秀字數限制 ex:(0/max字)
  final bool? isOnlyNum; //只能輸入數字
  final TextInputType? keyboardType; //鍵盤樣式鎖定
  final List<TextInputFormatter>? inputFormatters; //正則鎖定只能輸入數字用
  final EdgeInsetsGeometry? enterTextPadding; //光標與文字的padding
  final EdgeInsetsGeometry? tailWidgetPadding; //後方Widget的padding
  final Key? positionKey;
  final Widget? tailWidget;
  final String? tailWidgetSemantics;
  final VoidCallback? tailWidgetOnTap;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? leadWidgetPadding; //前方Widget的padding
  final Widget? leadWidget;
  final String? leadWidgetSemantics;
  final VoidCallback? leadWidgetOnTap;
  final Function(String)? onSubmitted;
  final double? hintFontSize;

  final List<BoxShadow>? boxShadow; //輸入框陰影

  const MyTextField({
    Key? key,
    required this.controller,
    required this.border,
    required this.height,
    this.hint,
    this.hintStyle,
    this.textAlignVerticalTop,
    this.textAlign,
    this.borderColor,
    this.keyboardType,
    this.inputFormatters,
    this.enterTextPadding,
    this.style,
    this.color,
    this.onChanged,
    this.isShowTextCount,
    this.obscureText,
    this.inputDecoration,
    this.maxLength,
    this.readOnly,
    this.isSingleLine,
    this.positionKey,
    this.tailWidget,
    this.tailWidgetPadding,
    this.underlineColor,
    this.focusUnderlineColor,
    this.tailWidgetOnTap,
    this.tailWidgetSemantics,
    this.leadWidgetPadding,
    this.leadWidget,
    this.leadWidgetSemantics,
    this.leadWidgetOnTap,
    this.boxShadow, this.textInputAction, this.onSubmitted, this.onTap, this.isOnlyNum, this.hintFontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // key: positionKey,
      height: height,
      decoration: BoxDecoration(
        boxShadow: boxShadow,
        color: color ?? Colors.white,
        borderRadius: BorderRadius.circular(border),
        border: Border.all(
          color: borderColor ?? MyColors.grey_219_219_219,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Padding(
            padding: leadWidgetPadding ?? EdgeInsets.zero,
            child: Semantics(
              label: leadWidgetSemantics,
              child: InkWell(
                  onTap: leadWidgetOnTap,
                  child: leadWidget ?? const SizedBox.shrink()),
            ),
          ),
          Flexible(
            child: Padding(
              padding: enterTextPadding ?? EdgeInsets.zero,
              child: Align(
                alignment: Alignment.center,
                child: TextField(
                  keyboardType: keyboardType ??
                      ((isOnlyNum ?? false) ? TextInputType.number : TextInputType.text),
                  inputFormatters: inputFormatters ??
                      ((isOnlyNum ?? false)
                          ? [FilteringTextInputFormatter.digitsOnly]
                          : []),
                  onTap: onTap,
                  obscureText: obscureText ?? false,
                  textInputAction: textInputAction,
                  onSubmitted: onSubmitted,
                  onTapOutside: (focus) {
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                  readOnly: readOnly ?? false,
                  onChanged: onChanged,
                  maxLength: maxLength,
                  // cursorColor: MyColor.blue37_74_125,   /光標顏色
                  maxLines: (isSingleLine ?? false) ? 1 : null,
                  expands: (isSingleLine ?? false) ? false : true,
                  style: style ?? const TextStyle(fontSize: 14),
                  textAlignVertical: textAlignVerticalTop ?? false
                      ? TextAlignVertical.top
                      : TextAlignVertical.center,

                  controller: controller,
                  buildCounter: (BuildContext context,
                      {int? currentLength, int? maxLength, bool? isFocused}) {
                    final isLimit = currentLength == maxLength;
                    return Padding(
                      padding: const EdgeInsets.only(right: 10),
                      child: isShowTextCount ?? false
                          ? Text('$currentLength/$maxLength字',
                              style:  TextStyle(
                                color: isLimit ? MyColors.red_154_0_40 : MyColors.grey_83_83_83,
                              ))
                          : null,
                    );
                  },
                  decoration: inputDecoration ?? InputDecoration(
                    isDense: true,
                    contentPadding: EdgeInsets.zero,
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                          color: focusUnderlineColor ?? Colors.transparent),
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                          color: underlineColor ?? Colors.transparent),
                    ),
                    hintText: hint ?? '',
                    hintStyle: hintStyle ??  TextStyle(fontSize: hintFontSize ?? 23.sp,color: MyColors.grey_140_140_140),
                  ),
                  textAlign: textAlign ?? TextAlign.center,
                ),
              ),
            ),
          ),
          Padding(
            padding: tailWidgetPadding ?? EdgeInsets.zero,
            child: Semantics(
              label: tailWidgetSemantics,
              child: InkWell(
                  onTap: tailWidgetOnTap,
                  child: tailWidget ?? const SizedBox.shrink()),
            ),
          ),
        ],
      ),
    );
  }
}
