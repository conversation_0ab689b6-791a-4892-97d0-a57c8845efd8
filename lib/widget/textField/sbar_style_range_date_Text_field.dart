import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/extension/my_extension.dart';

import '../../gen/r.dart';
import '../../logger/my_print.dart';
import '../../resource/MyColor.dart';
import '../calender/build_range_calender.dart';
import '../calender/build_speed_select_month.dart';
import '../container_with_radius.dart';
import '../divider/custom_vertical_divider.dart';

class SbarStyleRangeDateTextField extends StatefulWidget {
  final DateTime rangeStartDate;
  final DateTime rangeEndDate;
  final String startStr;
  final String endStr;
  final bool isCanSelectAfterThisYear; //是否可選超過今年
  final Function(DateTime start, DateTime end) onRangeSubmit;
  final Function(int year, int month) onSpeedSelectMonthSubmit;

  const SbarStyleRangeDateTextField({
    super.key,
    required this.rangeStartDate,
    required this.rangeEndDate,
    required this.startStr,
    required this.endStr,
    required this.onRangeSubmit,
    required this.onSpeedSelectMonthSubmit,
    required this.isCanSelectAfterThisYear,
  });

  @override
  State<SbarStyleRangeDateTextField> createState() =>
      _SbarStyleRangeDateTextFieldState();
}

class _SbarStyleRangeDateTextFieldState
    extends State<SbarStyleRangeDateTextField> {
  @override
  Widget build(BuildContext context) {
    return _dateTextField();
  }

  ///日期選擇輸入框
  Widget _dateTextField() {
    return ContainerWithRadius(
      w: (451 + 15 + 16).w,
      h: 54.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.w),
        child: Row(
          children: [
            ///開啟日曆（區間）
            InkWell(
              onTap: () {
                buildRangeCalender(
                  context: context,
                  initDate: DateTime.now(),
                  initStartDate: widget.rangeStartDate,
                  initEndDate: widget.rangeEndDate,
                  onSubmit: widget.onRangeSubmit,
                );
              },
              child: Row(
                children: [
                  Image(image: R.image.icon_calendar(),width: 36.w,height: 40.h,),
                  SizedBox(width: 12.w),
                  Text(
                    '${widget.startStr} ~${widget.endStr}',
                    style: TextStyle(
                      fontSize: 23.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(width: 8.5.w),
                ],
              ),
            ),

            ///開啟選取月份
            InkWell(
              onTap: () {
                buildSpeedSelectMonth(
                  context: context,
                  onSubmit: widget.onSpeedSelectMonthSubmit,
                  isCanSelectAfterThisYear: widget.isCanSelectAfterThisYear,
                );
              },
              child: Row(
                children: [
                  CustomVerticalDivider(height: 26.h,color: Colors.black,),
                  SizedBox(width: 12.w),
                  Text(
                    '快選月份',
                    style: TextStyle(
                      fontSize: 23.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
