 import 'package:barcode_scan2/gen/protos/protos.pbenum.dart';
import 'package:barcode_scan2/model/scan_result.dart';
import 'package:barcode_scan2/platform_wrapper.dart';

void runBarCodeScannerCamForOrder() async {
  ScanResult scanResult = await BarcodeScanner.scan();
  if (scanResult.type == ResultType.Barcode) {
    //TODO:掃描後動作
    // log(scanResult.rawContent);
    // await _onBarcodeScannerInput(scanResult.rawContent);
  }
}