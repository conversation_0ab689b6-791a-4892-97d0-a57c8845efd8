import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/btn/base_btn.dart';
import '../../resource/MyColor.dart';
import '../dialog/MyDialog.dart';
import 'my_calendar.dart';

///開啟生日日曆
void buildSingleDateCalender(
    {required BuildContext context,
      required  Function(DateTime, String, Map<DateTime, List<dynamic>>)? onDaySelected,
      required  VoidCallback onSubmit,
      required  DateTime initDate}) {
  MyDialog().customWidgetDialog(
      context,
      true,
      TransitionType.fadeIn,
      Padding(
        padding:  EdgeInsets.symmetric(horizontal: 200.w),
        child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            width: double.infinity,
            child: Padding(
              padding:  EdgeInsets.all(20.r),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  MyCalendar.single(
                      isShowLunar: true,
                      events: const {},
                      calenderSelectedType: CalenderSelectedType.Single,
                      onDaySelected: (selectDate, date, events) {
                        onDaySelected?.call(selectDate,date,events);
                      },
                      isShowTitle: true,
                      onCalendarCreated: (DateTime first, DateTime last,
                          format, DateTime today) {},
                      initDate: initDate),
                   SizedBox(
                    height: 10.h,
                  ),
                  BaseBtn(
                      backgroundColor: MyColors.green_157_193_65.withOpacity(0.8),
                      textColor: MyColors.grey_245_245_245_1,
                      text: '確定',
                      onTap: onSubmit)
                ],
              ),
            )),
      ));
}