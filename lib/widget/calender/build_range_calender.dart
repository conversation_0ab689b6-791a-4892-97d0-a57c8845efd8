import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/btn/base_btn.dart';
import '../../logger/my_print.dart';
import '../../resource/MyColor.dart';
import '../../screen/start/my_app.dart';
import '../dialog/MyDialog.dart';
import 'my_calendar.dart';

void buildRangeCalender({
  required BuildContext context,
  required Function(DateTime start, DateTime end) onSubmit,
  required DateTime initDate,
  required DateTime initStartDate,
  required DateTime initEndDate,
}) {
  DateTime? rangeStart = initStartDate;
  DateTime? rangeEnd = initEndDate;

  MyDialog().customWidgetDialog(
    context,
    true,
    TransitionType.fadeIn,
    StatefulBuilder(
      builder: (context, setStateDialog) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 200.w),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.all(20.r),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  MyCalendar.range(

                    onMultipleDaySelected: (focusDay, selectedDates, events) {
                      myPrint('焦點日期: $focusDay');
                      myPrint('選取的日期區間: $selectedDates');
                      myPrint('事件: $events');

                      if (selectedDates.isNotEmpty) {
                        setStateDialog(() {
                          rangeStart = parseCustomDate(selectedDates.first);
                          rangeEnd = parseCustomDate(selectedDates.last);
                        });
                      }
                    },
                    events: const {},
                    calenderSelectedType: CalenderSelectedType.Range,
                    initRangeStartDay: rangeStart ?? DateTime.now(),
                    initRangeEndDay: rangeEnd ?? DateTime.now(),
                    isShowTitle: true,
                    isShowLunar: true,
                    onCalendarCreated: (a, b, c, d) {},
                    onDaySelected: (selectedDate, _, __) {},
                  ),
                  SizedBox(height: 10.h),
                  BaseBtn(
                    backgroundColor: MyColors.green_157_193_65.withOpacity(0.8),
                    textColor: MyColors.grey_245_245_245_1,
                    text: '確定',
                    onTap: () {
                      if (rangeStart != null && rangeEnd != null) {
                        onSubmit(rangeStart!, rangeEnd!);
                      }
                      myPrint('rangeStart: ${rangeStart}');
                      myPrint('rangeEnd: ${rangeEnd}');
                      navigatorKey.currentState?.pop();
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    ),
  );
}

DateTime parseCustomDate(String str) {
  // str 格式是 '2025.07.15'，自己切割並解析
  var parts = str.split('.');
  if (parts.length != 3) return DateTime.now(); // 錯誤 fallback
  int year = int.parse(parts[0]);
  int month = int.parse(parts[1]);
  int day = int.parse(parts[2]);
  return DateTime(year, month, day);
}
