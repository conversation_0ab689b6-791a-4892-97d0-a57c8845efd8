import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/btn/base_btn.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import '../../resource/MyColor.dart';
import '../../screen/start/my_app.dart';
import '../dialog/MyDialog.dart';
import '../month_square/month_square_screen.dart';
import '../year_select/year_select.dart';

void buildSpeedSelectMonth({
  required BuildContext context,
  required Function(int year,int month) onSubmit,
  required bool isCanSelectAfterThisYear //是否可選超過今年

}) {
  int? selectedIndex; // 儲存選到的 index
  int selectedYear = DateTime.now().year;

  MyDialog().customWidgetDialog(
    context,
    true,
    TransitionType.fadeIn,
    StatefulBuilder(
      builder: (context, setStateDialog) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 200.w),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            width: double.infinity,
            child: Padding(
              padding: EdgeInsets.all(20.r),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  YearSelect( onChanged: (year) {
                    selectedYear = year; // 更新年份
                  }, isCanSelectAfterThisYear: isCanSelectAfterThisYear,),

                  SizedBox(height: 10.h),

                  MonthSquareScreen(
                    onSubmit: (int index) {
                      setStateDialog(() {
                        selectedIndex = index;
                      });
                    },),
                  SizedBox(height: 10.h),
                  BaseBtn(
                    backgroundColor: MyColors.green_157_193_65.withOpacity(0.8),
                    textColor: MyColors.grey_245_245_245_1,
                    text: '確定',
                    onTap: () {
                      if (selectedIndex != null) {
                        onSubmit(selectedYear,selectedIndex! + 1); // +1 => 轉為月份
                        navigatorKey.currentState?.pop();
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      },
    ),
  );
}


