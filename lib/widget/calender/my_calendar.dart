import 'dart:math';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:rxdart/rxdart.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../extension/my_extension.dart';
import '../../logger/my_print.dart';
import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../date_bottom_sheet/date_bottom_sheet.dart';
import '../shape/circle.dart';

///基於Table Calendar封裝的行事曆UI
///支持單選、多選與起迄時間樣式
///包含以下幾種樣式
///1.RightDot：右上方通知點
///2.EventList：下方通知事件點點
///3.Number：右下方通知數量
///
/// 用法：
/// Map<DateTime, List> _events;
///  _events = {
///      StringUtils.stringToDateTime("2020-8-31"): [
///        CalendarEventModel(
///            title: 'Event A5', color: MyColor.loginBlue, isUnavailableDay: true)
///      ],
///      StringUtils.stringToDateTime("2020-9-01"): [
///        CalendarEventModel(
///            title: 'Event A6', color: MyColor.loginBlue, isUnavailableDay: true)
///      ],
///      StringUtils.stringToDateTime("2020-9-03"): [
///        CalendarEventModel(title: 'Event A7', color: MyColor.loginBlue)
///      ],
///      StringUtils.stringToDateTime("2020-9-04"): [
///        CalendarEventModel(
///          title: 'Event A8',
///          color: MyColor.loginBlue,
///        ),
///        CalendarEventModel(
///          title: 'Event A8',
///          color: MyColor.red_210_80_80,
///        )
///      ],
///      StringUtils.stringToDateTime("2020-9-06"): [
///        CalendarEventModel(title: 'Event A9', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A9', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A9', color: MyColor.loginBlue)
///      ],
///      StringUtils.stringToDateTime("2020-9-10"): [
///        CalendarEventModel(title: 'Event A10', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A10', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A10', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A10', color: MyColor.loginBlue)
///      ],
///      StringUtils.stringToDateTime("2020-9-14"): [
///        CalendarEventModel(title: 'Event A11', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A11', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A11', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A11', color: MyColor.loginBlue),
///        CalendarEventModel(title: 'Event A11', color: MyColor.loginBlue)
///      ],
///      StringUtils.stringToDateTime("2020-9-20"): [
///        CalendarEventModel(title: 'Event A12', color: MyColor.loginBlue)
///      ],
///      StringUtils.stringToDateTime("2020-9-25"): [
///        CalendarEventModel(title: 'Event A13', color: MyColor.loginBlue)
///      ],
///      StringUtils.stringToDateTime("2020-9-29"): [
///        CalendarEventModel(title: 'Event A14', color: MyColor.loginBlue)
///      ],
///    };
///
///
///
/// Widget:
///
///Widget _createCalendar() {
///  return MyCalendar(
///    events: _events,
///    textColor: Colors.white,
///    calenderEventType: CalenderEventType.RightDot,
///    calenderSelectedType: CalenderSelectedType.Single,
///    calendarController: _calendarController,
///    onVisibleDaysChanged: (first, last, format) {},
///    isMultiplePick: false,
///    selectColor: Colors.blue,
///    isShowToday: true,
///    isShowEnableIcon: true,
///    onDaySelected: (DateTime date, selectDate, events) {
///     myPrint("@@@onDaySelected: ${date.toString()}");
///    },
///    onMultipleDaySelected: (date, selectDate, event) {
///     myPrint("@@@onMultipleDaySelected: ${selectDate.toString()}");
///    },
///  );
///}

enum CalenderEventType { RightDot, EventList, Number, BottomDot }

enum CalenderSelectedType { Single, Range, Multiple }

class MyCalendar extends StatefulWidget {
  final Map<DateTime, List> events; //事件清單
  final bool? isShowTodayHighLight; //是否顯示今日標示(單選預設true,其餘預設false)
  final TextStyle? todayTextStyle; //今日日期字體
  final Color? textColor; //行事曆文字顏色, 預設黑色
  final Color? todayColor; //今天的顏色, 預設Color(0xFF5C6BC0)
  final Decoration? selectedDecoration; //點選到的樣式
  final TextStyle? selectedTextStyle; //點選到的字體樣式
  final CalenderSelectedType calenderSelectedType; //選取到的樣式
  final void Function(DateTime, String, Map<DateTime, List<dynamic>>)?onDaySelected; //日期選取監聽
  final void Function(DateTime, String, Map<DateTime, List<dynamic>>)?
  onLongPressDaySelected; //日期選取監聽(長按)
  final void Function(DateTime, List<String>, Map<DateTime, List<dynamic>>)?
      onMultipleDaySelected; //多選日期選取監聽
  final bool isShowTitle; //是否顯示頂部年份月份
  final CalendarFormat calendarFormat; // 顯示周模式或月模式
  final TextStyle? weekendTextStyle; //假日文字樣式
  final TextStyle? weekdayTextStyle; //週一到週五文字樣式
  final bool isShowLunar; //是否顯示農曆
  final BehaviorSubject<Map<DateTime, List>>? updateEventSc; //監聽是否需要刷新event
  final Function onCalendarCreated; //行事曆建立完成
  final bool Function(DateTime)?  enableDay;
  final DateTime? firstDay;
  final DateTime? lastDay;
  final bool? isShowTodayCircle; //是否顯示今天圈圈
  final Color? rangeHighlightColor;
  final BoxDecoration? rangeStartDecoration;
  final BoxDecoration? rangeEndDecoration;
  final bool isShowOutside; //是否顯示當月之外的日期
  final DateTime initDate; //單選初始
  final List<String> initRangeDate; //多選初始
  final DateTime initRangeStartDay; //區間起始日初始
  final DateTime initRangeEndDay; //區間結束日初始
  final Function(DateTime)? onPageChange;
  final Function(DateTime)? onConfirmDate;

  MyCalendar.single({
    required this.events,
    required this.calenderSelectedType,
    required this.onDaySelected,
    required this.isShowTitle,
    required this.onCalendarCreated,
    required this.initDate,
    this.onLongPressDaySelected,
    this.updateEventSc,
    this.textColor,
    this.todayTextStyle,
    this.todayColor,
    this.weekendTextStyle,
    this.selectedDecoration,
    this.selectedTextStyle,
    this.calendarFormat = CalendarFormat.month,
    this.weekdayTextStyle,
    this.isShowLunar = false,
    this.isShowOutside = false,
    this.enableDay,
    this.firstDay,
    this.lastDay,
    this.isShowTodayCircle = false, this.onPageChange, this.onConfirmDate,
  })  : this.onMultipleDaySelected = null,
        this.isShowTodayHighLight = true,
        this.initRangeDate = [],
        this.initRangeStartDay = DateTime.now(),
        this.initRangeEndDay = DateTime.now(),
        this.rangeEndDecoration = const BoxDecoration(),
        this.rangeStartDecoration = const BoxDecoration(),
        this.rangeHighlightColor = Colors.blue;

  MyCalendar.range({
    required this.events,
    required this.calenderSelectedType,
    required this.onDaySelected,
    required this.isShowTitle,
    required this.onCalendarCreated,
    required this.initRangeEndDay,
    required this.initRangeStartDay,
    this.onLongPressDaySelected,
    this.updateEventSc,
    this.todayTextStyle,
    this.onMultipleDaySelected,
    this.isShowTodayHighLight,
    this.textColor,
    this.todayColor,
    this.selectedDecoration,
    this.selectedTextStyle,
    this.calendarFormat = CalendarFormat.month,
    this.weekdayTextStyle,
    this.weekendTextStyle,
    this.isShowLunar = false,
    this.isShowOutside = false,
    this.enableDay,
    this.firstDay,
    this.lastDay,
    this.rangeHighlightColor,
    this.rangeStartDecoration,
    this.rangeEndDecoration,
    this.isShowTodayCircle = false, this.onPageChange, this.onConfirmDate,
  })  : this.initDate = DateTime.now(),
        this.initRangeDate = [];

  MyCalendar.multiple({
    required this.events,
    required this.calenderSelectedType,
    required this.onDaySelected,
    required this.isShowTitle,
    required this.onCalendarCreated,
    required this.initRangeDate,
    this.onLongPressDaySelected,
    this.updateEventSc,
    this.todayTextStyle,
    this.isShowTodayHighLight,
    this.onMultipleDaySelected,
    this.textColor,
    this.todayColor,
    this.selectedDecoration,
    this.selectedTextStyle,
    this.calendarFormat = CalendarFormat.month,
    this.weekdayTextStyle,
    this.weekendTextStyle,
    this.isShowLunar = false,
    this.isShowOutside = false,
    this.enableDay,
    this.firstDay,
    this.lastDay,
    this.isShowTodayCircle = false, this.onPageChange, this.onConfirmDate,
  })  : this.initRangeStartDay = DateTime.now(),
        this.initRangeEndDay = DateTime.now(),
        this.initDate = DateTime.now(),
        this.rangeEndDecoration = const BoxDecoration(),
        this.rangeStartDecoration = const BoxDecoration(),
        this.rangeHighlightColor = Colors.blue;

  @override
  _MyCalendarState createState() => _MyCalendarState();
}

class _MyCalendarState extends State<MyCalendar> {
  DateTime? _selectedDay;
  String calendarTitle = '';
  bool isMonthFormat = false;
  var _titleSC = BehaviorSubject<String>();
  List<String> selectedDates = [];
  List<DateTime?> selectedDateTime = [];
  DateTime selectedDate = DateTime.now();
  double _fontSize = 15;

  // bool isInStartEndMode = false; //是否在起訖模式中

  CalenderEventType calenderEventType = CalenderEventType.RightDot; //行事曆事件樣式
  CalenderSelectedType calenderSelectedType =
      CalenderSelectedType.Single; //選取到的樣式
  //  CalendarController calendarController; //controller
  // bool isMultiplePick; //是否為多選
  BehaviorSubject<Map<DateTime, List>>? updateEventSc; //監聽是否需要刷新event
  DateTime _focusedDay = DateTime.now();
  DateTime? _rangeEndDay;
  DateTime? _rangeStartDay;

  @override
  void initState() {
    _selectedDay = _focusedDay;
    calenderSelectedType = widget.calenderSelectedType;
    _switchCalendarInit();
    super.initState();
  }

  @override
  void dispose() {
    // calendarController.dispose();
    _titleSC.close();
    updateEventSc?.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        (widget.isShowTitle)
            ? StreamBuilder<String>(
                stream: _titleSC.stream.asBroadcastStream(),
                initialData: calendarTitle,
                builder: (context, snapshot) {
                  final focusY = _focusedDay.year;
                  final focusM = _focusedDay.month;
                  Locale myLocale = Localizations.localeOf(context);
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        InkWell(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8)),
                          onTap: () {
                            _showDateBottomSheet();
                          },
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: Row(
                              children: [
                                Text('$focusM月 $focusY',
                                    style: const TextStyle(
                                        color: MyColors.grey_61_61_61,
                                        fontSize: 17,
                                        fontWeight: FontWeight.w600)),
                                const SizedBox(
                                  width: 5,
                                ),
                                const Icon(Icons.expand_more_rounded,
                                    color: MyColors.brown_57_54_18),
                              ],
                            ),
                          ),
                        ),
                        const Spacer(),
                        Row(
                          children: [
                            InkWell(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(8)),
                              onTap: () {
                                _decrementMonth();
                              },
                              child: const Icon(
                                Icons.arrow_back_ios_rounded,
                                color: MyColors.brown_57_54_18,
                                size: 18,
                              ),
                            ),
                            const SizedBox(
                              width: 28,
                            ),
                            InkWell(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(8)),
                              onTap: () {
                                _incrementMonth();
                              },
                              child: const Icon(
                                Icons.arrow_forward_ios_rounded,
                                color: MyColors.brown_57_54_18,
                                size: 18,
                              ),
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                })
            : Container(),
        _switchCalendar()
      ],
    );
  }

  ///遞迴減一個月份
  void _decrementMonth() {
    setState(() {
      _focusedDay = DateTime(
        _focusedDay.year,
        _focusedDay.month - 1,
        _focusedDay.day,
      );

      if (_focusedDay.month == 0) {
        _focusedDay = DateTime(_focusedDay.year - 1, 12, _focusedDay.day);
      }
    });
  }

  ///遞迴增一個月份
  void _incrementMonth() {
    setState(() {
      _focusedDay = DateTime(
        _focusedDay.year,
        _focusedDay.month + 1,
        _focusedDay.day,
      );

      if (_focusedDay.month == 0) {
        _focusedDay = DateTime(_focusedDay.year + 1, 12, _focusedDay.day);
      }
    });
  }

  ///日期初始事件切換
  void _switchCalendarInit() {
    switch (calenderSelectedType) {
      case CalenderSelectedType.Single:
        _singleCalenderModeInit();

      case CalenderSelectedType.Multiple:
        _multipleCalenderModeInit();

      case CalenderSelectedType.Range:
        _rangeCalenderModeInit();

      default:
        _singleCalenderMode();
    }
  }

  ///單選模式init
  void _singleCalenderModeInit() {
    ///單選init
    // 在初始化時檢查列表是否為空，如果是，則設置當天日期
    if (selectedDateTime.isEmpty) {
      selectedDateTime.add(DateUtils.dateOnly(_focusedDay));
      setState(() {});
    }
    selectedDateTime.first = widget.initDate;
    _focusedDay = widget.initDate;
    setState(() {});
    myPrint('_focusedDay:$_focusedDay');
  }

  ///多選模式init
  void _multipleCalenderModeInit() {
    _focusedDay = widget.initDate;
    selectedDateTime = (widget.initRangeDate)
        .map((date) => DateTime.parse(date.replaceAll('.', '-')))
        .toList();
    setState(() {});
    myPrint('_focusedDay:$_focusedDay');
    myPrint('reserve_calender_widget.init:${widget.initRangeDate}');
  }

  ///區間模式init
  void _rangeCalenderModeInit() {
    _focusedDay = widget.initDate;
    _rangeStartDay = widget.initRangeStartDay;
    _rangeEndDay = widget.initRangeEndDay;
    setState(() {});
  }

  ///單選模式
  Widget _singleCalenderMode() {
    return TableCalendar(
      eventLoader: (day) {
        return widget.events[DateTime(day.year, day.month, day.day)] ?? [];
      },

      ///事件處理
      calendarBuilders: CalendarBuilders(
        markerBuilder: (context, day, focusedDay) {
          final events =
              widget.events[DateTime(day.year, day.month, day.day)] ?? [];


            // 判断选中的日期与事件是否相同
          final isSelectedDay = selectedDate.year == day.year &&
              selectedDate.month == day.month &&
              selectedDate.day == day.day;


          //判斷選取到有事件日期不顯示紅點
          // if (isSelectedDay && events.isNotEmpty) {
          //   return const SizedBox();
          // }


          if (events.isNotEmpty) {
            return const Circle(color: MyColors.red_154_0_40, size: 5);
          }
          return const SizedBox();
        },
        selectedBuilder: (context, day, focusedDay) {
          return _createSelectCircle(
            day,
          );
        },
        todayBuilder: (context, day, focusedDay) {
          return (widget.isShowTodayCircle ?? false)
              ? _todayMod(day)
              : _dayCell(day, false);
        },
        defaultBuilder: (context, day, focusedDay) {
          return _dayCell(day, false);
        },
      ),
      onPageChanged: (c) {
        _focusedDay = c;

        calendarTitle = c.toString();
        myPrint('calendarTitle: $calendarTitle');
        myPrint('c: $c');
        widget.onPageChange?.call(c);
        setState(() {});
      },
      focusedDay: _focusedDay,
      firstDay: widget.firstDay ?? reduceDay(100),
      lastDay: widget.lastDay ?? DateTime(2049),
      enabledDayPredicate:
      widget.enableDay ,
          // == null
          // ? null
          // : (dt) {
          //     return reserve_calender_widget.enableDay?.contains(DateUtils.dateOnly(dt)) ?? true;
          //   },

      rowHeight:
          (widget.calendarFormat == CalendarFormat.month && widget.isShowLunar)
              ? 65
              : 55,

      availableGestures: AvailableGestures.horizontalSwipe,
      headerVisible: false,
      calendarStyle: CalendarStyle(
        defaultTextStyle: widget.weekdayTextStyle ?? const TextStyle(),
        weekendTextStyle: widget.weekendTextStyle ?? const TextStyle(),
        outsideDaysVisible: widget.isShowOutside,
      ),
      //星期面板樣式
      daysOfWeekStyle: DaysOfWeekStyle(
        dowTextFormatter: (date, locale) {
          //myPrint("@@@date: ${date.weekday},  locale: $locale");
          switch (date.weekday) {
            case 7:
              return '日';
            case 1:
              return '一';
            case 2:
              return '二';
            case 3:
              return "三";
            case 4:
              return "四";
            case 5:
              return "五";
            case 6:
              return "六";
            default:
              return "";
          }
        },
        weekdayStyle: const TextStyle(
          color:  Color.fromRGBO(60, 60, 67, 0.3),
          fontSize: 13,
          fontWeight: FontWeight.w600
        ),
        weekendStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            color: Color.fromRGBO(60, 60, 67, 0.3), fontSize: 13),
      ),
      selectedDayPredicate: (date) {
        return selectedDateTime.any((d) => DateUtils.isSameDay(d, date));
      },

        onDayLongPressed:(focusDay, tabDay){
        myPrint('focusDay: $focusDay');
        myPrint('tabDay: $tabDay');

        selectedDateTime = [tabDay];
        selectedDate = tabDay;
        widget.onLongPressDaySelected?.call(tabDay, '', {});
        },

      onDaySelected: (focusDay, tabDay) {
        ///單選邏輯
        selectedDateTime = [tabDay];
        selectedDate = tabDay;

        ///將所選的值拋出(單選)
        widget.onDaySelected?.call(tabDay, '', {});
        setState(() {});
        myPrint('tabDay: $tabDay');
        myPrint('selectedDateTime: $selectedDateTime');
        return;
      },
      onCalendarCreated: (c) {
        //設定title年月
        DateTime thisMonth = _focusedDay;
        String year = DateFormat("yyyy").format(thisMonth);
        String month = DateFormat("MM").format(thisMonth);
        String title;
        Locale myLocale = Localizations.localeOf(context);
        //myPrint("@@@ local: $myLocale");
        if (myLocale.toString() == "zh_TW") {
          title = '$year年$month月';
        } else {
          switch (month) {
            case "01":
              month = '一月';
              break;
            case "02":
              month = '二月';
              break;
            case "03":
              month = '三月';
              break;
            case "04":
              month = '四月';
              break;
            case "05":
              month = '五月';
              break;
            case "06":
              month = '六月';
              break;
            case "07":
              month = '七月';
              break;
            case "08":
              month = '八月';
              break;
            case "09":
              month = '九月';
              break;
            case "10":
              month = '十月';
              break;
            case "11":
              month = '十一月';
              break;
            case "12":
              month = '十二月';
              break;
            default:
              break;
          }
          title = '$month $year';
        }

        //刷新畫面
        calendarTitle = title;
        _titleSC.add(calendarTitle);
      },
    );
  }

  ///多選模式
  Widget _multipleCalenderMode() {
    return TableCalendar(
      eventLoader: (day) {
        return widget.events[DateTime(day.year, day.month, day.day)] ?? [];
      },

      ///事件處理
      calendarBuilders: CalendarBuilders(
        markerBuilder: (context, day, focusedDay) {
          final events =
              widget.events[DateTime(day.year, day.month, day.day)] ?? [];


          // 判断选中的日期与事件是否相同
          final isSelectedDay = selectedDate.year == day.year &&
              selectedDate.month == day.month &&
              selectedDate.day == day.day;


          //判斷選取到有事件日期不顯示紅點
          if (isSelectedDay && events.isNotEmpty) {
            return const SizedBox();
          }


          if (events.isNotEmpty) {
            return const Circle(color: MyColors.red_154_0_40, size: 5);
          }
          return const SizedBox();
        },
        selectedBuilder: (context, day, focusedDay) {
          return _createSelectCircle(
            day,
          );
        },
        todayBuilder: (context, day, focusedDay) {
          return (widget.isShowTodayCircle ?? false)
              ? _todayMod(day)
              : _dayCell(day, false);
        },
        defaultBuilder: (context, day, focusedDay) {
          return _dayCell(day, false);
        },
      ),
      rangeSelectionMode: RangeSelectionMode.toggledOn,
      onPageChanged: (c) {
        _focusedDay = c;

        calendarTitle = c.toString();
        myPrint('calendarTitle: $calendarTitle');
        myPrint('c: $c');
        setState(() {});
      },
      focusedDay: _focusedDay,
      firstDay: widget.firstDay ?? DateTime(1970),
      lastDay: widget.lastDay ?? DateTime(2049),
      enabledDayPredicate:
      widget.enableDay ,
      // reserve_calender_widget.enableDay == null
      //     ? null
      //     : (dt) {
      //         return reserve_calender_widget.enableDay?.contains(DateUtils.dateOnly(dt)) ?? true;
      //       },

      rowHeight: (widget.calendarFormat == CalendarFormat.month) ? 65 : 45,

      availableGestures: AvailableGestures.horizontalSwipe,
      headerVisible: false,
      calendarStyle: CalendarStyle(
        defaultTextStyle: widget.weekdayTextStyle ?? const TextStyle(),
        weekendTextStyle: widget.weekendTextStyle ?? const TextStyle(),
        selectedTextStyle:
            widget.selectedTextStyle ?? const TextStyle(color: Colors.black),
        selectedDecoration: widget.selectedDecoration ??
            const BoxDecoration(
                color: MyColors.red_255_232_234, shape: BoxShape.circle),
        outsideDaysVisible: widget.isShowOutside,
        todayTextStyle: widget.todayTextStyle ?? const TextStyle(),
        todayDecoration: BoxDecoration(
            color: (widget.isShowTodayHighLight ?? false)
                ? widget.todayColor
                : Colors.transparent,
            shape: BoxShape.circle),
      ),
      //星期面板樣式
      daysOfWeekStyle: DaysOfWeekStyle(
        dowTextFormatter: (date, locale) {
          //myPrint("@@@date: ${date.weekday},  locale: $locale");
          switch (date.weekday) {
            case 7:
              return '日';
            case 1:
              return '一';
            case 2:
              return '二';
            case 3:
              return '三';
            case 4:
              return '四';
            case 5:
              return '五';
            case 6:
              return '六';
            default:
              return "";
          }
        },
        weekdayStyle: const TextStyle(
          color: Colors.black,
          fontSize: 14,
        ),
        weekendStyle: const TextStyle(
          color: Colors.black,
          fontSize: 14,
        ),
      ),
      selectedDayPredicate: (date) {
        myPrint('selectedDateTime: $selectedDateTime');
        return selectedDateTime.any((d) => DateUtils.isSameDay(d, date));
      },

      onDaySelected: (focusDay, tabDay) {
        ///多選邏輯
        if (selectedDateTime.any((d) => DateUtils.isSameDay(d, tabDay))) {
          selectedDateTime.removeWhere((d) => DateUtils.isSameDay(d, tabDay));
          myPrint('remove');
        } else {
          selectedDateTime.add(tabDay);
        }

        List<String> selectedDateTimePos = selectedDateTime
            .whereType<DateTime>()
            .map((e) => e.toDateYMDCustomSplitString())
            .toList();

        widget.onMultipleDaySelected
            ?.call(_focusedDay, selectedDateTimePos, widget.events);
        myPrint('selectedDateTimePos: $selectedDateTimePos');
        setState(() {});
      },
      onCalendarCreated: (c) {
        //設定title年月
        DateTime thisMonth = _focusedDay;
        String year = DateFormat("yyyy").format(thisMonth);
        String month = DateFormat("MM").format(thisMonth);
        String title;
        Locale myLocale = Localizations.localeOf(context);
        //myPrint("@@@ local: $myLocale");
        if (myLocale.toString() == "zh_TW") {
          title = '$year年$month月';
        } else {
          switch (month) {
            case "01":
              month = '一月';
              break;
            case "02":
              month = '二月';
              break;
            case "03":
              month = '三月';
              break;
            case "04":
              month = '四月';
              break;
            case "05":
              month = '五月';
              break;
            case "06":
              month = '六月';
              break;
            case "07":
              month = '七月';
              break;
            case "08":
              month = '八月';
              break;
            case "09":
              month = '九月';
              break;
            case "10":
              month = '十月';
              break;
            case "11":
              month = '十一月';
              break;
            case "12":
              month = '十二月';
              break;
            default:
              break;
          }
          title = '$month $year';
        }

        //刷新畫面
        calendarTitle = title;
        _titleSC.add(calendarTitle);
      },
    );
  }

  ///區間模式
  Widget _rangeCalenderMode() {
    return TableCalendar(
      eventLoader: (day) {
        return widget.events[DateTime(day.year, day.month, day.day)] ?? [];
      },

      ///事件處理
      calendarBuilders: CalendarBuilders(
        markerBuilder: (context, day, focusedDay) {
          final events =
              widget.events[DateTime(day.year, day.month, day.day)] ?? [];


          // 判断选中的日期与事件是否相同
          final isSelectedDay = selectedDate.year == day.year &&
              selectedDate.month == day.month &&
              selectedDate.day == day.day;


          //判斷選取到有事件日期不顯示紅點
          if (isSelectedDay && events.isNotEmpty) {
            return const SizedBox();
          }


          if (events.isNotEmpty) {
            return const Circle(color: MyColors.red_154_0_40, size: 5);
          }
          return const SizedBox();
        },
        withinRangeBuilder: (context, day, focusedDay) {
          return _dayCell(day, false);
        },
        todayBuilder: (context, day, focusedDay) {
          return (widget.isShowTodayCircle ?? false)
              ? _todayMod(day)
              : _dayCell(day, false);
        },
        rangeStartBuilder: (context, day, focusedDay) {
          return _createSelectCircle(
            day,
          );
        },
        rangeEndBuilder: (context, day, focusedDay) {
          return _createSelectCircle(
            day,
          );
        },
        defaultBuilder: (context, day, focusedDay) {
          return _dayCell(day, false);
        },
      ),

      //區間
      rangeEndDay: _rangeEndDay,
      rangeStartDay: _rangeStartDay,
      onRangeSelected: _onRangeSelected,
      rangeSelectionMode: RangeSelectionMode.toggledOn,
      onPageChanged: (c) {
        _focusedDay = c;
        calendarTitle = c.toString();
        myPrint('calendarTitle: $calendarTitle');
        myPrint('c: $c');
        setState(() {});
      },
      focusedDay: _focusedDay,
      firstDay: widget.firstDay ?? DateTime(1970),
      lastDay: widget.lastDay ?? DateTime(2049),
      enabledDayPredicate: widget.enableDay ,

      // reserve_calender_widget.enableDay == null
      //     ? null
      //     : (dt) {
      //         return reserve_calender_widget.enableDay?.contains(DateUtils.dateOnly(dt)) ?? true;
      //       },

      rowHeight: (widget.calendarFormat == CalendarFormat.month) ? 65 : 45,

      availableGestures: AvailableGestures.horizontalSwipe,
      headerVisible: false,
      onDayLongPressed: (date, focusedDay){},
      calendarStyle: CalendarStyle(
        rangeHighlightColor:
            widget.rangeHighlightColor ?? MyColors.red_240_224_229.withOpacity(0.5),
        rangeStartDecoration: widget.rangeStartDecoration ??
            const BoxDecoration(
                color: MyColors.red_240_224_229, shape: BoxShape.circle),
        rangeEndDecoration: widget.rangeEndDecoration ??
            const BoxDecoration(
                color: MyColors.red_240_224_229, shape: BoxShape.circle),
        defaultTextStyle: widget.weekdayTextStyle ?? const TextStyle(),
        weekendTextStyle: widget.weekendTextStyle ?? const TextStyle(),
        selectedTextStyle:
            widget.selectedTextStyle ?? const TextStyle(color: Colors.black),
        selectedDecoration: widget.selectedDecoration ??
            const  BoxDecoration(
                color: MyColors.red_255_232_234, shape: BoxShape.circle),
        outsideDaysVisible: widget.isShowOutside,
        todayTextStyle: widget.todayTextStyle ?? const TextStyle(),
        todayDecoration: BoxDecoration(
            color: (widget.isShowTodayHighLight ?? false)
                ? widget.todayColor
                : Colors.transparent,
            shape: BoxShape.circle),
      ),
      //星期面板樣式
      daysOfWeekStyle: DaysOfWeekStyle(
        dowTextFormatter: (date, locale) {
          //myPrint("@@@date: ${date.weekday},  locale: $locale");
          switch (date.weekday) {
            case 7:
              return '日';
            case 1:
              return '一';
            case 2:
              return '二';
            case 3:
              return '三';
            case 4:
              return '四';
            case 5:
              return '五';
            case 6:
              return '六';
            default:
              return "";
          }
        },
        weekdayStyle: const TextStyle(
          color: Colors.black,
          fontSize: 14,
        ),
        weekendStyle: const TextStyle(
          color: Colors.black,
          fontSize: 14,
        ),
      ),
      selectedDayPredicate: (date) {
        myPrint('selectedDateTime: $selectedDateTime');
        return selectedDateTime.any((d) => DateUtils.isSameDay(d, date));
      },

      onDaySelected: (focusDay, tabDay) {
        ///多選邏輯
        selectedDateTime.add(tabDay);

        List<String> selectedDateTimePos = selectedDateTime
            .whereType<DateTime>()
            .map((e) => e.toDateYMDCustomSplitString())
            .toList();
        widget.onMultipleDaySelected
            ?.call(_focusedDay, selectedDateTimePos, widget.events);
        myPrint('selectedDateTimePos: $selectedDateTimePos');
        setState(() {});
      },
      onCalendarCreated: (c) {
        //設定title年月
        DateTime thisMonth = _focusedDay;
        String year = DateFormat("yyyy").format(thisMonth);
        String month = DateFormat("MM").format(thisMonth);
        String title;
        Locale myLocale = Localizations.localeOf(context);
        //myPrint("@@@ local: $myLocale");
        if (myLocale.toString() == "zh_TW") {
          title = '$year年$month月';
        } else {
          switch (month) {
            case "01":
              month = '一月';
              break;
            case "02":
              month = '二月';
              break;
            case "03":
              month = '三月';
              break;
            case "04":
              month = '四月';
              break;
            case "05":
              month = '五月';
              break;
            case "06":
              month = '六月';
              break;
            case "07":
              month = '七月';
              break;
            case "08":
              month = '八月';
              break;
            case "09":
              month = '九月';
              break;
            case "10":
              month = '十月';
              break;
            case "11":
              month = '十一月';
              break;
            case "12":
              month = '十二月';
              break;
            default:
              break;
          }
          title = '$month $year';
        }

        //刷新畫面
        calendarTitle = title;
        _titleSC.add(calendarTitle);
      },
    );
  }

  ///日期模式切換
  Widget _switchCalendar() {
    switch (calenderSelectedType) {
      case CalenderSelectedType.Single:
        return _singleCalenderMode();

      case CalenderSelectedType.Range:
        return _rangeCalenderMode();

      case CalenderSelectedType.Multiple:
        return _multipleCalenderMode();

      default:
        return _singleCalenderMode();
    }
  }

  ///日期選擇器
  void _showDateBottomSheet() async {
    final result = await DateBottomSheet.showDateBottomSheet(

      context,
      initialDateTime: _focusedDay,
      onConfirmDate: (d){
        widget.onConfirmDate?.call(d);
      }

    ) ;

    if ( result == null) {

      return ;
    }
    _focusedDay = result ?? DateTime.now();
    selectedDateTime = [result];
    myPrint('selectedDateTime: $selectedDateTime');
    widget.onDaySelected?.call(result, '', {});
    setState(() {});
  }

  ///區間選擇
  void _onRangeSelected(DateTime? start, DateTime? end, DateTime focusDay) {
    setState(() {
      // selectedDateTime = [];
      _focusedDay = focusDay;
      _rangeStartDay = start;
      _rangeEndDay = end;
    });

    if (start == null || end == null) return;

    widget.onMultipleDaySelected
        ?.call(_focusedDay, _getDatesInRange(start, end), widget.events);
    myPrint('rangerange: ${_getDatesInRange(start, end)}');
  }



  ///將選擇的起訖拉出總範圍
  List<String> _getDatesInRange(DateTime start, DateTime end) {
    List<String> dates = [];
    DateTime current = start;

    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      dates.add(current.toDateYMDCustomSplitString());
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }



  // Container _disableCell() {
  //   return Container(
  //     height: double.infinity,
  //     width: double.infinity,
  //     margin: EdgeInsets.all(4),
  //     child: FittedBox(
  //       fit: BoxFit.contain,
  //       child: reserve_calender_widget.disableImg,
  //     ),
  //   );
  // }

  Container _dayCell(DateTime date, bool isUnavailableDay) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${date.day}',
            style: TextStyle(
              fontSize: (widget.isShowLunar) ? 20 : 20,
              color: isUnavailableDay ? MyColors.red_154_0_40 : widget.textColor,
            ),
          ),
          (widget.isShowLunar)
              ? Text(
                  StringUtils.dateNumToLunarChinese(date),
                  style: TextStyle(
                      fontSize: 12,
                      color: isUnavailableDay
                          ? MyColors.red_154_0_40
                          : widget.textColor),
                )
              : Container()
        ],
      ),
    );
  }

  Container _todayMod(
    DateTime date,
  ) {
    return Container(
      width: double.infinity,
      height: double.infinity,

      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '${date.day}',
            style: TextStyle(
                fontSize: (widget.isShowLunar) ? 24 : 24,
                fontWeight: FontWeight.w400,
                color: MyColors.red_154_0_40),
          ),
          (widget.isShowLunar)
              ? Text(
                  StringUtils.dateNumToLunarChinese(date),
                  style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w400,
                      color: MyColors.red_154_0_40),
                )
              : Container()
        ],
      ),
    );
  }

  // LocaleType _getLocale(Locale myLocale) {
  //   return (myLocale.toString() == "zh_TW") ? LocaleType.zh : LocaleType.en;
  // }

  ///建立選取到的圈圈
  Widget _createSelectCircle(
    DateTime date,
  ) {
    final now = DateTime.now();
    final isSelectedToday = date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
    return Container(
      height: double.infinity,
      width: double.infinity,
      decoration: widget.selectedDecoration ??
          const BoxDecoration(
              color: MyColors.red_255_232_234, shape: BoxShape.circle),
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "${date.day}",
              style: TextStyle(
                color: isSelectedToday ? MyColors.red_154_0_40 : MyColors.grey_33_33_33,
                fontWeight: isSelectedToday ? FontWeight.w500 : FontWeight.w400,
                fontSize: (widget.isShowLunar) ? 24 : 24,
              ),
            ),
            (widget.isShowLunar)
                ? Text(StringUtils.dateNumToLunarChinese(date),
                    style: const TextStyle(
                      color: MyColors.red_154_0_40,
                      fontSize: 10,
                    ))
                : Container()
          ],
        ),
      ),
    );
  }

  ///建立膠囊形式的單圈
  Widget _makeRowStyleCircle(DateTime date, bool isUnavailableDay) {
    return Container(
      height: double.infinity,
      width: double.infinity,
      decoration: const BoxDecoration(
        color: Color(0xFF5C6BC0),

        //MyColors.loginBlue,
        shape: BoxShape.circle,
      ),
      margin: const EdgeInsets.only(top: 4, bottom: 4, right: 0, left: 0),
      child: Center(
        child: lunarChineseLayOut(date, isUnavailableDay),
      ),
    );
  }

  ///建立膠囊形式的正方形
  Widget _makeRowStyleSquare(DateTime date, bool isUnavailableDay) {
    return Center(child: LayoutBuilder(
      builder: (context, constraint) {
        final shortestSide = min(constraint.maxHeight, constraint.maxWidth);
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
                width: shortestSide,
                height: shortestSide,
                color: MyColors.loginBlue
                //MyColors.loginBlue.withOpacity(0.5),
                ),
            lunarChineseLayOut(date, isUnavailableDay),
          ],
        );
      },
    ));
  }

  ///建立膠囊型式的頭
  Widget _createRowStyleHead(DateTime date, bool isUnavailableDay) {
    return Center(
      child: LayoutBuilder(builder: (context, constraint) {
        final shortestSide = min(constraint.maxHeight, constraint.maxWidth);
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: shortestSide,
              height: shortestSide,
              decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [
                Colors.white,
                MyColors.loginBlue.withOpacity(0.5),
              ], stops: const [
                0.5,
                0.5
              ])),
              foregroundDecoration: BoxDecoration(
                shape: BoxShape.circle,
                color: MyColors.loginBlue.withOpacity(0.5),
              ),
            ),
            lunarChineseLayOut(date, isUnavailableDay),
          ],
        );
      }),
    );
  }

  ///建立膠囊型式的尾
  Widget _createRowStyleTail(DateTime date, bool isUnavailableDay) {
    return Center(
      child: LayoutBuilder(builder: (context, constraint) {
        final shortestSide = min(constraint.maxHeight, constraint.maxWidth);
        return Stack(
          alignment: Alignment.center,
          children: [
            Container(
              width: shortestSide,
              height: shortestSide,
              decoration: BoxDecoration(
                  gradient: LinearGradient(colors: [
                MyColors.loginBlue.withOpacity(0.5),
                Colors.white
              ], stops: const [
                0.5,
                0.5
              ])),
              foregroundDecoration: BoxDecoration(
                shape: BoxShape.circle,
                color: MyColors.loginBlue.withOpacity(0.5),
              ),
            ),
            lunarChineseLayOut(date, isUnavailableDay),
          ],
        );
      }),
    );
  }

  ///農曆圖層
  Column lunarChineseLayOut(DateTime date, bool isUnavailableDay) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '${date.day}',
          style: TextStyle(
            fontSize: (widget.isShowLunar) ? 18 : 14,
            color: isUnavailableDay ? MyColors.grey_c3c3c3 : widget.textColor,
          ),
        ),
        if (widget.isShowLunar)
          Text(
            StringUtils.dateNumToLunarChinese(date),
            style: TextStyle(
                fontSize: 10,
                color:
                    isUnavailableDay ? MyColors.grey_c3c3c3 : widget.textColor),
          ),
      ],
    );
  }

  ///建立Right dot style.
  Widget _createRightDot(CalendarEventModel calendarModel) {
    return Positioned(
      top: 12,
      right: 12,
      child: Container(
        height: 5.0,
        width: 5.0,
        decoration: BoxDecoration(
          color: (calendarModel.title == 'selected')
              ? Colors.transparent
              : (calendarModel.color == null)
                  ? Colors.red
                  : calendarModel.color,
          //MyColors.loginBlue,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  ///建立Bottom dot style.
  Widget _createBottomDot(CalendarEventModel calendarModel) {
    return Positioned(
      right: 0,
      left: 0,
      bottom: 5,
      child: Container(
        height: 5.0,
        width: 5.0,
        decoration: BoxDecoration(
          color: (calendarModel.title == 'selected')
              ? Colors.transparent
              : (calendarModel.color == null)
                  ? Colors.red
                  : calendarModel.color,
          //MyColors.loginBlue,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  ///右下角event數量style
  Widget _buildEventsMarker(DateTime date, List events) {
    var count = 0;
    events.forEach((element) {
      CalendarEventModel calendarEventModel = element as CalendarEventModel;
      if (!calendarEventModel.isUnavailableDay &&
          calendarEventModel.title != 'selected') count++;
    });
    if (count > 0) {
      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: const BoxDecoration(
          shape: BoxShape.rectangle,
          color: MyColors.red_154_0_40,
        ),
        width: 16.0,
        height: 16.0,
        child: Center(
          child: Text(
            '$count',
            style: const TextStyle().copyWith(
              color: widget.textColor,
              fontSize: 12.0,
            ),
          ),
        ),
      );
    } else {
      return Container();
    }
  }
}

///建立EventList style的row
Widget _createEventList(List<dynamic> value) {
  List<Widget> widgetList = [];
  if (value.length >= 4) {
    for (int i = 0; i < 4; i++) {
      CalendarEventModel calendarModel = value[i] as CalendarEventModel;
      widgetList.add(
        Container(
          height: 5.0,
          width: 5.0,
          margin: const EdgeInsets.all(1.5),
          decoration: BoxDecoration(
            color: (calendarModel.title == 'selected')
                ? Colors.transparent
                : (calendarModel.color == null)
                    ? Colors.red
                    : calendarModel.color,
            //MyColors.loginBlue,
            shape: BoxShape.circle,
          ),
        ),
      );
    }
  } else {
    value.forEach((element) {
      CalendarEventModel calendarModel = element as CalendarEventModel;
      widgetList.add(
        Container(
          margin: const EdgeInsets.all(1.5),
          height: 5.0,
          width: 5.0,
          decoration: BoxDecoration(
            color: (calendarModel.title == 'selected')
                ? Colors.transparent
                : (calendarModel.color == null)
                    ? Colors.red
                    : calendarModel.color,
            //MyColors.loginBlue,
            shape: BoxShape.circle,
          ),
        ),
      );
    });
  }
  return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: widgetList);
}

///清除所有已選
void _clearAllSelect(Map<DateTime, List> events) {
  events.forEach((key, value) {
    value.forEach((element) {
      CalendarEventModel calendarEventModel = element as CalendarEventModel;
      calendarEventModel.isSelected = false;
    });
  });
}

///取得昨天
DateTime _getYesterday(DateTime date) {
  return date.subtract(const Duration(days: 1));
}

///取得明天
DateTime _getTomorrow(DateTime date) {
  return date.subtract(const Duration(days: -1));
}

///將DateTime專成String: yyyy-MM-dd
String _dateTimeToString(DateTime date) {
  return DateFormat('yyyy-MM-dd').format(date);
}

///將字串轉DateTime
DateTime _stringToDateTime(String s) {
  DateFormat dateFormat = DateFormat("yyyy-MM-dd");
  DateTime dateTime = dateFormat.parse(s);
  return dateTime;
}

///判斷年月日是否相同
bool isSameDay(DateTime dt1, DateTime dt2) {
  return (dt1.year == dt2.year) &&
      (dt1.month == dt2.month) &&
      (dt1.day == dt2.day);
}

// bool setIsUnavailableDay(Set<DateTime>? enableDay, DateTime dt) {
//   var result = false;
//   //處理有設定限制日期的case
//   if (enableDay != null) {
//     result = !enableDay.contains(DateUtils.dateOnly(dt));
//   }
//   //myPrint("@@@setIsUnavailableDay: $result");
//   return result;
// }

//

///event物件class
class CalendarEventModel {
  String title; //事件名稱
  Color color; //顏色，右上點、通知點
  bool isSelected; //是否選擇
  bool isUnavailableDay; //是否為不可選
  List<Object>? data; //客製化data list

  CalendarEventModel(
      {required this.title,
      this.color = Colors.red,
      this.isSelected = false,
      this.isUnavailableDay = false,
      this.data});

  @override
  String toString() {
    return '''
      title: $title,
      isSelected: $isSelected,
      isUnavailableDay: $isUnavailableDay
    ''';
  }
}

class CalendarEventModelForDivingRecords {
  String id;
  String title; //事件名稱
  String entryTime;
  String exitTime;
  String divingTime;
  String divingType;
  int maximumDepth;
  bool isLock;
  List<String>? images;

  CalendarEventModelForDivingRecords(
      {required this.title,
      required this.id,
      this.isLock = false,
      this.images,
      required this.entryTime,
      required this.exitTime,
      required this.divingTime,
      required this.divingType,
      required this.maximumDepth});

  @override
  String toString() {
    return 'CalendarEventModelForDivingRecords{title: $title, id: $id, entryTime: $entryTime, exitTime: $exitTime, divingTime: $divingTime, divingType: $divingType, maximumDepth: $maximumDepth, isLock: $isLock}';
  }
}
