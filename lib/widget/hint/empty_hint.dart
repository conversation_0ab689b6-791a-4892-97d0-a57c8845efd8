import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class EmptyHint extends StatelessWidget {
  final String hint;
  final double? fontSize;
  const EmptyHint({super.key, required this.hint, this.fontSize});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text(
        hint,
        style: TextStyle(
          color: MyColors.grey_145_145_145,
          fontSize: fontSize ?? 28.sp,
        ),
      ),
    );
  }

}
