import 'dart:math';

import 'package:flutter/cupertino.dart';

import '../../resource/MyColor.dart';

class Diamond extends StatelessWidget {
  final double? size;
  const Diamond({super.key,  this.size});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: MyColors.red_154_0_40,
      width: size ?? 8,
      height: size ?? 8,
      transform: Matrix4.rotationZ(pi / 4),
    );
  }
}
