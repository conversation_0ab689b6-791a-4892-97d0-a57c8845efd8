import 'package:flutter/cupertino.dart';

class Circle extends StatelessWidget {
  final Color color;
  final double size;
  final Widget? child;
  final List<BoxShadow>? boxShadow;
  const Circle({super.key, required this.color, required this.size, this.child, this.boxShadow,});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        boxShadow: boxShadow,
        color: color,
        shape: BoxShape.circle,
      ),
      child: child,
    );
  }
}
