import 'package:flutter/material.dart';


class StadiumBtn extends StatelessWidget {
  final String semanticsLabel;
  final double? borderRadius;
  final Color? color;
  final String text;
  final VoidCallback onTap;
  final EdgeInsetsGeometry? padding;
  final bool isHaveIcon;
  final Widget? icon;
  final TextStyle? textStyle;

  const StadiumBtn({Key? key, required this.semanticsLabel, this.borderRadius, this.color, required this.text, required this.onTap, this.padding, required this.isHaveIcon, this.icon, this.textStyle}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return   InkWell(
      onTap: onTap,
      child: Semantics(
        label: semanticsLabel,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(borderRadius ?? 53),
            color: color ?? Colors.blue,
          ),
          child: Padding(
            padding: padding ??
                const EdgeInsets.only(top: 10, bottom: 10, left: 25, right: 25),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                ExcludeSemantics(
                  child: Text(
                    text,
                    style: textStyle ?? const TextStyle(
                        fontSize: 21,
                        fontWeight: FontWeight.w700,
                        letterSpacing: 5,
                        color: Colors.white),
                  ),
                ),
                SizedBox(
                  width: isHaveIcon ? 3 : 0,
                ),
                icon ?? const SizedBox.shrink(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}