import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

void showToast(
    {required String msg,
    Toast? toastLength,
    ToastGravity? gravity,
    int? time,
    Color? backgroundColor,
    Color? textColor,
    double? fontSize}) {
  Fluttertoast.showToast(
    msg: msg,
    toastLength: toastLength ?? Toast.LENGTH_SHORT,
    gravity: gravity ?? ToastGravity.BOTTOM,
    timeInSecForIosWeb: time ?? 2,
    backgroundColor: backgroundColor ?? Colors.black,
    textColor: textColor ?? Colors.white,
    fontSize: fontSize ?? 16.0,
  );
}
