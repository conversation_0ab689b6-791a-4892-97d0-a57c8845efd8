import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';

class NumberKeyboard extends StatefulWidget {
  final ValueChanged<String>? onValueChanged;

  const NumberKeyboard({super.key, this.onValueChanged});

  @override
  State<NumberKeyboard> createState() => _NumberKeyboardState();
}

class _NumberKeyboardState extends State<NumberKeyboard> {
  String _inputText = '';

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //7 8 9
        Row(
          children: [
            _baseBlock("7"),
            <PERSON><PERSON><PERSON><PERSON>(width: 20.w),
            _baseBlock("8"),
            SizedBox(width: 20.w),
            _baseBlock("9"),
          ],
        ),
        Sized<PERSON><PERSON>(height: 20.h),

        //4 5 6
        Row(
          children: [
            _baseBlock('4'),
            <PERSON><PERSON><PERSON><PERSON>(width: 20.w),
            _baseBlock("5"),
            <PERSON><PERSON><PERSON><PERSON>(width: 20.w),
            _base<PERSON>lock("6"),
          ],
        ),
        <PERSON><PERSON><PERSON><PERSON>(height: 20.h),

        //1 2 3
        Row(
          children: [
            _baseBlock("1"),
            SizedBox(width: 20.w),
            _baseBlock("2"),
            SizedBox(width: 20.w),
            _baseBlock("3"),
          ],
        ),
        SizedBox(height: 20.h),

        //00 0 x
        Row(
          children: [
            _baseBlock("00"),
            SizedBox(width: 20.w),
            _baseBlock("0"),
            SizedBox(width: 20.w),
            _baseBlock('', Icon(Icons.clear, color: MyColors.green_129_128_94)),
          ],
        ),
      ],
    );
  }

  ///base block
  Widget _baseBlock(String num, [Widget? child]) {
    return ContainerWithRadius(
      //長按X清除全部
      onLongPress: num.isEmpty
          ? () {
             HapticFeedback.mediumImpact();
              setState(() {
                _inputText = '';
              });
              widget.onValueChanged?.call(_inputText);
            }
          : null,

      onTap: () {
        HapticFeedback.mediumImpact();
        setState(() {
          if (num.isNotEmpty) {
            // 只在長度小於 11 時才允許新增
            if (_inputText.length < 11) {
              _inputText += num;
            }
          } else {
            // 當 num 是空字串代表是清除鍵（x）
            if (_inputText.isNotEmpty) {
              _inputText = _inputText.substring(0, _inputText.length - 1);
            }
          }
          widget.onValueChanged?.call(_inputText);
        });
      },
      w: 130.w,
      h: 130.w,
      r: 20.r,
      color: Colors.white,
      boxShadow: [
        BoxShadow(color: Colors.black12, blurRadius: 10, offset: Offset(3, 6)),
      ],
      isHaveBorder: true,
      child: Center(
        child:
            child ??
            Text(
              num,
              style: TextStyle(
                fontSize: 44.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
      ),
    );
  }
}
