import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../resource/MyColor.dart';

class BaseAppBar {
  const BaseAppBar._();

  static AppBar create(BuildContext context,
      {Widget? tailWidget,
      Widget? leaderWidget,
      String? title,
      Color? iconColor,
      PreferredSizeWidget? bottom,
      VoidCallback? onTap,
      bool? isHaveBack,
      Color? backgroundColor}) {
    return AppBar(
      bottom: bottom,
      automaticallyImplyLeading: false,
      titleSpacing: 0,
      title: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            (isHaveBack ?? true)
                ? InkWell(
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                    onTap: onTap ??
                        () {
                          Navigator.of(context).pop();
                        },
                    child: leaderWidget ??
                        Center(
                          child: Transform.translate(
                            offset: const Offset(5, 0),
                            child: Icon(
                              Icons.arrow_back_ios,
                              color: Colors.white,
                              size: 40.w,
                            ),
                          ),
                        ))
                : const Icon(
                    Icons.abc,
                    color: Colors.transparent,
                  ), //填充用

            if ((title ?? '').isNotEmpty)
              //標題
              Text(
                title!,
                style: const TextStyle(
                    fontSize: 17,
                    fontWeight: FontWeight.w400,
                    color: MyColors.grey_61_61_61),
              ),

            //尾部元件
            tailWidget ??
                const Icon(
                  Icons.abc,
                  color: Colors.transparent,
                ), //填充用
          ],
        ),
      ),
      elevation: 0,
      backgroundColor: backgroundColor ?? Colors.transparent,
    );
  }
}
