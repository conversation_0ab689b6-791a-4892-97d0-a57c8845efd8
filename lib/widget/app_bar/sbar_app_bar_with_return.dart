import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../background/base_background.dart';

class SbarAppBarWithReturn extends StatefulWidget {
  final Widget childScreen;
  final String title;
  const SbarAppBarWithReturn({super.key, required this.childScreen, required this.title});

  @override
  State<SbarAppBarWithReturn> createState() => _SbarAppBarWithReturnState();
}

class _SbarAppBarWithReturnState extends State<SbarAppBarWithReturn> {

  @override
  Widget build(BuildContext context) {
    return BaseBackground(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 23.h),
          Padding(
            padding: EdgeInsets.only(left: 16.w),
            child: Row(
              children: [
                _returnBtn(),
                SizedBox(width: 51.w),
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 44.sp,
                    color: MyColors.green_121_131_90,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: widget.childScreen,
          ),
        ],
      ),
    );
  }


  Widget _returnBtn() {
    return InkWell(
      onTap: () => Navigator.of(context).pop(),
      child: Container(
        width: 89.w,
        height: 89.w,
        decoration: BoxDecoration(
          color: MyColors.green_157_193_65,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Center(
          child: Transform.translate(
            offset: const Offset(5, 0),
            child: Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 40.w,
            ),
          ),
        ),
      ),
    );
  }
}
