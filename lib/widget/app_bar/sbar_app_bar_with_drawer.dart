import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/resource/MyColor.dart';

import '../btn/drawer_btn.dart';

class SbarAppBar extends StatelessWidget {
  final String title;
  final VoidCallback drawerOnTap;
  final Widget child;
  final Widget? tailWidget;
  const SbarAppBar({super.key, required this.drawerOnTap, required this.child, required this.title, this.tailWidget});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Positioned.fill(
          top: 0,
          child: child
        ),

        Positioned(
          top: 23.h,
          left: 16.w,
          right: 0,
          child: Row(
            children: [
              DrawerBtn(drawerOnTap:  drawerOnTap,),
              SizedBox(width: 51.w),
              Text(title,style: TextStyle(fontSize: 44.sp,color: MyColors.green_121_131_90),),

             Spacer(),

              tailWidget ?? SizedBox.shrink()
            ],
          ),
        ),
      ],
    );
  }
}
