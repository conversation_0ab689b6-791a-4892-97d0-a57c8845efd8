// import 'dart:io';
// import 'package:dio/dio.dart';
// import 'package:flutter/services.dart';
// // import 'package:image_gallery_saver/image_gallery_saver.dart';
// import 'package:permission_handler/permission_handler.dart';
// import '../../logger/my_print.dart';
// import '../../screen/start/my_app.dart';
// import '../toast/custom_toast.dart';
//
// Future<String> normalDownloadAndSaveImage(String imageUrl, String fileName) async {
//   showLoadingOverlay();
//   try {
//     // 检查和请求存储权限（Android 需要存储权限）
//     if (Platform.isAndroid) {
//       if (!await Permission.storage.request().isGranted) {
//         return '未授予存储权限';
//       }
//     }
//
//     // 使用 Dio 下载图片
//     final response = await Dio().get(
//       imageUrl,
//       options: Options(responseType: ResponseType.bytes), // 以字节形式下载
//     );
//
//     // 确保响应成功
//     if (response.statusCode != 200) {
//       return '图片下载失败: 状态码 ${response.statusCode}';
//     }
//
//     // 将下载的数据转换为 Uint8List
//     final Uint8List imageData = Uint8List.fromList(response.data);
//
//     // 保存图片到相册
//     final result = await ImageGallerySaver.saveImage(
//       imageData,
//       name: fileName, // 指定保存的文件名
//       isReturnImagePathOfIOS: true, // iOS 返回路径
//     );
//
//     // 检查保存结果
//     if (result['isSuccess'] == true) {
//       // iOS 或 Android 路径
//       final savedPath = result['filePath'] ?? '未知路径';
//       dismissLoadingOverlay();
//       showToast(msg: '圖片已儲存至媒體');
//       return '图片保存成功: $savedPath';
//
//     } else {
//       dismissLoadingOverlay();
//       showToast(msg: '圖片儲存失敗，請稍後再試');
//       return '图片保存失败';
//     }
//   } catch (e) {
//     myPrint('下载或保存图片时出错: $e');
//     dismissLoadingOverlay();
//     showToast(msg: '圖片儲存失敗，請稍後再試');
//     return '操作失败: $e';
//   }
// }