// import 'dart:io';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:image_gallery_saver/image_gallery_saver.dart';
// import 'package:long_yan/extension/my_extension.dart';
// import 'package:mime/mime.dart';
// import 'package:dio/dio.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:open_file_plus/open_file_plus.dart';
// import 'package:path/path.dart' as p;
// import 'package:path/path.dart' as path;
// import 'package:image/image.dart' as IMG;
// import 'package:path_provider/path_provider.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:sbar_pos/extension/my_extension.dart';
// import '../../exception/permission_exception.dart';
// import '../../logger/my_print.dart';
// import '../../mixin/callback_utils.dart';
// import '../../resource/MyColor.dart';
// import '../cached_network_image/error_cached_network_image.dart';
// import '../dialog/MyDialog.dart';
// import '../snack_bar/snack_bar_with_download.dart';
//
// void downloadFileWithAnimate(
//   BuildContext outerContext,
//   String url,
//   TapUpDetails details,
//   String fileName,
//   String id, {
//   Widget? icon,
//   bool? isDownloadImage,
// }) {
//   OverlayEntry? oe;
//   oe = OverlayEntry(
//     builder: (context) => Material(
//       type: MaterialType.transparency,
//       child: OverlayAnimation(
//         icon: icon ??
//             ((isDownloadImage == true) // 明确 isDownloadImage 是否为 true
//                 ?
//             ClipRRect(
//                 borderRadius: BorderRadius.circular(10),
//                 child: CachedNetworkImage(
//                   errorWidget: (context, url, error) =>
//                   const ErrorCachedNetworkImage(),
//                   height: 50,
//                   width: double.infinity,
//                   imageUrl: url,
//                   fit: BoxFit.cover,
//                 )):
//            const Icon(
//                     Icons.picture_as_pdf_rounded,
//                     size: 30,
//                     color: MyColors.red_154_0_40,
//                   )),
//         beginX: details.globalPosition.dx,
//         beginY: details.globalPosition.dy,
//         endX: details.globalPosition.dx,
//         endY: 1000,
//         animationCompleted: () async {
//           ///圖片下載
//           if (isDownloadImage ?? false) {
//             downloadAndSaveImage(url, fileName);
//             oe?.remove();
//             if (context.mounted) {
//               ScaffoldMessenger.of(outerContext).showSnackBar(
//                 const SnackBar(
//                   backgroundColor: MyColor.grey_74_74_74,
//                   content: Text(
//                     '下載中...',
//                     style: TextStyle(fontSize: 20),
//                   ),
//                 ),
//               );
//             }
//
//             ///檔案下載
//             if (context.mounted) {
//               downloadFile(
//                 outerContext,
//                 url,
//                 fileName,
//                 isDownloadImage ?? false,
//               );
//             }
//             return;
//           }
//           if (context.mounted) {
//             downloadFile(
//               outerContext,
//               url,
//               fileName,
//               isDownloadImage ?? false,
//             );
//           }
//
//           oe?.remove();
//           if (context.mounted) {
//             ScaffoldMessenger.of(outerContext).showSnackBar(
//               const SnackBar(
//                 backgroundColor: MyColor.grey_74_74_74,
//                 content: Text(
//                   '下載中...',
//                   style: TextStyle(fontSize: 20),
//                 ),
//               ),
//             );
//           }
//         },
//       ),
//     ),
//   );
//   Overlay.of(outerContext).insert(oe);
// }
//
// /// 純粹的download file回傳是否下載成功
// Future<(bool, String?)> pureDownloadFile(String url, String fileName,
//     [String? fileType]) async {
//   // 检查权限
//   final hasPermission = await requestDownloadPermissionIfNeed();
//   if (!hasPermission) {
//     return (false, null);
//   }
//
//   try {
//     Dio dio = Dio();
//
//     // 获取当前时间作为文件前缀
//     String dateTime = DateTime.now().toFormatString('yyyyMMddHHmmss');
//
//     // 获取存储路径
//     Directory? dir;
//     if (Platform.isAndroid) {
//       dir = await getExternalStorageDirectory();
//     } else if (Platform.isIOS) {
//       dir = await getApplicationDocumentsDirectory();
//     } else {
//       throw UnsupportedError('Unsupported platform');
//     }
//
//     // 拼接文件路径
//     String filePath = path.join(
//       dir!.path,
//       fileType == null ? '$dateTime$fileName' : '$dateTime$fileName.$fileType',
//     );
//
//     // 下载文件
//     final response = await dio.download(
//       url,
//       filePath,
//       options: Options(
//         followRedirects: true,
//       ),
//     );
//
//     // 检查 HTTP 状态码
//     if (response.statusCode == 200) {
//       myPrint('Download success: $filePath');
//       return (true, filePath);
//     } else {
//       myPrint('Download failed with status: ${response.statusCode}');
//       return (false, null);
//     }
//   } catch (e) {
//     myPrint('Download error: $e');
//     return (false, null);
//   }
// }
//
//
//
//
//
// ///下載檔案並顯示完成訊息的snakeBar
// Future<void> downloadFile(
//   BuildContext context,
//   String url,
//   String fileName,
//   bool isDownloadImage, {
//   String? fileType,
//   ValueCallback<String>? onSnackBarTap,
// }) async {
//   // 假设 pureDownloadFile 是你实现的一个下载函数，返回一个二元组（success, path）
//   final (success, path) = await pureDownloadFile(url, fileName, fileType);
//
//   // 如果未指定 onSnackBarTap，默认使用此逻辑
//   onSnackBarTap ??= (filePath) {
//     final mimeType = fileType ?? lookupMimeType(filePath); // 检测 MIME 类型
//     final extension = p.extension(filePath).toLowerCase(); // 获取文件扩展名
//
//     if (mimeType?.startsWith('image/') == true ||
//         ['.jpg', '.jpeg', '.png', '.gif'].contains(extension)) {
//       // 如果是图片，直接打开图片
//       OpenFile.open(filePath).catchError((error, stackTrace) {
//         myPrint('openFile error: $error');
//       });
//     } else {
//       // 其他类型文件（如PDF等）
//       OpenFile.open(filePath).catchError((error, stackTrace) {
//         myPrint('openFile error: $error');
//       });
//     }
//   };
//
//   final action = path == null || isDownloadImage
//       ? null
//       : SnackBarAction(
//           label: '查看檔案', onPressed: () => onSnackBarTap?.call(path));
//
//   if (context.mounted) {
//     if (success) {
//       _downloadRes(
//         context,
//         const Icon(
//           Icons.check_circle_outlined,
//           color: Colors.lightGreen,
//           size: 25,
//         ),
//         isDownloadImage ? '圖片已儲存至媒體':'下載完成',
//         action: action,
//       );
//     } else {
//       _downloadRes(
//         context,
//         const Icon(
//           Icons.cancel_outlined,
//           color: MyColors.red_216_34_34,
//           size: 25,
//         ),
//         '下載失敗',
//         action: action,
//       );
//     }
//   }
// }
//
// ///下載圖片並顯示完成訊息的snakeBar
// Future<String> downloadAndSaveImage(String imageUrl, String fileName) async {
//   try {
//     // 检查和请求存储权限（Android 需要存储权限）
//     if (Platform.isAndroid) {
//       if (!await Permission.storage.request().isGranted) {
//         return '未授予存储权限';
//       }
//     }
//
//     // 使用 Dio 下载图片
//     final response = await Dio().get(
//       imageUrl,
//       options: Options(responseType: ResponseType.bytes), // 以字节形式下载
//     );
//
//     // 确保响应成功
//     if (response.statusCode != 200) {
//       return '图片下载失败: 状态码 ${response.statusCode}';
//     }
//
//     // 将下载的数据转换为 Uint8List
//     final Uint8List imageData = Uint8List.fromList(response.data);
//
//     // 保存图片到相册
//     final result = await ImageGallerySaver.saveImage(
//       imageData,
//       name: fileName, // 指定保存的文件名
//       isReturnImagePathOfIOS: true, // iOS 返回路径
//     );
//
//     // 检查保存结果
//     if (result['isSuccess'] == true) {
//       // iOS 或 Android 路径
//       final savedPath = result['filePath'] ?? '未知路径';
//       return '图片保存成功: $savedPath';
//     } else {
//       return '图片保存失败';
//     }
//   } catch (e) {
//     debugPrint('下载或保存图片时出错: $e');
//     return '操作失败: $e';
//   }
// }
//
// ///下載回傳顯示邏輯
// void _downloadRes(BuildContext context, Widget icon, String text,
//     {SnackBarAction? action}) {
//   if (context.mounted) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         backgroundColor: MyColors.grey_74_74_74,
//         content: Row(
//           children: [
//             icon,
//             const SizedBox(
//               width: 5,
//             ),
//             Text(
//               text,
//               style: const TextStyle(fontSize: 16),
//             ),
//           ],
//         ),
//         action: action,
//       ),
//     );
//   }
// }
//
// Future<String> _getFilePath(String filename) async {
//   Directory? dir;
//
//   try {
//     if (Platform.isIOS) {
//       dir = await getApplicationDocumentsDirectory(); // for iOS
//     } else if (Platform.isAndroid) {
//       dir = Directory('/storage/emulated/0/Download/'); // for android
//       if (!await dir.exists()) dir = (await getExternalStorageDirectory())!;
//     } else {
//       throw Error();
//     }
//   } catch (err) {
//     myPrint("Cannot get download folder path $err");
//   }
//
//   String? path = dir?.path;
//
//   /// 如果最後沒有斜線則加上
//   if (path != null && !path.endsWith(Platform.pathSeparator)) {
//     path += Platform.pathSeparator;
//   }
//   return "$path$filename";
// }
//
// // Future<bool> requestDownloadPermissionIfNeed() async {
// //   final result = await Permission.storage.request();
// //   if (result.isGranted) return true;
// //   if (result.isLimited) return true;
// //   throw PermissionException.storage(result);
// // }
//
// void downloadAutoRequest(
//   BuildContext context,
//   String url,
//   String fileName,
//   bool isDownloadImage, {
//   String? fileType,
// }) {
//   requestDownloadPermissionIfNeed().then((value) {
//     SnackBarWithDownload.showSimpleSnackBar(context: context, label: '下載中...');
//     return downloadFile(context, url, fileName, isDownloadImage,
//         fileType: fileType);
//   });
// }
//
// class OverlayAnimation extends StatelessWidget {
//   final double beginX;
//   final double beginY;
//   final double endX;
//   final double endY;
//   final VoidCallback animationCompleted;
//   final Widget? icon;
//
//   const OverlayAnimation({
//     super.key,
//     required this.beginX,
//     required this.beginY,
//     required this.endX,
//     required this.endY,
//     required this.animationCompleted,
//     this.icon,
//   });
//
//   @override
//   Widget build(BuildContext context) {
//     return TweenAnimationBuilder<Offset>(
//       tween:
//           Tween<Offset>(begin: Offset(beginX, beginY), end: Offset(endX, endY)),
//       duration: const Duration(milliseconds: 750),
//       curve: Curves.easeInBack,
//       builder: (context, offset, child) {
//         return Stack(
//           fit: StackFit.expand,
//           children: [
//             Positioned.fromRect(
//               rect: Rect.fromCenter(center: offset, width: 50, height: 50),
//               child: child!,
//             ),
//           ],
//         );
//       },
//       onEnd: animationCompleted,
//       child: Container(
//         decoration: const BoxDecoration(
//             shape: BoxShape.circle, color: MyColor.grey238_238_238),
//         child: Center(
//             child: icon ??
//                 const Icon(
//                   Icons.file_copy_rounded,
//                   color: Colors.white,
//                 )),
//       ),
//     );
//   }
// }
