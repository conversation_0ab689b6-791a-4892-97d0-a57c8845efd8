// import 'package:flutter/cupertino.dart';
// import '../../model/other_model/BaseDialogListModel.dart';
// import '../dialog/MyDialog.dart';
// import 'mormal_save_image.dart';
//
// ///長按儲存邏輯
// Future<void> saveImageFunction(BuildContext context, String url) async {
//   List<BaseDialogListModel> data = [
//     BaseDialogListModel(0, '儲存相片', null),
//   ];
//
//
//
//   BaseDialogListModel? baseDialogListModel =
//   await MyDialog().showAlertWithMultipleDatas(
//     context,
//     false,
//     '',
//     data,
//   );
//
//   //未選擇則返回
//   if (baseDialogListModel?.pos == null) {
//     return;
//   }
//   //儲存相片
//   if (baseDialogListModel?.pos == 0) {
//     if(context.mounted){
//       normalDownloadAndSaveImage(url,'');
//     }
//
//     return;
//   }
//   //選擇大頭照
//   if (baseDialogListModel?.pos == 1) {
//     return;
//   }
//
//
// }