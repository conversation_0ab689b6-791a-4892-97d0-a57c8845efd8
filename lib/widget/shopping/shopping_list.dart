import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/widget/hint/empty_hint.dart';

import '../../model/product/product_model.dart';
import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../btn/btn_with_green_shadow.dart';
import '../container_with_radius.dart';

class ShoppingList extends StatefulWidget {
  final double w;
  final double h;
  final EdgeInsets padding;
  final int? type; //商品類別，暫定(1:商品，2:預約與課程，3:儲值金)

  const ShoppingList({
    super.key,
    required this.w,
    required this.h,
    required this.padding,
    this.type,
  });

  @override
  State<ShoppingList> createState() => _ShoppingListState();
}

class _ShoppingListState extends State<ShoppingList> {
  @override
  Widget build(BuildContext context) {
    return _productList();
  }

  ///商品列表
  Widget _productList() {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.shoppingCarListModel,

      builder: (context, shoppingCarListModel, _) {
        //暫定(1:商品，2:預約與課程，3:儲值金)
        // 根據 type 過濾資料
        final filteredList = widget.type == null
            ? shoppingCarListModel
            : shoppingCarListModel
                  .where((item) => item.type == widget.type)
                  .toList();

        if (filteredList.isEmpty) {
          return EmptyHint(hint: '購物車尚無商品');
        }
        return ListView.separated(
          padding: widget.padding,
          physics: BouncingScrollPhysics(),
          itemCount: filteredList.length,
          itemBuilder: (context, index) {
            final model = filteredList[index];
            return _productListWidget(model);
          },
          separatorBuilder: (context, index) => SizedBox(height: 10.h),
        );
      },
    );
  }

  ///商品列表元件
  Widget _productListWidget(ProductModel model) {
    return ContainerWithRadius(
      isHaveBorder: true,
      w: widget.w,
      h: widget.h,
      r: 20.r,
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Stack(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 25.w, top: 15.h, bottom: 21.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  model.title,
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: MyColors.brown_57_54_18,
                  ),
                ),
                Spacer(),
                Text(
                  '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
                  style: TextStyle(
                    fontSize: 25.sp,
                    color: MyColors.grey_119_119_119,
                  ),
                ),
              ],
            ),
          ),
          Positioned(bottom: 11.h, right: 13.w, child: _addAndReduceBtn(model)),
        ],
      ),
    );
  }

  ///新增減少按鈕
  Widget _addAndReduceBtn(ProductModel model) {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.shoppingCarListModel,
      shouldRebuild: (p, n) => true,
      builder: (context, shoppingCarListModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        return Row(
          children: [
            _baseCountBtn(
              () {
                shoppingCarVm.decrementAmountByProductId(
                  model.productId,
                );
              },
              '-',
              MyColors.brown_197_186_165,
            ),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Text(
                model.selectAmount.toString(),
                style: TextStyle(
                  color: MyColors.green_121_131_90,
                  fontSize: 28.sp,
                ),
              ),
            ),
            _baseCountBtn(
              () {
                shoppingCarVm.incrementAmountByProductId(
                  model.productId,
                );
              },
              '+',
              MyColors.blue121_160_167,
            ),
          ],
        );
      },
    );
  }

  ///
  Widget _baseCountBtn(VoidCallback onTap, String icon, Color color) {
    return BtnWithGreenShadow(
      onTap: onTap,
      child: Center(
        child: Transform.translate(
          offset: Offset(0, -5.h),
          child: Text(
            icon,
            style: TextStyle(
              fontSize: 50.w,
              color: color,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }
}
