// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/cupertino.dart';
// import '../../logger/my_print.dart';
// import '../../reserve_calendar_model/other_model/BaseDialogListModel.dart';
// import '../dialog/MyDialog.dart';
// import '../save_image.dart';
// import 'error_cached_network_image.dart';
//
// class CachedNetworkImageWithSave extends StatefulWidget {
//   final String imageUrl;
//   final List<String> imageModel;
//   final int imageIndex;
//   final Alignment alignment;
//   final bool applySave;
//
//   const CachedNetworkImageWithSave(
//       {super.key,
//       required this.imageUrl,
//       required this.imageModel,
//       required this.imageIndex,
//       required this.alignment,
//       required this.applySave});
//
//   @override
//   State<CachedNetworkImageWithSave> createState() =>
//       _CachedNetworkImageWithSaveState();
// }
//
// class _CachedNetworkImageWithSaveState
//     extends State<CachedNetworkImageWithSave> {
//   @override
//   Widget build(BuildContext context) {
//     return _image();
//   }
//
//   Widget _image() {
//     return CachedNetworkImage(
//       imageUrl: reserve_calender_widget.imageUrl,
//       alignment: reserve_calender_widget.alignment,
//       fit: BoxFit.cover,
//       height: 193,
//       imageBuilder: (context, i) {
//         return GestureDetector(
//             onLongPress:
//                 reserve_calender_widget.applySave ? () => _saveFunction(reserve_calender_widget.imageUrl) : () {},
//             onTap: () {
//               MyDialog()
//                   .showFullScreenImageDialog(
//                       context, reserve_calender_widget.imageModel, reserve_calender_widget.imageIndex,
//                       )
//                   .withHideMusicOverlay(context);
//               myPrint('imageIndex: ${reserve_calender_widget.imageIndex}');
//             },
//             child: Image(image: i));
//       },
//       errorWidget: (context, url, error) => const ErrorCachedNetworkImage(),
//     );
//   }
//
//   ///儲存邏輯
//   void _saveFunction(String image) async {
//     List<BaseDialogListModel> datas = [];
//     datas.add(BaseDialogListModel(0, '儲存圖片', null));
//
//     BaseDialogListModel? baseDialogListModel = await MyDialog()
//         .showAlertWithMultipleDatas(
//           context,
//           false,
//           '',
//           datas,
//         );
//
//     //未選擇則返回
//     if (baseDialogListModel?.pos == null) {
//       return;
//     }
//     //儲存圖片
//     if (baseDialogListModel?.pos == 0) {
//       if (context.mounted) {
//         downloadAndSaveImageToGallery(context, image);
//       }
//
//       return;
//     }
//   }
// }
