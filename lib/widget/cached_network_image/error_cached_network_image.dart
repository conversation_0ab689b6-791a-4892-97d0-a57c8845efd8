import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

import '../../gen/r.dart';
import '../../resource/MyColor.dart';
import '../shape/circle.dart';

// import '../../gen/r.dart';


class ErrorCachedNetworkImage extends StatelessWidget {
  final double? h;
  final double? w;
  final double? borderRadius;
  const ErrorCachedNetworkImage({super.key, this.h, this.w, this.borderRadius});

  @override
  Widget build(BuildContext context) {

    return ClipRRect(
      borderRadius:  BorderRadius.all(Radius.circular(borderRadius ?? 0)),
      child:
      Image(image: R.image.icon_default_avatar(),width: 140.w,height: 140.w,   fit: BoxFit.cover,),

    );
  }
}
