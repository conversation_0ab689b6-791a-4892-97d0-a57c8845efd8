import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import '../../resource/MyColor.dart';
import '../stadium_btn.dart';

class DateBottomSheet extends StatefulWidget {
  final DateTime? initialDateTime;
  final DateTime? initialEndDateTime;
  final bool isRange;
  final Color? btnColor;
  final String? btnText;
  final void Function(DateTime)? onDateTimeChanged;
  final void Function(DateTime)? onEndDateTimeChanged;
  final void Function(List<DateTime>?)? onConfirmDate;
  final DateTime? minimumDate;
  final DateTime? maximumDate;

  const DateBottomSheet({
    required this.initialDateTime,
    required this.isRange,
    this.onDateTimeChanged,
    this.onConfirmDate,
    this.initialEndDateTime,
    this.onEndDateTimeChanged,
    this.minimumDate,
    this.maximumDate,
    super.key,
    this.btnColor,
    this.btnText,
  });

  ///單一
  static Future<DateTime?> showDateBottomSheet(
    BuildContext context, {
    DateTime? initialDateTime,
    DateTime? minimumDate,
    DateTime? maximumDate,
    void Function(DateTime)? onDateTimeChanged,
        void Function(DateTime)? onConfirmDate,
  }) async {
    return await showModalBottomSheet(
      isScrollControlled: true,
      constraints: const BoxConstraints(
        maxHeight: 300,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(17),
          topRight: Radius.circular(17),
        ),
      ),
      context: context,
      builder: (context) => DateBottomSheet(
        minimumDate: minimumDate ??
            DateTime.now().subtract(const Duration(days: 365 * 100)),
        maximumDate: maximumDate,
        initialDateTime: initialDateTime,
        onDateTimeChanged: onDateTimeChanged,
        isRange: false,
        onConfirmDate: (date) {
          Navigator.of(context).pop(date?.firstOrNull);
          onConfirmDate?.call((date ?? []).firstOrNull!);
        },
      ),
    );
  }

  ///區間
  static Future<List<DateTime?>> showRangeDateBottomSheet(
    BuildContext context, {
    DateTime? initialDateTime,
    DateTime? initialEndDateTime,
    void Function(DateTime)? onDateTimeChanged,
  }) async {
    return await showModalBottomSheet(
      isScrollControlled: true,
      constraints: const BoxConstraints(
        maxHeight: 500,
      ),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(17),
          topRight: Radius.circular(17),
        ),
      ),
      context: context,
      builder: (context) => DateBottomSheet(
        initialDateTime: initialDateTime,
        initialEndDateTime: initialEndDateTime,
        onDateTimeChanged: onDateTimeChanged,
        isRange: true,
        onConfirmDate: (date) => Navigator.of(context).pop((date)),
      ),
    );
  }

  @override
  State<DateBottomSheet> createState() => _DateBottomSheetState();
}

class _DateBottomSheetState extends State<DateBottomSheet> {
  final DateTime minDate = DateTime(2023);
  late final DateTime initialDateTime;
  late final DateTime initialEndDateTime;
  late DateTime selectedDate;
  late DateTime selectedEndDate;

  @override
  void initState() {
    super.initState();
    initialDateTime =
        (widget.initialDateTime ?? DateTime.now()).clamp(min: minDate);
    selectedDate = initialDateTime;
    initialEndDateTime =
        (widget.initialEndDateTime ?? DateTime.now()).clamp(min: minDate);
    selectedEndDate = initialEndDateTime;
  }

  @override
  Widget build(BuildContext context) {
    return widget.isRange ? _rangeDatePickerUI() : _singleDatePickerUI();
  }

  ///單一日期選擇器UI
  Widget _singleDatePickerUI() {
    return Column(

      children: [
        SizedBox(
          height: MediaQuery.of(context).size.height * 0.05, // 根據螢幕高度調整
        ),
        SizedBox(
          height: 170,
          child: CupertinoDatePicker(
            minimumDate: widget.minimumDate ?? DateTime(2023),
            maximumDate: widget.maximumDate,
            initialDateTime: initialDateTime,
            mode: CupertinoDatePickerMode.date,
            onDateTimeChanged: onDateTimeChange,
          ),
        ),
        // const Spacer(),
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: StadiumBtn(
            color: widget.btnColor ?? MyColors.green_157_193_65,
            semanticsLabel: widget.btnText ?? '確定',
            text: widget.btnText ?? '確定',
            onTap: onSubmitTap,
            isHaveIcon: false,
          ),
        )
      ],
    );
  }

  ///區間日期選擇器UI
  Widget _rangeDatePickerUI() {
    DateTime now = DateTime.now();
    return Column(
      children: [
        const SizedBox(
          height: 10,
        ),
        //起始日期
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Padding(
              padding: EdgeInsets.only(top: 10, left: 10),
              child: Text('起始日期'),
            ),
            Expanded(
              child: SizedBox(
                height: 170,
                child: CupertinoDatePicker(
                  minimumDate: DateTime(now.year, now.month, now.day),
                  initialDateTime: initialDateTime,
                  mode: CupertinoDatePickerMode.date,
                  onDateTimeChanged: onDateTimeChange,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 20,
        ),
        //結束日期
        Row(
          children: [
            const Padding(
              padding: EdgeInsets.only(top: 10, left: 10),
              child: Text('結束日期'),
            ),
            Expanded(
              child: SizedBox(
                height: 170,
                child: CupertinoDatePicker(
                  minimumDate: DateTime(2023),
                  initialDateTime: initialEndDateTime,
                  mode: CupertinoDatePickerMode.date,
                  onDateTimeChanged: onEndDateTimeChange,
                ),
              ),
            ),
          ],
        ),
        const Spacer(),

        Padding(
          padding: const EdgeInsets.only(bottom: 40),
          child: StadiumBtn(
            color: widget.btnColor ?? MyColors.green_157_193_65,
            semanticsLabel: widget.btnText ?? '確定',
            text: widget.btnText ?? '確定',
            onTap: onSubmitTap,
            isHaveIcon: false,
          ),
        )
      ],
    );
  }

  void onDateTimeChange(DateTime dateTime) {
    selectedDate = dateTime;
    widget.onDateTimeChanged?.call(selectedDate);
  }

  void onSubmitTap() {
    widget.onConfirmDate?.call(
        widget.isRange ? [selectedDate, selectedEndDate] : [selectedDate]);
  }

  void onEndDateTimeChange(DateTime dateTime) {
    selectedEndDate = dateTime;
    widget.onEndDateTimeChanged?.call(selectedEndDate);
  }
}
