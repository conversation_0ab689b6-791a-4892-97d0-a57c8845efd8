import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../resource/MyColor.dart';
import '../stadium_btn.dart';

class YearPickerBottomSheet extends StatefulWidget {
  final int? initialYear;
  final int startYear;
  final int endYear;
  final Color? btnColor;
  final String? btnText;

  const YearPickerBottomSheet({
    super.key,
    this.initialYear,
    this.startYear = 1900,
    this.endYear = 2100,
    this.btnColor,
    this.btnText,
  });

  static Future<int?> show(BuildContext context, {
    int? initialYear,
    int startYear = 1900,
    int? endYear,
    Color? btnColor,
    String? btnText,
  }) async {
    final result = await showModalBottomSheet<int>(
      context: context,
      isScrollControlled: true,
      constraints: const BoxConstraints(maxHeight: 300),
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(17)),
      ),
      builder: (context) => YearPickerBottomSheet(
        initialYear: initialYear,
        startYear: startYear,
        endYear: endYear ?? DateTime.now().year,
        btnColor: btnColor,
        btnText: btnText,
      ),
    );
    return result;
  }

  @override
  State<YearPickerBottomSheet> createState() => _YearPickerBottomSheetState();
}

class _YearPickerBottomSheetState extends State<YearPickerBottomSheet> {
  late int selectedYear;
  late FixedExtentScrollController scrollController;

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialYear ?? DateTime.now().year;
    final initialIndex = selectedYear - widget.startYear;
    scrollController = FixedExtentScrollController(initialItem: initialIndex);
  }

  @override
  Widget build(BuildContext context) {
    final yearList = List<int>.generate(
        widget.endYear - widget.startYear + 1, (i) => widget.startYear + i);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 20.h),
        SizedBox(
          height: 180.h,
          child: CupertinoPicker(
            scrollController: scrollController,
            itemExtent: 40,
            onSelectedItemChanged: (index) {
              selectedYear = yearList[index];
            },
            children: yearList
                .map((year) => SizedBox(
                width: 200.w,
                  child: Center(
                  child: Text(
                    '$year 年',
                    style: TextStyle(fontSize: 40.sp),
                  )),
                ))
                .toList(),
          ),
        ),
        SizedBox(height: 10.h),
        StadiumBtn(
          color: widget.btnColor ?? MyColors.green_157_193_65,
          semanticsLabel: widget.btnText ?? '確定',
          text: widget.btnText ?? '確定',
          onTap: () => Navigator.pop(context, selectedYear),
          isHaveIcon: false,
        ),
        SizedBox(height: 20.h),
      ],
    );
  }
}
