import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../resource/MyColor.dart';


class MyDropdownButton extends StatefulWidget {
  final List<String> items;
  final String? selectedItem;
  final ValueChanged<String?> onChanged;
  final double? iconPadding;
  final double? menuMaxHeight;
  final Color? borderColor;
  final int? itemLength;
  final Widget? hint;

  const MyDropdownButton(
      {super.key,
      required this.items,
      required this.selectedItem,
      required this.onChanged,
      this.iconPadding,
      this.menuMaxHeight,
      this.borderColor,
      this.itemLength,
        this.hint});

  @override
  _MyDropdownButtonState createState() => _MyDropdownButtonState();
}

class _MyDropdownButtonState extends State<MyDropdownButton> {
  @override
  Widget build(BuildContext context) {
    final iconPadRight = widget.iconPadding ?? 11;
    return Stack(
      children: [
        Container(
          // key: widget.positionKey,
          width: double.infinity,
          height: 44,
          decoration: BoxDecoration(
            border: Border.all(
                color: widget.borderColor ?? MyColors.grey_217_217_217),
            color: Colors.white,
            borderRadius: BorderRadius.circular(5),
          ),
          child: DropdownButton<String>(
            isExpanded: true,
            hint: widget.hint ?? const SizedBox.shrink(),
              icon: const SizedBox.shrink(),
              underline: const SizedBox.shrink(),
              value: widget.selectedItem,
              menuMaxHeight: widget.menuMaxHeight ?? 300,

              selectedItemBuilder: (context) {
                return widget.items.map((item) {
                  return Padding(
                    padding: EdgeInsets.only(left: 15, right: iconPadRight +24),
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        item,
                        style: const TextStyle(fontSize: 14),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  );
                }).toList();
              },

              items: widget.items.map((item) {
                // final truncatedText = item.length > (widget.itemLength ?? 10)
                //     ? "${item.substring(0, (widget.itemLength ?? 10))}..."
                //     : item;
                return DropdownMenuItem<String>(
                  value: item,

                  child: Padding(
                    padding:  const EdgeInsets.only(left: 15),
                    child: Text(
                      item,
                      style: const TextStyle(fontSize: 14),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                );
              }).toList(),
              onChanged: widget.onChanged),
        ),
        Positioned(
          top: 0,
          right: iconPadRight,
          bottom: 0,
          child:  IgnorePointer(
            child: Icon(Icons.keyboard_arrow_down_rounded,size: 16.w,)
          ),
        )
      ],
    );
  }
}




class MyDropdownButtonT<T> extends StatelessWidget {
  final List<T> items;
  final T? selectedItem;
  final ValueChanged<T?>? onChanged;
  final String Function(T) labelSelector;
  final double? iconPadding;
  final double? menuMaxHeight;
  final double? width;
  final int? itemLength;
  final bool isExpanded;
  final Widget? hint;
  final Widget? underline;
  final Widget? tailWidget;
  final Color? iconColor;
  final bool? isReadOnly;
  final EdgeInsetsGeometry? padding;
  final TextStyle? textStyle;


  const MyDropdownButtonT({
    super.key,
    required this.items,
    required this.selectedItem,
    this.onChanged,
    this.iconPadding,
    this.menuMaxHeight,
    this.itemLength,
    this.hint,
    required this.labelSelector,
    this.iconColor,
    this.underline, required this.isExpanded, this.isReadOnly, this.padding, this.textStyle, this.tailWidget, this.width,
  });

  @override
  Widget build(BuildContext context) {
    final iconPadRight = iconPadding ?? 11;

   return Stack(
      children: [
        DropdownButton<T>(
          isExpanded: true,
          value: selectedItem,
          hint: hint ?? const SizedBox.shrink(),
          underline: underline ?? const SizedBox.shrink(),
          borderRadius: const BorderRadius.all(Radius.circular(8)),
          icon: const SizedBox.shrink(),
          menuMaxHeight: menuMaxHeight ?? 300,
          selectedItemBuilder: (context) {
            return items.map((item) {
              return Padding(
                padding: padding ?? EdgeInsets.only(left: 0, right: iconPadRight +24),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    labelSelector.call(item),
                    style: textStyle ?? const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              );
            }).toList();
          },
          items: items.map<DropdownMenuItem<T>>((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: Text(labelSelector(item)),
            );
          }).toList(),
          onChanged: onChanged,
        ),
        Positioned(
          top: 0,
          bottom: 0,
          right: 0,
          child: IgnorePointer(
            child: tailWidget ?? Icon(Icons.keyboard_arrow_down_rounded, size: 16.w),
          ),
        ),
      ],
    );


  }
}
