// import 'dart:convert';
// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:photo_view/photo_view.dart';
// import 'package:photo_view/photo_view_gallery.dart';
// import '../../logger/my_print.dart';
// import '../check_and_execute_image_action/check_and_execute_image_action.dart';
// import '../download/long_press_save_image.dart';
//
//
// class GalleryPhotoViewWrapper extends StatefulWidget {
//   final LoadingBuilder? loadingBuilder;
//   final BoxDecoration? backgroundDecoration;
//   final dynamic minScale;
//   final dynamic maxScale;
//   final int initialIndex;
//   final PageController pageController;
//   final List<String> galleryUrls;
//   final Axis scrollDirection;
//   final bool isBase64;
//   final bool? isHaveLengthHint;
//
//   GalleryPhotoViewWrapper({
//     Key? key,
//     this.loadingBuilder,
//     this.backgroundDecoration,
//     this.minScale,
//     this.maxScale,
//     this.initialIndex = 0,
//     this.scrollDirection = Axis.horizontal,
//     required this.galleryUrls,
//     required this.isBase64,
//     // required this.applySave,
//     this.isHaveLengthHint,
//     // required this.scaleStateController,
//   })  : pageController = PageController(initialPage: initialIndex),
//         super(key: key);
//
//   @override
//   State<StatefulWidget> createState() {
//     return _GalleryPhotoViewWrapperState();
//   }
// }
//
// class _GalleryPhotoViewWrapperState extends State<GalleryPhotoViewWrapper> {
//   int currentIndex = 0;
//
//   // PhotoViewScaleStateController scaleStateController = PhotoViewScaleStateController();
//   double? scaleCopy;
//   late final PhotoViewScaleStateController controller;
//   double initialScale = 1.0;
//
//   @override
//   void initState() {
//     super.initState();
//     currentIndex = widget.initialIndex;
//     controller = PhotoViewScaleStateController();
//
//     // scaleStateController = PhotoViewScaleStateController();
//   }
//
//   void onPageChanged(int index) {
//     setState(() {
//       currentIndex = index;
//     });
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Container(
//         decoration: widget.backgroundDecoration,
//         constraints: BoxConstraints.expand(
//           height: MediaQuery.of(context).size.height,
//         ),
//         child: Stack(
//           alignment: Alignment.bottomRight,
//           children: <Widget>[
//             InkWell(
//               onLongPress: (){
//                 myPrint('onLongPress');
//                 checkAndExecuteImageAction(
//                   context: context,
//                   imageUrl: widget.galleryUrls[currentIndex],
//                   onSuccess: () {
//                     saveImageFunction(context, widget.galleryUrls[currentIndex],);
//                   },
//                 );
//
//               },
//               child: PhotoViewGallery.builder(
//                 scrollPhysics: const BouncingScrollPhysics(),
//                 builder: _buildItem,
//                 gaplessPlayback: true,
//                 itemCount: widget.galleryUrls.length,
//                 loadingBuilder: widget.loadingBuilder,
//                 backgroundDecoration: widget.backgroundDecoration,
//                 pageController: widget.pageController,
//                 onPageChanged: onPageChanged,
//                 scrollDirection: widget.scrollDirection,
//               ),
//             ),
//             Positioned(
//               bottom: 20,
//               right: 20,
//               child: Row(
//                 children: [
//                   // if (widget.applySave)
//                   //   InkWell(
//                   //       onTapUp: (details) {
//                   //         downloadFileWithAnimate(
//                   //             context,
//                   //             widget.galleryUrls[currentIndex],
//                   //             details,
//                   //             '',
//                   //             '',
//                   //         isDownloadImage: true);
//                   //         // downloadAndSaveImageToGallery(
//                   //         //     context, widget.galleryUrls[currentIndex]);
//                   //       },
//                   //       child: const Icon(
//                   //         Icons.file_download_outlined,
//                   //         color: Colors.white,
//                   //       )),
//                   // if (widget.applySave)
//                   //   const SizedBox(
//                   //     width: 16,
//                   //   ),
//                   if (widget.isHaveLengthHint ?? false)
//                     Text(
//                       "${currentIndex + 1} of ${widget.galleryUrls.length}",
//                       style: const TextStyle(
//                         color: Colors.white,
//                         fontSize: 17.0,
//                         decoration: null,
//                       ),
//                     ),
//                 ],
//               ),
//             ),
//             Positioned(
//                 top: 50,
//                 right: 20,
//                 child: InkWell(
//                     onTap: () {
//                       Navigator.of(context).pop();
//                     },
//                     child: const Icon(
//                       Icons.close,
//                       color: Colors.white,
//                       size: 25,
//                       shadows: <Shadow>[
//                         Shadow(color: Colors.black, blurRadius: 10.0)
//                       ],
//                     )))
//           ],
//         ),
//       ),
//     );
//   }
//
//
//   PhotoViewGalleryPageOptions _buildItem(BuildContext context, int index) {
//     final item = widget.galleryUrls[index];
//
//     // 指定型別為 ImageProvider<Object>
//     final ImageProvider<Object> imageProvider = widget.isBase64
//         ? MemoryImage(base64Decode(item)) as ImageProvider<Object>
//         : CachedNetworkImageProvider(item) as ImageProvider<Object>;
//
//     return PhotoViewGalleryPageOptions(
//       imageProvider: imageProvider,
//       minScale: PhotoViewComputedScale.contained,
//       maxScale: PhotoViewComputedScale.covered,
//       initialScale: PhotoViewComputedScale.contained,
//       heroAttributes: widget.isBase64
//           ? null
//           : PhotoViewHeroAttributes(tag: '$item $index'),
//       basePosition: Alignment.center,
//     );
//   }
//
//
//
//
// }
