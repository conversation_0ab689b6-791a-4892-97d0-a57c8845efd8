import 'package:flutter/material.dart';

import '../../model/other_model/BaseDialogListModel.dart';
import '../../resource/MyColor.dart';

///多選picker的子項目
class MultiplePickerListItem extends StatefulWidget {
  final BaseDialogListModel? model;
  VoidCallback? onTap;
  EdgeInsetsGeometry? padding;

  MultiplePickerListItem(
      {Key? key, this.model,
        this.onTap,
        this.padding
      }) : super(key: key);

  @override
  _MultiplePickerListItemState createState() =>
      _MultiplePickerListItemState(model, onTap);
}

class _MultiplePickerListItemState extends State<MultiplePickerListItem> {
  final BaseDialogListModel? model;
  VoidCallback? onTap;

  _MultiplePickerListItemState(this.model, this.onTap);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      onTap: () {
        model?.isCheck ^= true;
        setState(() {});
      },

      title: Padding(
        padding: widget.padding ?? EdgeInsets.only(right: 28,left: 28),
        child: Row(
         mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(model?.title ?? '', style: TextStyle(fontSize: 14)),
            (model?.isCheck == true) ? Icon(Icons.check_circle,color: MyColors.loginBlue,) : Container(width: 0,),
          ]
            ),
      ),



    );
  }
}
