import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../resource/MyColor.dart';
import 'MyDialog.dart';

Future<dynamic> sbarStyleDialog(
    {required BuildContext context,
      required void Function()? yesAction,
      required String title,
      required String content,
      Widget? icon,
      Widget? titleWidget,
      String? confirmTitle,
      bool? barrierDismissible,
      EdgeInsetsGeometry? confirmPadding,
    }) {
  return MyDialog().customWidgetDialog(
    context,
    barrierDismissible ?? true,
    TransitionType.fadeIn,
    Padding(
      padding: const EdgeInsets.symmetric(horizontal: 59),
      child: Container(
        width: 470.w,

        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 10),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              icon ?? const SizedBox.shrink(),


              if (titleWidget != null)
                titleWidget,
              // 如果 title 為空，則顯示空的 SizedBox
              if ((title).isEmpty)
                const SizedBox.shrink(),
              // 如果 titleWidget 為 null 且 title 有值，顯示 Text
              if (titleWidget == null && (title).isNotEmpty)
                  Text(
                    title,
                    style: const TextStyle(
                        fontWeight: FontWeight.w700, fontSize: 18),
                  ),
              const SizedBox(
                height: 16,
              ),
              Text(
                content,
                style: const TextStyle(
                  fontWeight: FontWeight.w400,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 20,
              ),

              ///確定按鈕
              _confirmBtn(
                  yesAction, confirmTitle, confirmPadding, )
            ],
          ),
        ),
      ),
    ),
  );
}

///確定按鈕
Widget _confirmBtn(void Function()? yesAction, String? confirmTitle,
    EdgeInsetsGeometry? confirmPadding, ) {
  return  ElevatedButton(
      style: ElevatedButton.styleFrom(
          padding: EdgeInsets.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          minimumSize: Size.zero,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10)),
          backgroundColor: MyColors.green_157_193_65),
      onPressed: yesAction,
      child: Padding(
        padding: confirmPadding ??
            const EdgeInsets.symmetric(horizontal: 34, vertical: 9.5),
        child: Text(
          confirmTitle ?? '確定',
          style: const TextStyle(color: Colors.white, fontSize: 16),
        ),
      ));
}