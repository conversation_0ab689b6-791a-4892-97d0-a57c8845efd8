import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import '../../model/other_model/BaseDialogListModel.dart';
import '../../resource/MyColor.dart';
import '../../screen/start/my_app.dart';
import '../../util/MyRoutes.dart';
import 'gallery_photo_view_wrapper.dart';

enum ConfirmAction { ACCEPT, CANCEL, NONE }

enum TransitionType { slide, fadeIn }

/// 區分雙平台的dialog
/// 寫了常用的幾種，樣式如下：
///
/// showAlert：只有確定的dialog
/// showAlertWithCancel：確定與取消樣式
/// showAlertWithTextField：帶有一個輸入框
/// showAlertWithMultipleDatas：多資料選單樣式
/// showLoadingAlert：loading dialog
/// customAlertDialog：自訂樣式
/// fullScreenDialog：符合螢幕大小的dialog
/// showImageAlert：image dialog
/// showRadioBtnDialog : 帶有radioBtn的dialog
/// showEmptyAlert : 空白的dialog，利用Duration自行關閉彈窗

class MyDialog {
  final double radius = (Platform.isIOS) ? 14.0 : 2.0;
  final FocusNode nodeSearch = FocusNode();
  static const bool isWeb = kIsWeb;
  static final bool isIos = !kIsWeb && Platform.isIOS;

  RouteTransitionsBuilder _getRouteTransitionBuilder(
      TransitionType transitionType) {
    return switch (transitionType) {
      ///飛入式動畫
      TransitionType.slide => (context, a1, a2, widget) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).chain(CurveTween(curve: Curves.easeOutQuart)).animate(a1),
            child: widget,
          );
        },

      ///漸進式動畫
      TransitionType.fadeIn => (context, a1, a2, widget) {
          return ScaleTransition(
            scale: Tween<double>(
              begin: 0.5,
              end: 1.0,
            ).chain(CurveTween(curve: Curves.easeOutQuart)).animate(a1),
            child: FadeTransition(
              opacity: Tween<double>(begin: 0.5, end: 1.0).animate(a1),
              child: widget,
            ),
          );
        }
    };
  }

  /// 圖片畫廊Dialog(多張)
  // showFullScreenImageDialog(BuildContext context, List<String> list, int index,
  //     {bool verticalGallery = false,
  //     bool isBase64 = false,
  //     // bool applySave = true,
  //     bool isHaveLengthHint = true}) {
  //   pushTo(
  //     PageName.galleryPhotoViewWrapper.toString(),
  //     builder: (context) => GalleryPhotoViewWrapper(
  //       galleryUrls: list,
  //       backgroundDecoration: const BoxDecoration(
  //         color: Colors.black,
  //       ),
  //       initialIndex: index,
  //       isBase64: isBase64,
  //       scrollDirection: verticalGallery ? Axis.vertical : Axis.horizontal,
  //       // applySave: applySave,
  //       isHaveLengthHint: isHaveLengthHint,
  //     ),
  //   );
  // }

  // ///直接呼叫api的圖片
  // void showFullScreenImageDialogWithDio(
  //     BuildContext context,
  //     List<String> imageUrls, {
  //       int initialIndex = 0,
  //       bool? isShowLengthHint
  //     }) async {
  //   showLoadingOverlay();
  //   // final token = context.read<MainScreenViewModel>().memberToken;
  //
  //   // 平行請求多張圖片，加上 index 紀錄
  //   final results = await Future.wait(
  //     imageUrls.asMap().entries.map((entry) async {
  //       final index = entry.key;
  //       final url = entry.value;
  //       final bytes = await fetchTokenImageBytes(url, ''); //token ?? ''
  //       return {
  //         'index': index,
  //         'bytes': bytes,
  //       };
  //     }).toList(),
  //   );
  //
  //   // 過濾成功的圖片
  //   final validResults = results.where((e) => e['bytes'] != null).toList();
  //
  //   if (validResults.isEmpty) {
  //     dismissLoadingOverlay();
  //     if (context.mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(
  //         const SnackBar(content: Text('無法載入圖片')),
  //       );
  //     }
  //     return;
  //   }
  //
  //   // 建立 base64 圖片清單，並計算轉換後的正確 index
  //   final base64Images = <String>[];
  //   int? newInitialIndex;
  //
  //   for (int i = 0; i < validResults.length; i++) {
  //     final item = validResults[i];
  //     final bytes = item['bytes'] as Uint8List;
  //     final originalIndex = item['index'] as int;
  //
  //     base64Images.add(base64Encode(bytes));
  //
  //     if (originalIndex == initialIndex) {
  //       newInitialIndex = i;
  //     }
  //   }
  //
  //   dismissLoadingOverlay();
  //
  //   pushTo(
  //     PageName.galleryPhotoViewWrapper.toString(),
  //     builder: (context) => GalleryPhotoViewWrapper(
  //       galleryUrls: base64Images,
  //       backgroundDecoration: const BoxDecoration(
  //         color: Colors.black,
  //       ),
  //       initialIndex: newInitialIndex ?? 0,
  //       isBase64: true,
  //       scrollDirection: Axis.horizontal,
  //       isHaveLengthHint: isShowLengthHint ?? true,
  //     ),
  //   );
  // }



  Future<Uint8List?> fetchTokenImageBytes(String imageUrl, String token) async {
    try {
      final dio = Dio();
      final response = await dio.get(
        imageUrl,
        options: Options(
          headers: {'Authorization': 'Bearer $token'},
          responseType: ResponseType.bytes,
        ),
      );
      return response.data;
    } catch (e) {
      debugPrint("Failed to load image: $e");
      return null;
    }
  }

  /// 只有確定的dialog
  Future<void> showAlert(
    BuildContext context,
    String title,
    String msg,
    TransitionType transitionType, {
    Function()? yesAction,
    String? btnText,
    bool? barrierDismissible,
  }) {
    final RouteTransitionsBuilder transitionBuilder = switch (transitionType) {
      ///飛入式動畫
      TransitionType.slide => (context, a1, a2, widget) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).chain(CurveTween(curve: Curves.easeOutQuart)).animate(a1),
            child: widget,
          );
        },

      ///漸進式動畫
      TransitionType.fadeIn => (context, a1, a2, widget) {
          return ScaleTransition(
            scale: Tween<double>(
              begin: 0.5,
              end: 1.0,
            ).chain(CurveTween(curve: Curves.easeOutQuart)).animate(a1),
            child: FadeTransition(
              opacity: Tween<double>(begin: 0.5, end: 1.0).animate(a1),
              child: widget,
            ),
          );
        }
    };

    return showGeneralDialog(
      transitionBuilder: transitionBuilder,
      barrierDismissible: barrierDismissible ?? false,
      barrierLabel: '',
      context: context,
      pageBuilder: (
        BuildContext context,
        Animation animation,
        Animation secondaryAnimation,
      ) {
        yesAction ??= () => Navigator.of(context).pop();

        return Center(
          child: Material(
            clipBehavior: Clip.antiAlias, //Material元件下要使用圓角必須寫這行
            type: MaterialType.transparency,
            borderRadius: BorderRadius.circular(radius),
            child: (isIos)
                ? CupertinoAlertDialog(
                    title: Text(title),
                    content: Text(msg),
                    actions: <Widget>[
                      CupertinoDialogAction(
                        onPressed: yesAction,
                        child: Text(btnText ?? '確定'),
                      ),
                    ],
                  )
                : AlertDialog(
                    title: Text(title, textAlign: TextAlign.center),
                    content: Text(
                      msg,
                      textAlign: TextAlign.center,
                    ),
                    actions: <Widget>[
                      ElevatedButton(
                        onPressed: yesAction,
                        child: Text(btnText ?? '確定'),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  /// 帶有確定與取消的dialog
  Future<ConfirmAction?> showAlertWithCancel(
    BuildContext context,
    String title,
    String msg,
    TransitionType transitionType, {
    Function()? yesAction,
    Function()? cancelAction,
    String? confirmText,
    String? cancelText,
    TextStyle? contentStyle,
    TextStyle? titleStyle,
  }) {
    final RouteTransitionsBuilder transitionBuilder = switch (transitionType) {
      ///飛入式動畫
      TransitionType.slide => (context, a1, a2, widget) {
          return SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 1),
              end: Offset.zero,
            ).chain(CurveTween(curve: Curves.easeOutQuart)).animate(a1),
            child: widget,
          );
        },

      ///漸進式動畫
      TransitionType.fadeIn => (context, a1, a2, widget) {
          return ScaleTransition(
            scale: Tween<double>(
              begin: 0.5,
              end: 1.0,
            ).chain(CurveTween(curve: Curves.easeOutQuart)).animate(a1),
            child: FadeTransition(
              opacity: Tween<double>(begin: 0.5, end: 1.0).animate(a1),
              child: widget,
            ),
          );
        }
    };
    return showGeneralDialog(
        transitionBuilder: transitionBuilder,
        context: context,
        barrierDismissible: false, //控制點擊對話框以外的區域是否隱藏對話框
        pageBuilder: (
          BuildContext context,
          Animation animation,
          Animation secondaryAnimation,
        ) {
          yesAction ??= () => Navigator.of(context).pop();

          return Center(
            child: Material(
              clipBehavior: Clip.antiAlias, //Material元件下要使用圓角必須寫這行
              type: MaterialType.transparency,
              borderRadius: BorderRadius.circular(radius),
              child: (isIos)
                  ? CupertinoAlertDialog(
                      title: Text(
                        title,
                        style: titleStyle,
                      ),
                      content: Text(
                        msg,
                        style: contentStyle,
                      ),
                      actions: <Widget>[
                        CupertinoDialogAction(
                          onPressed: cancelAction ??
                              () {
                                Navigator.of(context).pop();
                              },
                          child: Text(cancelText ?? '取消'),
                        ),
                        CupertinoDialogAction(
                          onPressed: yesAction,
                          child: Text(confirmText ?? '確定'),
                        ),
                      ],
                    )
                  : AlertDialog(
                      title: Text(title, textAlign: TextAlign.center),
                      content: Text(
                        msg,
                        textAlign: TextAlign.center,
                      ),
                      actions: <Widget>[
                        CupertinoDialogAction(
                          onPressed: cancelAction ??
                              () {
                                Navigator.of(context).pop();
                              },
                          child: Text(cancelText ?? '取消'),
                        ),
                        ElevatedButton(
                          onPressed: yesAction,
                          child: Text(confirmText ?? '確定'),
                        ),
                      ],
                    ),
            ),
          );
        });
  }

  /// 空白的dialog，利用Duration自行關閉彈窗
  Future<void> showEmptyAlert(
      BuildContext context, String msg, int milliseconds) {
    return showGeneralDialog(
        barrierDismissible: false,
        context: context,
        pageBuilder: (context, _, __) {
          Future.delayed(Duration(milliseconds: milliseconds),
              () => Navigator.of(context).pop());

          if (Platform.isIOS) {
            return SizedBox(
              height: 350,
              child: CupertinoAlertDialog(
                content: Text((msg == null) ? "" : msg),
              ),
            );
          } else {
            return Center(
              child: ColoredBox(
                color: Colors.white,
                child: SizedBox(
                  height: 150,
                  width: 300,
                  child: AlertDialog(
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    content: Center(child: Text(msg)),
                  ),
                  //  AlertDialog(
                  //   content: Text(msg,textAlign: TextAlign.center,),
                  // ),
                ),
              ),
            );
          }
        });
  }

  /// loading樣式
  void showLoadingAlert(BuildContext context, {bool? useRootNavigator}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      useRootNavigator: useRootNavigator ?? true,
      builder: (context) {
        return const MyCustomLoadingDialog();
      },
    );
  }

  /// 多資料選單
  Future<BaseDialogListModel?> showAlertWithMultipleDatas(BuildContext context,
      bool isShowTitle, String title, List<BaseDialogListModel> datas,
      {Color? titleColor, double? titleFontSize}) async {
    if (Platform.isIOS) {
      return showCupertinoModalPopup<BaseDialogListModel>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: isShowTitle
                ? Text(
                    title,
                    textAlign: TextAlign.center,
                    style:
                        TextStyle(color: titleColor, fontSize: titleFontSize),
                  )
                : const SizedBox.shrink(),
            actions: _createIOSDataList(context, datas),
            cancelButton: CupertinoActionSheetAction(
              isDefaultAction: true,
              child: const Text('取消'),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          );
        },
      );
    } else {
      return await showDialog<BaseDialogListModel>(
          barrierDismissible: true,
          context: context,
          builder: (BuildContext context) {
            return SimpleDialog(
              title: isShowTitle ? Text(title) : const SizedBox.shrink(),
              children: _createAndroidDataList(context, datas),
            );
          });
    }
  }

  /// 圖片來源選單
  Future<BaseDialogListModel?> showAlertWithImagePicker(
      BuildContext context, List<BaseDialogListModel> datas) async {
    if (Platform.isIOS) {
      return await showCupertinoModalPopup<BaseDialogListModel>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: const Text(
              '請選擇圖片來源',
              style: TextStyle(color: MyColors.blue, fontSize: 18),
            ),
            actions: _createIOSDataList(context, datas),
            cancelButton: CupertinoActionSheetAction(
              isDefaultAction: true,
              child: const Text('取消'),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
          );
        },
      );
    } else {
      return await showDialog<BaseDialogListModel>(
          barrierDismissible: true,
          context: context,
          builder: (BuildContext context) {
            return SimpleDialog(
              title: Text(
                '請選擇圖片來源',
                style: const TextStyle(color: MyColors.blue, fontSize: 18),
              ),
              children: _createAndroidDataList(context, datas),
            );
          });
    }
  }

  List<Widget> _createIOSDataList(
      BuildContext context, List<BaseDialogListModel> datas) {
    List<Widget> widgets = [];
    datas.forEach((element) => widgets.add(CupertinoActionSheetAction(
          child:
              Text(element.title, style: const TextStyle(color: MyColors.blue)),
          onPressed: () {
            Navigator.pop(context, element);
          },
        )));
    return widgets;
  }

  List<Widget> _createAndroidDataList(
      BuildContext context, List<BaseDialogListModel> datas) {
    List<Widget> widgets = [];
    datas.forEach((element) => widgets.add(SimpleDialogOption(
          child:
              Text(element.title, style: const TextStyle(color: MyColors.blue)),
          onPressed: () {
            Navigator.pop(context, element);
          },
        )));
    return widgets;
  }

  ///自訂樣式
  void customAlertDialog(BuildContext context) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return Dialog(
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(radius)), //this right here
            child: Container(
              height: 200,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const TextField(
                      decoration: InputDecoration(
                          border: InputBorder.none,
                          hintText: 'What do you want to remember?'),
                    ),
                    SizedBox(
                        width: 320.0,
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF1BC0C5)),
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          child: const Text(
                            "Save",
                            style: TextStyle(color: Colors.white),
                          ),
                        ))
                  ],
                ),
              ),
            ),
          );
        });
  }


  /// 自定義大小與客製widget
  Future customWidgetDialog(BuildContext context, bool barrierDismissible,
      TransitionType transitionType, Widget widget) async {
    //方法一：可以自訂大小
    // final double radius = (Platform.isIOS) ? 14.0 : 2.0;
    final RouteTransitionsBuilder transitionBuilder =
        _getRouteTransitionBuilder(transitionType);
    return showGeneralDialog(
      transitionBuilder: transitionBuilder,
      context: context,
      barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
      barrierDismissible: barrierDismissible,
      transitionDuration: const Duration(milliseconds: 250),
      //這個是時間
      barrierColor: Colors.black54,
      //添加這個屬性是顏色
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Center(
          child: Material(
            clipBehavior: Clip.antiAlias, //Material元件下要使用圓角必須寫這行
            type: MaterialType.transparency,
            borderRadius: BorderRadius.circular(radius),
            child: widget,
          ),
        );
      },
    );
  }

//   ///圖片的dialog
//   Future<void> showImageAlert(
//     BuildContext context, {
//     String? imgUrl,
//     String? base64,
//     Object? heroTag,
//     bool? applySave,
//     bool? isHaveLengthHint,
//   }) {
//     final _applySave = applySave ?? true;
//     final _isHaveLengthHint = isHaveLengthHint ?? true;
//
//     PhotoViewHeroAttributes? hero;
//
//     if (heroTag != null) {
//       hero = PhotoViewHeroAttributes(
//         tag: heroTag,
//       );
//     }
//
//     return showDialog(
//         context: context,
//         useSafeArea: false,
//         builder: (BuildContext context) {
//           if ((imgUrl != null && imgUrl.isNotEmpty) ||
//               (base64 != null && base64.isNotEmpty)) {
//             return GestureDetector(
//               onTap: () {
//                 Navigator.of(context).pop();
//               },
//               child: GalleryPhotoViewWrapper(
//                 galleryUrls: [base64?.isNotEmpty == true ? base64! : imgUrl!],
//                 backgroundDecoration: const BoxDecoration(color: Colors.black),
//                 initialIndex: 0,
//                 isBase64: base64?.isNotEmpty == true,
//                 scrollDirection: Axis.horizontal,
//                 isHaveLengthHint: _isHaveLengthHint,
//               ),
//             );
//           } else {
//             return Container(); // 都沒有資料才回傳空容器
//           }
//         });
//   }
}

///自定義的loading dialog
class MyCustomLoadingDialog extends StatelessWidget {
  const MyCustomLoadingDialog({super.key});

  @override
  Widget build(BuildContext context) {
    Duration insetAnimationDuration = const Duration(milliseconds: 100);
    Curve insetAnimationCurve = Curves.decelerate;

    final isIos = MyDialog.isIos;
    final double radius = (isIos) ? 14.0 : 2.0;

    // showDialog(context: context, builder: builder).withHideMusicOverlay(context);

    RoundedRectangleBorder defaultDialogShape = RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(radius)),
    );

    return AnimatedPadding(
      padding: MediaQuery.viewInsetsOf(context) +
          const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0),
      duration: insetAnimationDuration,
      curve: insetAnimationCurve,
      child: MediaQuery.removeViewInsets(
        removeLeft: true,
        removeTop: true,
        removeRight: true,
        removeBottom: true,
        context: context,
        child: Center(
          child: SizedBox(
            width: 120,
            height: 120,
            child: Material(
              elevation: 24.0,
              color: Colors.black,
              type: MaterialType.card,
              shape: defaultDialogShape,
              //在這裡修改稱我們想要顯示的widget就行了，外部的屬性跟其他dialog保持一致。
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  if (isIos)
                    Theme(
                      data: ThemeData(
                          cupertinoOverrideTheme: const CupertinoThemeData(
                              brightness: Brightness.dark)),
                      child: const CupertinoActivityIndicator(),
                    )
                  else
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
