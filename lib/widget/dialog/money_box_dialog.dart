import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/resource/MyColor.dart';

import '../container_with_radius.dart';
import 'MyDialog.dart';

void moneyBoxDialog(BuildContext context, String name, String openTime){
  final textStyle = TextStyle(fontSize: 28.sp,color: MyColors.green_129_128_94);
   MyDialog().customWidgetDialog(
    context,
    true,
    TransitionType.fadeIn,
    SafeArea(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20.r),
        //這邊若只靠ContainerWithRadius下方的圓角會消失
        child: ContainerWithRadius(
          isHaveBorder: false,
          w: 470.w,
          h: 288.h,
          r: 20.r,
          color: Colors.white,
          child:
         Column(
           crossAxisAlignment: CrossAxisAlignment.center,
           mainAxisAlignment: MainAxisAlignment.center,
           children: [
             Text('錢箱已開啟',style: TextStyle(fontSize: 44.sp,color: MyColors.brown_57_54_18),),
             SizedBox(height: 48.h,),
             Text(name,style: textStyle,),
             SizedBox(height: 22.38.h,),
             Text(openTime,style: textStyle,),
           ],
         )
        ),
      ),
    ),
  );
}