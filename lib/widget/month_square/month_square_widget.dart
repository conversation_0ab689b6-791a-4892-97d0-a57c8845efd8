import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class MonthSquareWidget extends StatelessWidget {
  final VoidCallback onTap;
  final bool isSelected;
  final String monthLabel;

  const MonthSquareWidget({
    super.key,
    required this.onTap,
    required this.isSelected,
    required this.monthLabel,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 100.w,
        height: 60.h,
        decoration: BoxDecoration(
          border: Border.all(color: MyColors.grey_235_235_235, width: 0.5),
          color: isSelected
              ? MyColors.green_157_193_65.withOpacity(0.5)
              : Colors.white,
          boxShadow: [
            BoxShadow(
              color: const Color(0x14000000),
              offset: const Offset(3, 6),
              blurRadius: 10.r,
            ),
          ],
          borderRadius: BorderRadius.circular(12),
        ),
        alignment: Alignment.center,
        child: Text(
          monthLabel,
          style: TextStyle(
            fontSize: 24.sp,
            fontWeight: FontWeight.w600,
            color: isSelected ? Colors.white : Colors.grey,
          ),
        ),
      ),
    );
  }
}
