import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'month_square_widget.dart';

class MonthSquareScreen extends StatefulWidget {
  final ValueChanged<int> onSubmit;
  const MonthSquareScreen({super.key, required this.onSubmit});

  @override
  State<MonthSquareScreen> createState() => _MonthSquareScreenState();
}

class _MonthSquareScreenState extends State<MonthSquareScreen> {
  int? selectedIndex;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: _buildMonthSelect(),
    );
  }

  Widget _buildMonthSelect() {
    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.only(bottom: 42.h),
      physics: const BouncingScrollPhysics(),
      itemCount: 12,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 20.w,
        mainAxisSpacing: 20.h,
        childAspectRatio: 286 / 188,
      ),
      itemBuilder: (context, index) {
        return MonthSquareWidget(
          onTap: () {
            setState(() {
              selectedIndex = index;
              widget.onSubmit(index);
            });
          },
          isSelected: selectedIndex == index,
          monthLabel: '${index + 1}月',
        );
      },
    );

  }
}

