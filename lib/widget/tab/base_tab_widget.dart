import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class BaseTabWidget extends StatelessWidget {
  final String title;
  final bool isSelected;
  final Widget icon;

  const BaseTabWidget({
    super.key,
    required this.title,
    required this.isSelected,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return _baseTabWidget();
  }

  ///tab元件
  Widget _baseTabWidget() {
    final color = isSelected ? Colors.white : MyColors.green_129_128_94;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        icon,
        SizedBox(height: 4.h),
        Text(
          title,
          style: TextStyle(
            fontSize: 25.sp,
            color: color,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
