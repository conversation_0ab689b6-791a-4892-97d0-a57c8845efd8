import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';

import '../../provider/shopping_car_provider.dart';
import '../../screen/member/member_info/member_info_main_screen_view_model.dart';
import '../reserve/reserve_slider.dart';

class MemberMainTabWidget extends StatefulWidget {
  final List<Widget> childScreen;
  final List<Widget Function(bool isSelected)> tabs;
  final int initIndex;
  final MemberInfoMainScreenViewModel vm;

  const MemberMainTabWidget({
    super.key,
    required this.childScreen,
    required this.tabs,
    required this.initIndex,
    required this.vm,
  });

  @override
  _MemberMainTabWidgetState createState() => _MemberMainTabWidgetState();
}

class _MemberMainTabWidgetState extends State<MemberMainTabWidget> with TickerProviderStateMixin {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initIndex;
    _pageController = PageController(initialPage: _currentIndex);

    // 初始化動畫控制器並交給 ViewModel
    widget.vm.infoAniController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    widget.vm.infoAniController.dispose();
    super.dispose();
  }

  void _onTapTab(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.jumpToPage(index);
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  Widget _buildTab(int index) {
    final bool isSelected = index == _currentIndex;
    return GestureDetector(
      onTap: () {
        widget.vm.closeSlider();
        _onTapTab(index);
      },
      child: Padding(
        padding: EdgeInsets.only(right: 5.w),
        child: Container(
          width: 142.w,
          height: 89.h,
          decoration: BoxDecoration(
            color: isSelected
                ? MyColors.green_157_193_65
                : const Color(0xFFFFFFFF).withOpacity(0.3),
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? MyColors.green_157_193_65.withOpacity(0.6)
                    : const Color(0xFF000000).withOpacity(0.08),
                blurRadius: 10.r,
                offset: const Offset(3, 6),
              ),
            ],
          ),
          child: Center(child: widget.tabs[index](isSelected)),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          Positioned.fill(
            top: 0,
            child: PageView(
              physics: const NeverScrollableScrollPhysics(),
              controller: _pageController,
              onPageChanged: _onPageChanged,
              children: widget.childScreen,
            ),
          ),
          Positioned(
            top: 23.h,
            left: 0.w,
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                children: [
                  _returnBtn(),
                  SizedBox(width: 12.w),
                  _memberInfo(),
                ],
              ),
            ),
          ),
          Positioned(
            top: 23.h,
            right: 33.w,
            child: Row(
              children: List.generate(
                widget.tabs.length,
                    (index) => _buildTab(index),
              ),
            ),
          ),
          // 側邊資訊欄
          Positioned(
            right: 0,
            top: 0,
            child: AnimatedBuilder(
              animation: widget.vm.infoAniController,
              builder: (context, child) => _sliderArea(),
            ),
          ),
        ],
      ),
    );
  }

  ///右邊側邊欄區塊
  Widget _sliderArea() {
    return ReserveSlider(
      sizeFactor: widget.vm.infoAniController,
      date: widget.vm.selectedModel?.orderTime ?? '',
      week: widget.vm.selectedModel?.week ?? '',
      orderId: widget.vm.selectedModel?.orderId ?? '',
      className: widget.vm.selectedModel?.className ?? '',
      memberName: widget.vm.selectedModel?.memberName ?? '',
      memberPhone: widget.vm.selectedModel?.memberPhone ?? '',
      beautician: widget.vm.selectedModel?.beautician ?? '',
      customerSelectionType: widget.vm.selectedModel?.customerSelectionType ?? '',
      classDate: widget.vm.selectedModel?.orderClassTime ?? '',
      price: widget.vm.selectedModel?.price ?? 0,
    );
  }

  Widget _memberInfo() {
    final vm = context.read<ShoppingCarProvider>();
    return (vm.memberName ?? '').isEmpty
        ? Text(
      '尚未選擇會員',
      style: TextStyle(fontSize: 40.sp, color: MyColors.brown_57_54_18),
    )
        : Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          vm.memberName ?? '',
          style: TextStyle(fontSize: 40.sp, color: MyColors.brown_57_54_18),
        ),
        Text(
          vm.memberPhone ?? '',
          style: TextStyle(fontSize: 32.sp, color: MyColors.green_129_128_94),
        ),
      ],
    );
  }

  Widget _returnBtn() {
    return InkWell(
      onTap: () => Navigator.of(context).pop(),
      child: Container(
        width: 89.w,
        height: 89.w,
        decoration: BoxDecoration(
          color: MyColors.green_157_193_65,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Center(
          child: Transform.translate(
            offset: const Offset(5, 0),
            child: Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 40.w,
            ),
          ),
        ),
      ),
    );
  }
}
