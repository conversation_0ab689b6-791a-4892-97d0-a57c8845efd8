import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:barcode_scan2/gen/protos/protos.pb.dart' hide ScanResult;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';

import '../../screen/brand_member/brand_member_screen.dart';
import '../../screen/history_order/history_order_screen.dart';
import '../../screen/scan/scan_screen.dart';
import '../../screen/search_product/search_product_screen.dart';
import '../../screen/start/my_app.dart';
import '../../util/MyRoutes.dart';
import '../btn/drawer_btn.dart';
import '../../provider/main_provider.dart';
import '../container_with_radius.dart';
import '../dialog/MyDialog.dart';
import '../dialog/money_box_dialog.dart';
import '../scanner/scanner.dart';

class MainTabWidget extends StatefulWidget {
  final List<Widget> childScreen;
  final List<Widget Function(bool isSelected)> tabs;
  final Function() drawerOnTap;

  const MainTabWidget({
    super.key,
    required this.childScreen,
    required this.tabs,
    required this.drawerOnTap,
  });

  @override
  _MainTabWidgetState createState() => _MainTabWidgetState();
}

class _MainTabWidgetState extends State<MainTabWidget> {
  late final PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    final provider = Provider.of<MainProvider>(context, listen: false);
    _currentIndex = provider.initialIndex;
    _pageController = PageController(initialPage: _currentIndex);
  }

  void _onTapTab(int index) {
    ///開啟商品搜尋彈窗
    if (index == 0) {
      MyDialog().customWidgetDialog(
        context,
        false,
        TransitionType.fadeIn,
        SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20.r),
              //這邊若只靠ContainerWithRadius下方的圓角會消失
              child: ContainerWithRadius(
                isHaveBorder: false,
                w: double.infinity.w,
                h: double.infinity.h,
                r: 20.r,
                color: Colors.white,
                child: SearchProductScreen(
                  backOnTap: () {

                    Navigator.of(context).pop();
                  },
                  submitOnTap: (product, type) {
                    final shoppingCarVm = context.read<ShoppingCarProvider>();
                    shoppingCarVm.addToShoppingCarByProductId(
                      productId: product?.productId ?? '',
                    );


                    if(type == '商品'){
                      context.read<MainProvider>().setInitialIndex(2);

                    }

                    if(type == '課程'){
                      context.read<MainProvider>().setInitialIndex(3);

                    }
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ),
          ),
        ),
      );
      return;
    }

    ///掃描
    if (index == 1) {
      // showScanDialog(context: context);
      runBarCodeScannerCamForOrder();
      return;
    }

    ///開啟錢箱彈窗
    if (index == 9) {
      DateTime now = DateTime.now();
      String formatted = DateFormat('yyyy-MM-dd HH:mm').format(now);
      moneyBoxDialog(context, 'Tim', formatted);
      return;
    }

    ///導向到品牌會員頁面
    if (index == 8) {
      pushTo(
        PageName.brandMember.toString(),
        builder: (context) => BrandMemberScreen(),
        isPushReplacement: true,
      );
      return;
    }

    ///導向到歷史訂單頁面
    if (index == 7) {
      pushTo(
        PageName.historyOrder.toString(),
        builder: (context) => HistoryOrderScreen(),
        isPushReplacement: true,
      );
      return;
    }

    setState(() {
      _currentIndex = index;
    });
    _pageController.jumpToPage(index);

    context.read<MainProvider>().setInitialIndex(index);
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });

    context.read<MainProvider>().setInitialIndex(index);
  }

  Widget _buildTab(int index) {
    final bool isSelected = index == _currentIndex;
    return GestureDetector(
      onTap: () => _onTapTab(index),
      child: Padding(
        padding: EdgeInsets.only(right: 5.w),
        child: Container(
          width: 142.w,
          height: 89.w,
          decoration: BoxDecoration(
            color: isSelected
                ? MyColors.green_157_193_65
                : const Color(0xFFFFFFFF).withOpacity(0.3),
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? MyColors.green_157_193_65.withOpacity(0.6)
                    : const Color(0xFF000000).withOpacity(0.08),
                blurRadius: 10.r,
                offset: const Offset(3, 6),
              ),
            ],
          ),
          child: Center(child: widget.tabs[index](isSelected)),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Selector<MainProvider, int>(
        selector: (_, vm) => vm.initialIndex,
        builder: (context, index, child) {
          if (_currentIndex != index) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              setState(() {
                _currentIndex = index;
                _pageController.jumpToPage(index);
              });
            });
          }

          return Stack(
            children: [
              /// 主畫面內容
              Positioned.fill(
                top: 0,
                child: PageView(
                  physics: const NeverScrollableScrollPhysics(),
                  controller: _pageController,
                  onPageChanged: _onPageChanged,
                  children: widget.childScreen,
                ),
              ),

              /// 上方的 DrawerBtn + Tabs
              Positioned(
                top: 0.h,
                left: 0.w,
                right: 0.w,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10.w),
                  child: Row(
                    children: [
                      /// DrawerBtn 固定不滑動
                      DrawerBtn(drawerOnTap: widget.drawerOnTap),
                      SizedBox(width: 12.w),

                      /// Tabs 可滑動
                      SizedBox(
                        width: 1318.w,
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          padding: EdgeInsets.only(top: 23.h, bottom: 26.w),
                          child: Row(
                            children: List.generate(
                              widget.tabs.length,
                              (index) => _buildTab(index),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
