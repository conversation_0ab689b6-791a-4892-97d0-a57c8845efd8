import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/screen/member/member_info/member_info_main_screen.dart';
import 'package:sbar_pos/screen/start/my_app.dart';

import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../util/MyRoutes.dart';
import '../background/base_background.dart';
import '../member/member_area.dart';
import '../member/member_area_un_select.dart';

class PaymentTab extends StatefulWidget {
  final List<Widget Function(bool isSelect)> tabs;
  final Widget child;

  const PaymentTab({super.key, required this.tabs, required this.child});

  @override
  State<PaymentTab> createState() => _PaymentTabState();
}

class _PaymentTabState extends State<PaymentTab> {
  final PageController _pageController = PageController();

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ShoppingCarProvider>(
      builder: (context, vm, _) {
        return Stack(
          children: [
            Positioned.fill(
              top: 0,
              child:
              BaseBackground(child: widget.child,)


            ),

            Positioned(
              top: 0.h,
              left: 0.w,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    ///會員資料區塊
                    if (vm.memberName == null) _memberAreaUnSelect(),

                    if (vm.memberName != null) _memberArea(),

                    SizedBox(width: 12.w),

                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 23.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          widget.tabs.length,
                          (index) => _buildTab(index),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTab(int index) {
    return GestureDetector(
      onTap: () {
        switch (index) {
          case 0:
            //基本資料
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 0),
            );
            break;
          case 1:

          //歷史訂單
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 1),
            );
            break;

          case 2:

          //預約訂單
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 2),
            );
            break;

          case 3:

          //會員儲值金
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 3),
            );
            break;
          case 4:

          //儲值紀錄
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 4),
            );
            break;
          case 5:

          //基本資料
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 5),
            );
            break;
          case 6:

          //會員課程
            pushTo(
              PageName.memberInfoMain.toString(),
              builder: (context) => MemberInfoMainScreen(initIndex: 6),
            );
            break;

          default:
            break;
        }
      },
      child: Padding(
        padding: EdgeInsets.only(right: 10.w),
        child: Container(
          width: 142.w,
          height: 89.h,
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF).withOpacity(0.3),
            borderRadius: BorderRadius.circular(20.r),

            boxShadow: [
              BoxShadow(
                color: const Color(0xFF000000).withOpacity(0.08), // #00000014
                blurRadius: 10.r,
                offset: const Offset(3, 6),
              ),
            ],
          ),
          child: Center(child: widget.tabs[index](false)),
        ),
      ),
    );
  }

  ///上方會員區塊(未選擇會員)
  Widget _memberAreaUnSelect() {
    return MemberAreaUnSelect();
  }

  ///上方會員區塊(已選擇會員)
  Widget _memberArea() {
    return MemberArea();
  }
}
