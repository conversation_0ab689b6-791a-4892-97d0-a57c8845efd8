import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/switch_account/switch_account_dialog_screen.dart';
import '../../screen/product_inventory_management/product_inventory_management_screen_view_model.dart';
import '../container_with_radius.dart';
import '../dialog/MyDialog.dart';

///切換帳號彈窗
void switchAccountDialog({
  required BuildContext context,
  required ProductInventoryManagementScreenViewModel vm,
}) {
  MyDialog().customWidgetDialog(
    context,
    false,
    TransitionType.fadeIn,
    SafeArea(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 500.w, vertical: 350.h),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(20.r),
          //這邊若只靠ContainerWithRadius下方的圓角會消失
          child: ContainerWithRadius(
            isHaveBorder: true,
            w: double.infinity.w,
            h: double.infinity.h,
            r: 20.r,
            color: Colors.white,
            child: SwitchAccountDialogScreen(vm: vm),
          ),
        ),
      ),
    ),
  );
}
