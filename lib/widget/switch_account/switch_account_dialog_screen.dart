import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../resource/MyColor.dart';
import '../../screen/payment/payment_screen_view_model.dart';
import '../../screen/product_inventory_management/product_inventory_management_screen_view_model.dart';
import '../container_with_radius.dart';
import '../textField/my_text_field.dart';

class SwitchAccountDialogScreen extends StatefulWidget {
  final ProductInventoryManagementScreenViewModel vm;

  const SwitchAccountDialogScreen({super.key, required this.vm});

  @override
  State<SwitchAccountDialogScreen> createState() =>
      _SwitchAccountDialogScreenState();
}

class _SwitchAccountDialogScreenState extends State<SwitchAccountDialogScreen> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.vm,
      child: Padding(
        padding: EdgeInsets.only(top: 50.h),
        child: Column(
          children: [
            Text(
              '切換帳號',
              style: TextStyle(
                color: MyColors.green_129_128_94,
                fontSize: 30.sp,
              ),
            ),
            SizedBox(height: 50.h),

            //帳號
            _accountArea(),
            SizedBox(height: 17.h),

            //密碼
            _pwdArea(),
            SizedBox(height: 40.h),

            //取消、登入
            Selector<ProductInventoryManagementScreenViewModel, bool>(
              selector: (context, vm) => vm.loading,
              builder: (context, loading, _) {
                return AnimatedSwitcher(
                  duration: 300.ms,
                  child: !loading
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _baseBtn(
                              w: (200 - 20).w,
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              title: '取消',
                            ),
                            SizedBox(width: 20.w),
                            _baseBtn(
                              w: (300 - 20).w,
                              onTap: () {
                                widget.vm.loginLogic(context, () {
                                  Navigator.of(context).pop();
                                });
                              },
                              title: '登入',
                            ),
                          ],
                        )
                      : Center(
                          key: ValueKey('loading'),
                          child: CircularProgressIndicator(
                            color: MyColors.green_67_160_71,
                          ),
                        ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  ///帳號區塊
  Widget _accountArea() {
    return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '帳號：',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(width: 17.w),
            SizedBox(
              width: 400.w,
              child: MyTextField(
                isSingleLine: true,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
                borderColor: MyColors.grey_235_235_235,
                hint: '請輸入帳號',
                textAlign: TextAlign.start,
                enterTextPadding: EdgeInsets.only(left: 24.w),
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.grey_164_164_164,
                ),
                controller: widget.vm.accountController,
                border: 20.r,
                height: 74.h,
              ),
            ),
          ],
        )
        .animate(
          autoPlay: false,
          onInit: (controller) {
            widget.vm.animationAccountController = controller;
          },
        )
        .shakeX(duration: const Duration(milliseconds: 500));
  }

  ///密碼區塊
  Widget _pwdArea() {
    return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '密碼：',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(width: 17.w),

            SizedBox(
              width: 400.w,
              child: MyTextField(
                isSingleLine: true,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.green_129_128_94,
                  letterSpacing: 1.5,
                ),
                obscureText: true,

                borderColor: MyColors.grey_235_235_235,
                hint: '請輸入密碼',
                textAlign: TextAlign.start,
                enterTextPadding: EdgeInsets.only(left: 24.w),
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.grey_164_164_164,
                ),
                controller: widget.vm.pwdController,
                border: 20.r,
                height: 74.h,
              ),
            ),
          ],
        )
        .animate(
          autoPlay: false,
          onInit: (controller) {
            widget.vm.animationPwdController = controller;
          },
        )
        .shakeX(duration: const Duration(milliseconds: 500));
  }

  ///base btn
  Widget _baseBtn({
    required double w,
    required VoidCallback onTap,
    required String title,
  }) {
    return ContainerWithRadius(
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      onTap: onTap,
      w: w,
      h: 70.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Center(
        child: Text(
          title,
          style: TextStyle(fontSize: 20.sp, color: MyColors.grey_164_164_164),
        ),
      ),
    );
  }
}
