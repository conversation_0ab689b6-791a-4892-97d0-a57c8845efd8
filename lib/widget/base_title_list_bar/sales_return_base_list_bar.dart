import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../container_with_radius.dart';

class SalesReturnBaseListBar extends StatelessWidget {
  final bool isTitleBar;
  final String orderId; //訂單編號
  final String invoiceId; //發票號碼
  final String checkOutTime; //結帳時間
  final String totalSalesOrderAmount; //銷售訂單總金額
  final String price; //本次付款金額
  final String priceInfo; //本次付款金額(說明)
  final String orderStatue; //訂單狀態
  final int orderStatueCode; //訂單狀態(暫定：０為未完成付款，１為其他)
  final String unPaidPrice; //未付款金額
  final String memberName; //會員
  final String operatorName; //操作員
  final Color backgroundColor;

  const SalesReturnBaseListBar({
    super.key,
    required this.isTitleBar,
    required this.orderId,
    required this.invoiceId,
    required this.checkOutTime,
    required this.totalSalesOrderAmount,
    required this.price,
    required this.priceInfo,
    required this.orderStatue,
    required this.orderStatueCode,
    required this.unPaidPrice,
    required this.memberName,
    required this.operatorName,
    required this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );


    //未完成付款
    final unPaidStatueTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.red_230_75_75,
    );

    //未付款金額（標頭）
    final unPaidTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.grey_220_220_213,
    );

    final memberTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w700,
    );

    return ContainerWithRadius(
      w: double.infinity.w,
      h: 120.h,
      r: 0.r,
      color: backgroundColor,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 58.w, right: 51.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(
              width: 158.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ///訂單編號
                  isTitleBar
                      ? Text(orderId, style: titleStyle)
                      : FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(orderId, style: titleStyle),
                  ),

                  ///發票號碼
                  isTitleBar
                      ? Text(invoiceId, style: titleStyle)
                      : FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(invoiceId, style: titleStyle),
                  ),
                ],
              ),
            ),

            SizedBox(width: 41.w),

            ///結帳時間
            SizedBox(
              width: 224.w,
              child: isTitleBar
                  ? Text(checkOutTime, style: titleStyle)
                  : FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(checkOutTime, style: titleStyle),
              ),
            ),

            SizedBox(width: 28.w),

            ///銷售訂單總金額
            isTitleBar
                ? SizedBox(
              width: 200.w,
              child: Text(totalSalesOrderAmount, style: titleStyle),
            )
                : SizedBox(
              width: 200.w,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Text(
                    totalSalesOrderAmount.isEmpty ? '':
                  '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(totalSalesOrderAmount) ?? 0)}',
                  style: titleStyle,
                ),
              ),
            ),

            SizedBox(width: 47.w),

            ///本次付款金額
            isTitleBar
                ? SizedBox(
              width: 196.w,
              child: Text(price, style: titleStyle),
            )
                : SizedBox(
              width: 196.w,
              child:
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  Text(
                    '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(price) ?? 0)}',
                    style: titleStyle,
                  ),
                  Text(
                    priceInfo,
                    overflow: TextOverflow.ellipsis,
                    style: titleStyle,
                  ),
                ],
              ),
            ),

            SizedBox(width: 107.w),

            ///訂單狀態
            SizedBox(
              width: 140.w,
              child: isTitleBar
                  ? Text(orderStatue, style: titleStyle, overflow: TextOverflow.ellipsis,)
                  : Text(orderStatue,  overflow: TextOverflow.ellipsis,style: orderStatueCode == 0 ? unPaidStatueTextStyle: titleStyle),
            ),

            SizedBox(width: 60.w),

            ///未付款金額

            SizedBox(
              width: 180.w,
              child: isTitleBar
                  ? Row(
                children: [
                  Icon(Icons.lock_outline,color: MyColors.grey_220_220_213,),
                  Text(unPaidPrice, style: unPaidTextStyle),
                ],
              )
                  :
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Text('\$ ${StringUtils.formatMoneyForDouble(double.tryParse(unPaidPrice) ?? 0)}', style: orderStatueCode == 0 ? unPaidStatueTextStyle : titleStyle),
              ),
            ),

            SizedBox(width: 54.w),

            SizedBox(
              width: 84.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ///會員
                  isTitleBar
                      ? Text(memberName, style: titleStyle)
                      : FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(memberName, style: memberTextStyle),
                  ),

                  ///操作員
                  isTitleBar
                      ? Text(operatorName, style: titleStyle)
                      : FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(operatorName, style: titleStyle),
                  ),
                ],
              ),
            ),


          ],
        ),
      ),
    );
  }
}
