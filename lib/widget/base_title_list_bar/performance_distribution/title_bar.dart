import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../model/performance/performance_info_model.dart';
import '../../../resource/MyColor.dart';
import '../../../screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';
import '../../../util/StringUtils.dart';
import '../../container_with_radius.dart';
import '../../divider/custom_vertical_divider.dart';
import '../performance_distribution_info_base_title_bar.dart';

class TitleBar extends StatefulWidget {
  final PerformanceDistributionInfoScreenViewModel vm;
  final PerformanceInfoModel model;
  final ProductType type;
  final int productIndex;

  const TitleBar({
    super.key,
    required this.model,
    required this.vm,
    required this.type,
    required this.productIndex,
  });

  @override
  State<TitleBar> createState() => _TitleBarState();
}

class _TitleBarState extends State<TitleBar> {
  @override
  Widget build(BuildContext context) {
    return _titleBar();
  }

  //主標題列
  Widget _titleBar() {
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return ContainerWithRadius(
      w: double.infinity.w,
      h: 76.h,
      r: 0.r,
      color: Colors.white,
      isHaveBorder: false,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(width: 57.w),
          Text(widget.model.skuCode, style: textStyle),
          SizedBox(width: 57.w),
          SizedBox(
            width: 600.w,
            child: Text(
              widget.model.productName,
              style: textStyle.copyWith(color: MyColors.brown_57_54_18),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 250.w),
          Text(widget.model.purchaseQuantity.toString(), style: textStyle),
          SizedBox(width: 75.w),
          Text(
            '\$ ${StringUtils.formatMoneyForDouble(widget.model.originalPrice)}',
            style: textStyle,
          ),
          SizedBox(width: 50.w),
          Text(
            '\$ ${StringUtils.formatMoneyForDouble(widget.model.actualTotalSalesPrice)}',
            style: textStyle,
          ),
          SizedBox(width: 80.w),
          Text(
            '\$ ${StringUtils.formatMoneyForDouble(widget.model.actualSalesPrice)}',
            style: textStyle,
          ),
          SizedBox(width: 40.w),
          CustomVerticalDivider(height: 60.h),
          SizedBox(width: 30.w),
          InkWell(
            onTap: () {
              widget.vm.setEdit(widget.type, true);
            },
            child: widget.vm.isEdit(widget.type)
                ? Row(
                    children: [
                      //返回
                      _returnBtn(textStyle),
                      SizedBox(width: 10.w),
                      _saveBtn(textStyle),
                    ],
                  )
                : Padding(
                    padding: EdgeInsets.only(left: 90.w),
                    child: Icon(
                      Icons.edit_note,
                      color: MyColors.grey_204_204_204,
                      size: 60.w,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  //返回
  Widget _returnBtn(TextStyle textStyle) {
    return InkWell(
      onTap: () {
        widget.vm.setEdit(widget.type, false);
        widget.vm.restorePerformanceMember(widget.type, widget.productIndex);
      },
      child: Row(
        children: [
          Transform(
            alignment: Alignment.center,
            transform: Matrix4.rotationX(3.14159), // π 弧度，垂直翻轉
            child: Icon(
              Icons.keyboard_return_rounded,
              size: 30.w,
              color: MyColors.green_129_128_94,
            ),
          ),

          Text('返回', style: textStyle.copyWith(fontSize: 30.sp)),
        ],
      ),
    );
  }

  //儲存
  Widget _saveBtn(TextStyle textStyle) {
    return InkWell(
      onTap: () {
        widget.vm.savePerformanceMember(widget.type, widget.productIndex);
        widget.vm.setEdit(widget.type, false);
      },
      child: Row(
        children: [
          Icon(Icons.check, size: 30.w, color: MyColors.green_157_193_65),
          Text(
            '儲存',
            style: textStyle.copyWith(
              color: MyColors.green_157_193_65,
              fontSize: 30.sp,
            ),
          ),
        ],
      ),
    );
  }
}
