import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../gen/r.dart';
import '../../../model/performance/performance_info_model.dart';
import '../../../model/widget_model/drop_down_model.dart';
import '../../../resource/MyColor.dart';
import '../../../screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';
import '../../btn/btn_with_green_shadow.dart';
import '../../container_with_radius.dart';
import '../../drop_down/drop_down_button.dart';
import '../../textField/base_text_field.dart';
import '../performance_distribution_info_base_title_bar.dart';

//編輯狀態
class EditStaffListWidget extends StatefulWidget {
  final PerformanceMemberModel innerModel;
  final List<PerformanceMemberModel> performanceMemberModels;
  final ProductType productType;
  final PerformanceDistributionInfoScreenViewModel vm;
  final int staffIndex;
  final int productIndex;

  const EditStaffListWidget({
    super.key,
    required this.innerModel,
    required this.productType,
    required this.vm,
    required this.staffIndex,
    required this.performanceMemberModels,
    required this.productIndex,
  });

  @override
  State<EditStaffListWidget> createState() => _EditStaffListWidgetState();
}

class _EditStaffListWidgetState extends State<EditStaffListWidget> {
  final double textFieldWidth = 300.w;

  @override
  void initState() {
    widget.vm.initFromProductModel(
      widget.productType,
      widget.performanceMemberModels,
    );
    super.initState();
  }

  @override
  void dispose() {
    widget.vm.percentController.dispose();
    widget.vm.percentPriceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _subTitleBar();
  }

  //副標題列
  Widget _subTitleBar() {
    final percentController =
        widget.vm.percentControllerMap[widget.productType]?[widget.staffIndex];
    final percentPriceController = widget
        .vm
        .percentPriceControllerMap[widget.productType]?[widget.staffIndex];

    if (percentController == null || percentPriceController == null) {
      return const SizedBox.shrink();
    }

    return ContainerWithRadius(
      w: double.infinity.w,
      h: 76.h,
      r: 0.r,
      color: widget.vm.getStaffListWidgetColor(widget.productType),
      isHaveBorder: false,
      child: Stack(
        children: [
          SizedBox.expand(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 530.w),

                //員工下拉
                _staffDropDown(),
                SizedBox(width: 80.w),

                //分配比例
                _baseTextField(percentController,ValueKey(
                  'percentController_${widget.productType}_${widget.staffIndex}',
                ),),
                SizedBox(width: 80.w),

                //分配金額
                _baseTextField(percentPriceController,ValueKey(
                  'percentPriceController${widget.productType}_${widget.staffIndex}',
                ),),

              ],
            ),
          ),

          //Ｘ按鈕
          Positioned(
            left: 450.w,
            top: 0,
            bottom: 0,
            child: Center(
              child: _baseCountBtn(() {
                widget.vm.removePerformanceMember(
                  widget.productType,
                  widget.productIndex,
                  widget.staffIndex,
                );
              }),
            ),
          ),
        ],
      ),
    );
  }


  ///
  Widget _baseTextField(TextEditingController controller,Key key) {
    return BaseTextField(
                key: key ,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                hint: '',
                controller: controller,
                w: textFieldWidth,
                onChanged: (value) {
                  //只允許一個小數點
                  if (value.indexOf('.') != value.lastIndexOf('.')) {
                    final newValue = value.substring(0, value.length - 1);
                    controller.text = newValue;
                    controller.selection = TextSelection.fromPosition(
                      TextPosition(offset: newValue.length),
                    );
                  }
                },
              );
  }

  ///員工下拉
  Widget _staffDropDown() {
    return Selector<PerformanceDistributionInfoScreenViewModel, DropDownModel?>(
      selector: (context, vm) =>
          vm.staffDropDownSelectedMap[widget.productType]?[widget.staffIndex],
      shouldRebuild: (p, n) => true,
      builder: (context, typeDropDownModel, _) {
        return ContainerWithRadius(
          w: textFieldWidth,
          h: 54.h,
          r: 20.r,
          color: Colors.white,
          isHaveBorder: true,
          child: Padding(
            padding: EdgeInsets.only(
              left: 15.w,
              right: 8.w,
              top: 7.h,
              bottom: 7.h,
            ),
            child: MyDropdownButtonT<DropDownModel>(
              tailWidget: Image(
                image: R.image.icon_arrow_down_with_circle(),
                width: 40.w,
                height: 40.w,
              ),
              isExpanded: false,
              underline: SizedBox.shrink(),
              hint: Text(
                '請選擇類型',
                style: TextStyle(
                  fontSize: 23.sp,
                  color: MyColors.grey_60_60_67.withOpacity(0.3),
                ),
              ),
              textStyle: TextStyle(
                fontSize: 23.sp,
                color: MyColors.brown_57_54_18,
              ),
              selectedItem:
                  widget.vm.staffDropDownSelectedMap[widget.productType]?[widget
                      .staffIndex],
              labelSelector: (item) {
                return item.name;
              },
              items: widget.vm.staffDropDownList,
              onChanged: (DropDownModel? value) async {
                if (widget.vm.staffDropDownSelectedMap[widget.productType] !=
                        null &&
                    widget.staffIndex <
                        widget
                            .vm
                            .staffDropDownSelectedMap[widget.productType]!
                            .length) {
                  widget.vm.staffDropDownSelectedMap[widget.productType]![widget
                          .staffIndex] =
                      value;
                }

                (context as Element).markNeedsBuild();
              },
            ),
          ),
        );
      },
    );
  }

  ///
  Widget _baseCountBtn(VoidCallback onTap) {
    return BtnWithGreenShadow(
      onTap: onTap,
      child: Center(
        child: Icon(Icons.close_outlined, color: MyColors.grey_119_119_119),
      ),
    );
  }
}
