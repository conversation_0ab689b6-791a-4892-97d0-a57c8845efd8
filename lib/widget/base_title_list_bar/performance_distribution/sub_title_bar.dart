import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../resource/MyColor.dart';
import '../../../screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';
import '../../btn/btn_with_green_shadow.dart';
import '../../container_with_radius.dart';
import '../performance_distribution_info_base_title_bar.dart';

class SubTitleBar extends StatelessWidget {
  final ProductType productType;
  final PerformanceDistributionInfoScreenViewModel vm;
  final int productIndex;

  const SubTitleBar({
    super.key,
    required this.productType,
    required this.vm,
    required this.productIndex,
  });

  @override
  Widget build(BuildContext context) {
    return _subTitleBar();
  }

  //副標題列
  Widget _subTitleBar() {
    final textStyle = TextStyle(
      fontSize: 25.sp,
      color: MyColors.brown_57_54_18,
    );
    return ContainerWithRadius(
      w: double.infinity.w,
      h: 76.h,
      r: 0.r,
      color: _getSubTitleBarColor(),
      isHaveBorder: false,
      child: Stack(
        children: [
          SizedBox.expand(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 560.w),
                Text(
                  '員工編號',
                  style: textStyle.copyWith(color: _getSubTitleBarTextColor()),
                ),
                SizedBox(width: 262.w),
                Text(
                  '分配比例',
                  style: textStyle.copyWith(color: _getSubTitleBarTextColor()),
                ),
                SizedBox(width: 262.w),
                Text(
                  '分配金額',
                  style: textStyle.copyWith(color: _getSubTitleBarTextColor()),
                ),
              ],
            ),
          ),

          if (vm.isEdit(productType))
            Positioned(
              left: 450.w,
              top: 0,
              bottom: 0,
              child: Center(
                child: _baseCountBtn(() {
                  //insert資料
                  vm.insertProduct(productType, productIndex);
                }),
              ),
            ),
        ],
      ),
    );
  }

  ///
  Widget _baseCountBtn(VoidCallback onTap) {
    return BtnWithGreenShadow(
      onTap: onTap,
      child: Center(
        child: Transform.translate(
          offset: Offset(0, -5.h),
          child: Text(
            '+',
            style: TextStyle(
              fontSize: 50.w,
              color: MyColors.blue121_160_167,
              fontWeight: FontWeight.w700,
            ),
          ),
        ),
      ),
    );
  }

  //提取副標顏色
  Color _getSubTitleBarColor() {
    switch (productType) {
      case ProductType.product:
        return MyColors.blue222_240_242;
      case ProductType.classes:
        return MyColors.green_233_240_209;
      case ProductType.topUp:
        return MyColors.orange_236_218_199;
    }
  }

  Color _getSubTitleBarTextColor() {
    switch (productType) {
      case ProductType.product:
        return MyColors.blue122_160_167;
      case ProductType.classes:
        return MyColors.green_188_208_125;
      case ProductType.topUp:
        return MyColors.orange_229_165_104;
    }
  }
}
