import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../model/performance/performance_info_model.dart';
import '../../../resource/MyColor.dart';
import '../../../screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';
import '../../../util/StringUtils.dart';
import '../../container_with_radius.dart';
import '../performance_distribution_info_base_title_bar.dart';

//未編輯
class StaffListWidget extends StatelessWidget {
  final PerformanceMemberModel innerModel;
  final ProductType productType;
  final PerformanceDistributionInfoScreenViewModel vm;
  const StaffListWidget({super.key, required this.innerModel, required this.productType, required this.vm});

  @override
  Widget build(BuildContext context) {
    return _staffListWidget();
  }

  //業績人員列表元件
  Widget _staffListWidget(){
    final textStyle = TextStyle(fontSize: 25.sp,color: MyColors.brown_57_54_18);
    return ContainerWithRadius(  w: double.infinity.w,
      h: 76.h,
      r: 0.r,
      color: vm.getStaffListWidgetColor(productType),
      isHaveBorder: false,
      child: Row(
        children: [
          SizedBox(width: 578.w,),
          SizedBox(
            width: 100.w,
              child: Text(innerModel.staffName,style: textStyle,overflow: TextOverflow.ellipsis,)),
          SizedBox(width: 270.w,),
          SizedBox(
              width: 300.w,
              child: Text(innerModel.percent.toString(),style: textStyle,)),
          SizedBox(width: 50.w,),
          Text('\$ ${StringUtils.formatMoneyForDouble(innerModel.percentPrice)}',style: textStyle,),

        ],
      ),);

  }


}
