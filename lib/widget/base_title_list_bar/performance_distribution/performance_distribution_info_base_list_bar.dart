import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/base_title_list_bar/performance_distribution/staff_list_widget.dart';
import 'package:sbar_pos/widget/base_title_list_bar/performance_distribution/sub_title_bar.dart';
import 'package:sbar_pos/widget/base_title_list_bar/performance_distribution/title_bar.dart';
import 'package:sbar_pos/widget/base_title_list_bar/performance_distribution_info_base_title_bar.dart';
import '../../../model/performance/performance_info_model.dart';
import '../../../resource/MyColor.dart';
import '../../../screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';
import '../../btn/btn_with_green_shadow.dart';
import 'edit_staff_list_widget.dart';

class PerformanceDistributionInfoBaseListBar extends StatelessWidget {
  final ProductType productType;
  final List<PerformanceInfoModel> performanceInfoModels;
  final PerformanceDistributionInfoScreenViewModel vm;

  const PerformanceDistributionInfoBaseListBar({
    super.key,
    required this.productType,
    required this.performanceInfoModels,
    required this.vm,
  });

  @override
  Widget build(BuildContext context) {
    return _list();
  }

  //列表
  Widget _list() {
    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: performanceInfoModels.length,
      itemBuilder: (context, index) {
        final model = performanceInfoModels[index];
        return _listWidget(model, index);
      },
    );
  }

  //列表元件
  Widget _listWidget(PerformanceInfoModel model, int productIndex) {
    return Column(
      children: [
        TitleBar(model: model, vm: vm, type: productType, productIndex: productIndex,),
        SubTitleBar(productType: productType, vm: vm, productIndex: productIndex,),
        _staffList(model, productIndex),
      ],
    );
  }

  //業績人員列
  Widget _staffList(PerformanceInfoModel model, int productIndex) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: model.performanceMemberModel.length,
      itemBuilder: (context, index) {
        final innerModel = model.performanceMemberModel[index];
        return vm.isEdit(productType)
            ? EditStaffListWidget(
                innerModel: innerModel,
                performanceMemberModels: model.performanceMemberModel,
                productType: productType,
                vm: vm,
                staffIndex: index,
                productIndex: productIndex,
              )
            : StaffListWidget(
                vm: vm,
                innerModel: innerModel,
                productType: productType,
              );
      },
    );
  }
}
