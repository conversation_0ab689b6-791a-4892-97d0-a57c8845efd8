import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';

import '../../gen/r.dart';
import '../../screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';

enum ProductType { product, classes, topUp }

class PerformanceDistributionInfoBaseTitleBar extends StatelessWidget {
  final ProductType productType;

  const PerformanceDistributionInfoBaseTitleBar({
    super.key,
    required this.productType,
  });

  @override
  Widget build(BuildContext context) {
    final textStyle = TextStyle(color: Colors.white,fontSize: 25.sp);
    return ContainerWithRadius(
      w: double.infinity.w,
      h: 76.h,
      r: 0.r,
      color: _getColor(),
      isHaveBorder: false,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(width: 57.w,),
          Text('SKU編碼',style: textStyle,),
          SizedBox(width: 57.w,),

          SvgPicture.asset(
            _getImage(),
            width: 30.w,
            height: 30.w,
            colorFilter: ColorFilter.mode(
               Colors.white,
              BlendMode.srcIn,
            ),
          ),

          SizedBox(width: 12.w,),
          SizedBox(
            width: 180.w,
              child: Text(_getType(),style: textStyle.copyWith(fontWeight: FontWeight.w600),)),
          SizedBox(width: 550.w,),
          Text('購買數量',style: textStyle,),
          SizedBox(width: 75.w,),
          Text('原售價',style: textStyle,),
          SizedBox(width: 55.w,),
          Text('實售小計',style: textStyle,),
          SizedBox(width: 85.w,),
          Text('實售價',style: textStyle,),
        ],
      ),
    );
  }

  ///提取顏色
  Color _getColor() {
    switch (productType) {
      case ProductType.product:
       return MyColors.blue121_160_167;
      case ProductType.classes:
        return  MyColors.green_157_193_65;
      case ProductType.topUp:
        return  MyColors.orange_240_149_68;
    }

  }

  ///提取對應種類名稱
  String _getType() {
    switch (productType) {
      case ProductType.product:
      return '商品名稱';
      case ProductType.classes:
        return  '課程名稱';
      case ProductType.topUp:
        return  '儲值金名稱';
    }
  }

  ///提取對應icon
  String _getImage(){
    switch (productType) {
      case ProductType.product:
        return  R.image.icon_product();
      case ProductType.classes:
        return  R.image.icon_class();
      case ProductType.topUp:
        return   R.image.icon_top_up();
    }
  }
}
