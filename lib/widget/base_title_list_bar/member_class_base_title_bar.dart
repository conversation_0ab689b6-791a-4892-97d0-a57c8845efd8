import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class MemberClassBaseTitleBar extends StatelessWidget {
  const MemberClassBaseTitleBar({super.key});

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    return ContainerWithRadius(
      w: 1648.w,
      h: 120.h,
      r: 0.r,
      color: MyColors.grey_247_247_247,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 37.w, right: 51.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [

            ///訂單編號
            SizedBox(
              width: 158.w,
              child: Text('訂單編號', style: titleStyle),
            ),
            SizedBox(width: 57.w),

            ///結帳時間
            SizedBox(
              width: 146.w,
              child: Text('結帳時間', style: titleStyle),
            ),

            SizedBox(width: 46.w),

            ///課程名稱
            SizedBox(
              width: 650.w,
              child: Text('課程名稱', style: titleStyle),
            ),
          ],
        ),
      ),
    );
  }
}
