import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';
import '../shape/circle.dart';

class PerformancePersonalBaseListBar extends StatelessWidget {
  final String staffId; //員工編號
  final String shopId; //店編號
  final String staffName; //員工姓名
  final bool isSelected;
  final Color backgroundColor;
  final VoidCallback onTap;
  final bool isTextCenter;

  const PerformancePersonalBaseListBar({
    super.key,
    required this.staffId,
    required this.shopId,
    required this.staffName,
    required this.isSelected,
    required this.backgroundColor,
    required this.onTap,
    required this.isTextCenter,
  });

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return InkWell(
      onTap: onTap,
      child: ContainerWithRadius(
        w: double.infinity.w,
        h: 76.h,
        r: 0.r,
        color: backgroundColor,
        isHaveBorder: true,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            SizedBox(width: 126.w),

            ///員工編號
            SizedBox(
              width: 112.w,
              child: isTextCenter
                  ? Center(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(staffId, style: titleStyle),
                      ),
                    )
                  : FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(staffId, style: titleStyle),
                    ),
            ),

            SizedBox(width: 112.w),

            ///店編號
            SizedBox(
              width: 84.w,
              child: isTextCenter
                  ? Center(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(shopId, style: titleStyle),
                      ),
                    )
                  : FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(shopId, style: titleStyle),
                    ),
            ),

            SizedBox(width: 179.w),

            ///員工姓名
            SizedBox(
              width: 112.w,
              child: isTextCenter
                  ? Center(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(staffName, style: titleStyle),
                      ),
                    )
                  : FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(staffName, style: titleStyle),
                    ),
            ),

            Spacer(),
            if (isSelected)
              Circle(
                color: MyColors.yellow_251_227_140,
                size: 40.w,
                child: Icon(Icons.check, color: MyColors.green_129_128_94),
              ),

            SizedBox(width: 75.w),
          ],
        ),
      ),
    );
  }
}
