import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../resource/MyColor.dart';
import '../../container_with_radius.dart';

class ClassConfirmBaseTitleBar extends StatelessWidget {
  const ClassConfirmBaseTitleBar({super.key});

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    return ContainerWithRadius(
      w: double.infinity.w,
      h: 120.h,
      r: 0.r,
      color: MyColors.grey_247_247_247,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 90.w, right: 51.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [

            ///訂單編號
            SizedBox(
              width: 158.w,
              child: Text('訂單編號', style: titleStyle),
            ),
            SizedBox(width: 57.w),

            ///會員
            SizedBox(
              width: 170.w,
              child: Text('會員', style: titleStyle),
            ),

            SizedBox(width: 70.w),

            ///課程商品編號、預約單邊號
            SizedBox(
              width: 200.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('課程商品編號', style: titleStyle),
                  Text('預約單編號', style: titleStyle),
                ],
              ),
            ),

            SizedBox(width: 46.w),

            ///課程名稱、預約課程時間
            SizedBox(
              width: 650.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('課程名稱', style: titleStyle),
                  Text('預約課程時間', style: titleStyle),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
