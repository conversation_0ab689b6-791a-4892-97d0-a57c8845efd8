import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import '../../../model/class/class_confirm_model.dart';
import '../../../resource/MyColor.dart';

class ClassConfirmBaseListBar extends StatelessWidget {
  final String orderId;
  final String memberName;
  final String memberPhone;
  final bool isAllSign;
  final List<ClassConfirmInfo> classConfirmInnerInfo;
  final List<ClassConfirmModel> classConfirmModel;

  const ClassConfirmBaseListBar({
    super.key,
    required this.orderId,
    required this.memberName,
    required this.memberPhone,
    required this.classConfirmInnerInfo,
    required this.classConfirmModel,
    required this.isAllSign,
  });

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 30.sp,
      color: MyColors.green_129_128_94,
    );

    return Container(
      width: double.infinity.w,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: MyColors.grey_235_235_235, width: 0.5),
      ),
      child: Padding(
        padding: EdgeInsets.only(left: 37.w, top: 24.5.h, bottom: 24.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///訂單編號
            SizedBox(
              width: 220.w,
              child: Column(
                children: [
                  FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(orderId, style: titleStyle),
                  ),

                  SizedBox(height: 4.h),

                  ContainerWithRadius(
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0x14000000),
                        offset: const Offset(3, 6),
                        blurRadius: 10.r,
                      ),
                    ],
                    w:200.w,
                    h: 50.h,
                    r: 20.r,
                    color: Colors.white,
                    isHaveBorder: true,
                    child: Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ContainerWithRadius(
                            onTap: (){
                              //TODO:課程確認書
                            },
                            w: 70.w,
                            h: 30.h,
                            r: 20.r,
                            color: _getStatueColor(isAllSign),
                            isHaveBorder: true,
                            child: Center(
                              child: Text(
                                _getStatue(isAllSign),
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18.sp,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 4.w),
                          Text(
                            '一次簽核',
                            style: titleStyle.copyWith(fontSize: 20.sp),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(width: 57.w),

            ///會員
            SizedBox(
              width: 170.w,
              child: FittedBox(
                fit: BoxFit.scaleDown,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      memberName,
                      style: titleStyle.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 30,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(memberPhone, style: titleStyle),
                  ],
                ),
              ),
            ),

            SizedBox(width: 70.w),

            Expanded(
              child: _list(),
            ),
          ],
        ),
      ),
    );
  }

  /// 列表
  Widget _list() {
    return Column(
      children: List.generate(classConfirmInnerInfo.length, (index) {
        final info = classConfirmInnerInfo[index];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 顯示課程標題 (classId + className)
            Padding(
              padding: EdgeInsets.only(bottom: 16.h),
              child: Row(
                children: [
                  SizedBox(
                    width: 200.w,
                    child: Text(
                      info.classId,
                      style: TextStyle(
                        fontSize: 28.sp,
                        color: MyColors.green_129_128_94,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  SizedBox(width: 30.w),
                  SizedBox(
                    width: 550.w,
                    child: Text(
                      info.className,
                      style: TextStyle(
                        fontSize: 28.sp,
                        color: MyColors.brown_57_54_18,
                        overflow: TextOverflow.ellipsis
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 顯示該課程底下的所有預約紀錄
            Column(
              children: List.generate(info.classConfirmInnerInfo.length, (subIndex) {
                final inner = info.classConfirmInnerInfo[subIndex];

                return Column(
                  children: [
                    _baseListWidget(inner),
                    if (subIndex != info.classConfirmInnerInfo.length - 1)
                     SizedBox(height: 6.h,)
                  ],
                );
              }),
            ),

            // 課程之間分隔線
            if (index != classConfirmInnerInfo.length - 1)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 24.h),
                child: Divider(thickness: 2, color: MyColors.grey_235_235_235),
              ),
          ],
        );
      }),
    );
  }


  ///base list reserve_calender_widget
  Widget _baseListWidget(ClassConfirmInnerInfo model) {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 預約單編號
        SizedBox(
          width: 200.w,
          child: Text(
            model.reserveId,
            style: titleStyle.copyWith(fontSize: 25.sp),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 30.w),

        // 預約時間
        SizedBox(
          width: 550.w,
          child: Text(
            model.reserveClassTime,
            style: titleStyle.copyWith(
              fontSize: 25.sp,
              color: MyColors.brown_57_54_18,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 90.w),

        // 確認書按鈕
        _confirmBtn(titleStyle, model.isSign),
      ],
    );
  }


  ///課程確認書按鈕
  Widget _confirmBtn(TextStyle titleStyle,bool isSign) {
    return ContainerWithRadius(
      onTap: (){
        //TODO:課程確認書
      },
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      w: 230.w,
      h: 60.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Center(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('課程確認書', style: titleStyle.copyWith(fontSize: 24.sp)),
            SizedBox(width: 4.w),
            ContainerWithRadius(
              w: 70.w,
              h: 30.h,
              r: 20.r,
              color: _getStatueColor(isSign),
              isHaveBorder: true,
              child: Center(
                child: Text(
                  _getStatue(isSign),
                  style: TextStyle(color: Colors.white, fontSize: 18.sp),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///提取確認書狀態
  String _getStatue(bool isSign) {
    if (isSign) {
      return '已簽';
    } else {
      return '未簽';
    }
  }

  ///提取確認書狀態顏色
  Color _getStatueColor(bool isSign) {
    if (isSign) {
      return MyColors.green_157_193_65;
    } else {
      return MyColors.brown_197_186_165;
    }
  }
}
