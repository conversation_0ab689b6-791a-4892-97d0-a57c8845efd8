import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class InventoryManagementBaseListBar extends StatelessWidget {
  final bool isTitleBar;
  final String shopName; //營業店編號
  final String productType; //商品類別
  final String productName; //商品名稱
  // final String skuCode; //SKU編碼
  final String countryCode; //國際條碼
  final int count; //庫存數量
  final String countTitle; //庫存數量
  final Color backgroundColor;

  const InventoryManagementBaseListBar({super.key, required this.shopName, required this.productType, required this.productName,  required this.countryCode, required this.count, required this.isTitleBar, required this.backgroundColor, required this.countTitle});

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );



    return ContainerWithRadius(
      w: double.infinity.w,
      h: 76.h,
      r: 0.r,
      color: backgroundColor,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 58.w, right: 51.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [

            ///營業店編號
            SizedBox(
              width: (151+50).w,
              child:  Text(shopName, style: titleStyle,overflow: TextOverflow.ellipsis,)

            ),
            SizedBox(width: (165-50).w),

            ///商品類別
            SizedBox(
              width: (184+50).w,
              child:  Text(productType, style: titleStyle,overflow: TextOverflow.ellipsis,)

            ),
            SizedBox(width: (103-50).w),

            ///商品名稱
            SizedBox(
                width: (578+101+30).w,
                child:  Text(productName, style: isTitleBar ? titleStyle : titleStyle.copyWith(color: MyColors.brown_57_54_18),overflow: TextOverflow.ellipsis,)

            ),
            SizedBox(width: 40.w),
            Spacer(),



            ///國際條碼
            SizedBox(
                width: (181+30).w,
                child:  FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(countryCode, style: titleStyle))

            ),
            SizedBox(width: 40.w),

            ///庫存數量
            SizedBox(
                width: (100+30).w,
                child:
                isTitleBar ?
                Text(countTitle, style:  titleStyle)
                    :
                Align(
                  alignment: Alignment.centerRight,
                    child: Text('$count', style: count <= 0 ? titleStyle.copyWith(color: MyColors.red_230_75_75) : titleStyle))



            ),
          ],
        ),
      ),
    );
  }


}
