import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class MemberReserveOrderBaseListBar extends StatelessWidget {
  final bool isTitleBar;
  final String orderId; //訂單編號
  final String reserveId; //預約單編號
  final String orderTime; //預約下單時間
  final String orderClassTime; //預約課程時間
  final String className; //課程名稱
  final String classNameInfo; //課程名稱詳細說明(ex:濕潤美肌課程(75分鐘))
  final String memberName; //會員
  final String memberPhone; //會員手機
  final String beautician; //美容師
  final String customerSelectionType; //指定情況(ex:客戶指定)
  final String orderStatue; //訂單狀態
  final String orderStatueInfo; //訂單狀態說明(暫定：1 線上，2 POS)
  final Color backgroundColor;
  final VoidCallback onTap;

  const MemberReserveOrderBaseListBar({
    super.key,
    required this.isTitleBar,
    required this.orderId,
    required this.reserveId,
    required this.orderTime,
    required this.orderClassTime,
    required this.className,
    required this.memberName,
    required this.memberPhone,
    required this.beautician,
    required this.customerSelectionType,
    required this.orderStatue,
    required this.orderStatueInfo,
    required this.backgroundColor,
    required this.onTap,
    required this.classNameInfo,
  });

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    //會員姓名
    final memberTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w500,
    );

    return InkWell(
      onTap: onTap,
      child: ContainerWithRadius(
        w: double.infinity.w,
        h: 120.h,
        r: 0.r,
        color: backgroundColor,
        isHaveBorder: true,
        child: Padding(
          padding: EdgeInsets.only(left: 58.w, right: 51.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 158.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///訂單編號
                    isTitleBar
                        ? Text(orderId, style: titleStyle)
                        : FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(orderId, style: titleStyle),
                    ),

                    ///預約單編號
                    isTitleBar
                        ? Text(reserveId, style: titleStyle)
                        : FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(reserveId, style: titleStyle),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 57.w),

              SizedBox(
                width: 311.w,
                child: isTitleBar
                    ? Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///預約下單時間
                    Text(orderTime, style: titleStyle),

                    ///預約課程時間
                    Text(orderClassTime, style: titleStyle),
                  ],
                )
                    : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///預約下單時間
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(orderTime, style: titleStyle),
                    ),

                    ///預約課程時間
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        orderClassTime,
                        style: titleStyle.copyWith(
                          color: MyColors.brown_57_54_18,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 102.w),

              ///課程名稱
              isTitleBar
                  ? SizedBox(
                width: 270.w,
                child: Text(className, style: titleStyle),
              )
                  : SizedBox(
                width: 270.w,
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ///課程名稱
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(className, style: titleStyle),
                      ),

                      ///課程名稱說明
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          classNameInfo,
                          style: titleStyle.copyWith(
                            color: MyColors.brown_57_54_18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(width: 120.w),

              ///會員
              isTitleBar
                  ? SizedBox(
                width: 139.w,
                child: Text(memberName, style: titleStyle),
              )
                  : SizedBox(
                width: 139.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///會員姓名
                    Text(memberName, style: memberTextStyle),

                    ///會員電話
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        memberPhone,
                        style: titleStyle.copyWith(fontSize: 25.sp),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 134.w),

              ///美容師
              SizedBox(
                width: 92.w,
                child: isTitleBar
                    ? Text(beautician, style: titleStyle)
                    : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///美容師姓名
                    Text(beautician, style: memberTextStyle),

                    ///指定情況
                    FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        customerSelectionType,
                        style: titleStyle.copyWith(
                          fontSize: 23.sp,
                          color: MyColors.red_230_75_75,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 146.w),

              ///訂單情況
              SizedBox(
                width: 112.w,
                child: isTitleBar
                    ?   Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///訂單狀態
                    Text(orderStatue, style: titleStyle),

                    ///預約來源
                    Text(_getReserveText(), style: titleStyle),
                  ],
                )
                    : FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ///訂單狀態
                      Text(
                        orderStatue,
                        style: memberTextStyle.copyWith(
                          color: MyColors.brown_57_54_18,
                        ),
                      ),

                      ///訂單狀態說明
                      FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                            _getReserveText(),
                            style: _getReserveTextColor(titleStyle)
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              Spacer(),
              if(isTitleBar)
                Opacity(
                    opacity: 0,
                    child: Icon(Icons.arrow_forward_ios_sharp, size: 20.w)),

              if(!isTitleBar)
                Icon(Icons.arrow_forward_ios_sharp, size: 20.w),
            ],
          ),
        ),
      ),
    );
  }

  //提取預約來源顏色
  TextStyle _getReserveTextColor(TextStyle titleStyle){
    if(orderStatueInfo == '1'){
      return titleStyle.copyWith(
        fontSize: 23.sp,
        color: MyColors.blue101_196_213,
      );
    }
    if(orderStatueInfo == '2'){
      return titleStyle.copyWith(
        fontSize: 23.sp,
        color: MyColors.orange_240_149_68,
      );
    }
    return titleStyle;
  }

  //提取預約來源
  String _getReserveText(){
    if(orderStatueInfo == '1'){
      return '線上';
    }
    if(orderStatueInfo == '2'){
      return 'POS';
    }
    return '預約來源';
  }
}
