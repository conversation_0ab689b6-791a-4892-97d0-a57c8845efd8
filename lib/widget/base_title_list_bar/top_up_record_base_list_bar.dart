import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../container_with_radius.dart';

class TopUpRecordBaseListBar extends StatelessWidget {
  final bool isTitleBar;
  final String orderId; //訂單編號
  final String cashId; //發票號碼
  final String checkOutTime; //結帳時間
  final String somPrice; //銷售訂單母單
  final String price; //金額
  final String useFor; //用途
  final String walletName; //錢包名稱
  final String memberName; //會員
  final String operatorName; //操作員
  final Color backgroundColor;
  final VoidCallback onTap;
  final bool? isPriceMinus;

  const TopUpRecordBaseListBar({
    super.key,
    required this.isTitleBar,
    required this.orderId,
    required this.cashId,
    required this.checkOutTime,
    required this.somPrice,
    required this.price,
    required this.useFor,
    required this.walletName,
    required this.memberName,
    required this.operatorName,
    required this.backgroundColor,
    required this.onTap,
    this.isPriceMinus,
  });

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    final userForTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
    );
    final memberTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w500,
    );

    return InkWell(
      onTap: onTap,
      child: ContainerWithRadius(
        w: double.infinity.w,
        h: 120.h,
        r: 0.r,
        color: backgroundColor,
        isHaveBorder: true,
        child: Padding(
          padding: EdgeInsets.only(left: 58.w, right: 51.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 165.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///訂單編號
                    isTitleBar
                        ? Text(orderId, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(orderId, style: titleStyle),
                          ),

                    ///儲值編號
                    isTitleBar
                        ? Text(cashId, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(cashId, style: titleStyle),
                          ),
                  ],
                ),
              ),

              SizedBox(width: 41.w),

              ///結帳時間
              SizedBox(
                width: 224.w,
                child: isTitleBar
                    ? Text(checkOutTime, style: titleStyle)
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(checkOutTime, style: titleStyle),
                      ),
              ),

              SizedBox(width: 68.w),

              ///銷售訂單母單
              isTitleBar
                  ? SizedBox(
                      width: 168.w,
                      child: Text(somPrice, style: titleStyle),
                    )
                  : SizedBox(
                      width: 168.w,
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(somPrice) ?? 0)}',
                          style: titleStyle,
                        ),
                      ),
                    ),

              SizedBox(width: 91.w),

              ///金額
              isTitleBar
                  ? SizedBox(
                      width: 120.w,
                      child: FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(price, style: titleStyle)),
                    )
                  : SizedBox(
                      width: 120.w,
                      child: (isPriceMinus ?? false)
                          ? FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                '-\$ ${StringUtils.formatMoneyForDouble(double.tryParse(price) ?? 0)}',
                                style: titleStyle.copyWith(
                                  color: MyColors.red_230_75_75,
                                ),
                              ),
                            )
                          : FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(price) ?? 0)}',
                                style: titleStyle,
                              ),
                            ),
                    ),

              SizedBox(width: 50.w),

              ///用途
              SizedBox(
                width: 168.w,
                child: isTitleBar
                    ? Text(useFor, style: titleStyle)
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(useFor, style: userForTextStyle),
                      ),
              ),

              SizedBox(width: 89.w),

              ///錢包名稱
              SizedBox(
                width: 159.w,
                child: isTitleBar
                    ? Text(walletName, style: titleStyle)
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(walletName, style: titleStyle),
                      ),
              ),

              Spacer(),

              SizedBox(
                width: 84.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///會員
                    isTitleBar
                        ? Text(memberName, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(memberName, style: memberTextStyle),
                          ),

                    ///操作員
                    isTitleBar
                        ? Text(operatorName, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(operatorName, style: titleStyle),
                          ),
                  ],
                ),
              ),


              SizedBox(width: 63.w),
              if(!isTitleBar)
              Icon(Icons.arrow_forward_ios_sharp, size: 20.w),

              if(isTitleBar)
                Opacity(
                  opacity: 0,
                    child: Icon(Icons.arrow_forward_ios_sharp, size: 20.w)),

            ],
          ),
        ),
      ),
    );
  }
}
