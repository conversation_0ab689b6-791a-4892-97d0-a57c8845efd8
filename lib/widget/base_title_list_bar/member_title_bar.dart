import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class MemberTitleBar extends StatelessWidget {
  const MemberTitleBar({super.key});

  @override
  Widget build(BuildContext context) {
    return _memberTitleBar();
  }

  Widget _memberTitleBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    final int testPadding = 50;
    return Row(

      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(width: 80.w,),

        ///會員姓名
        SizedBox(
          width: 112.w,
          child: Center(child: Text('會員姓名', style: titleStyle)),
        ),

        ///手機
        SizedBox(
          width: (156 + 43.85 + testPadding).w,
          child: Center(child: Text('手機', style: titleStyle)),
        ),

        ///E-mail
        SizedBox(
          width: (192 + 35.97 + testPadding).w,
          child: Center(child: Text('E-mail', style: titleStyle)),
        ),

        ///生日
        SizedBox(
          width: (146 + 35.27 + testPadding).w,
          child: Center(child: Text('生日', style: titleStyle)),
        ),
      ],
    );
  }
}
