import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../gen/r.dart';
import '../../model/member/member_list_model.dart';
import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../screen/member/member_info/member_info_main_screen.dart';
import '../../screen/start/my_app.dart';
import '../../util/MyRoutes.dart';
import '../cached_network_image/error_cached_network_image.dart';
import '../shape/circle.dart';

class MemberListWidget extends StatefulWidget {
  final MemberListModel model;
  final VoidCallback onTap;
  final bool isSearchMemberScreen;
  final int index;
  final bool? isSelected;

  const MemberListWidget({
    super.key,
    required this.model,
    required this.onTap,
    required this.isSearchMemberScreen,
    required this.index,
    this.isSelected,
  });

  @override
  State<MemberListWidget> createState() => _MemberListWidgetState();
}

class _MemberListWidgetState extends State<MemberListWidget> {
  @override
  Widget build(BuildContext context) {
    return _memberListWidget();
  }

  ///會員列表元件
  Widget _memberListWidget() {
    final int testPadding = 50;
    final nameStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w700,
      overflow: TextOverflow.ellipsis,
    );
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
      overflow: TextOverflow.ellipsis,
    );
    return InkWell(
      onTap: widget.onTap,
      child: Container(
        height: 116.h,
        width: double.infinity.w,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: MyColors.grey_235_235_235, width: 1),
        ),
        child: Row(
          children: [
            SizedBox(width: 80.w,),
            // ///大頭照
            // SizedBox(
            //   width: 145.w,
            //   child: Center(
            //     child: ClipRRect(
            //       borderRadius: BorderRadius.circular(50.r),
            //       child: CachedNetworkImage(
            //         imageUrl: widget.model.avatar,
            //         height: 90.w,
            //         width: 90.w,
            //         fit: BoxFit.cover,
            //         errorWidget: (context, url, error) =>
            //             const ErrorCachedNetworkImage(),
            //       ),
            //     ),
            //   ),
            // ),

            ///會員姓名
            SizedBox(
              width: 112.w,
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(widget.model.name, style: nameStyle),
                ),
              ),
            ),

            ///手機
            SizedBox(
              width: (156 + 43.85 + testPadding).w,
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(widget.model.phone, style: textStyle),
                ),
              ),
            ),

            ///E-mail
            SizedBox(
              width: (192 + 35.97 + testPadding).w,
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(widget.model.mail, style: textStyle),
                ),
              ),
            ),

            ///生日
            SizedBox(
              width: (146 + 35.27 + testPadding).w,
              child: Center(
                child: FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(widget.model.birthday, style: textStyle),
                ),
              ),
            ),

            Spacer(),

            ///搜尋會員
            if (widget.isSearchMemberScreen)
              if (widget.isSelected ?? false)
                Padding(
                  padding: EdgeInsets.only(right: 75.w),
                  child: Circle(
                    color: MyColors.yellow_251_227_140,
                    size: 40.w,
                    child: Icon(Icons.check, color: MyColors.green_129_128_94),
                  ),
                ),

            ///品牌會員
            if (!widget.isSearchMemberScreen)
              Row(
                children: [
                  Image(image: R.image.icon_shopping_car(),width: 40.w,height: 40.w,),
                  SizedBox(width: 90.w),

                  Icon(Icons.arrow_forward_ios_rounded, color: Colors.grey),
                  SizedBox(width: 48.04.w),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
