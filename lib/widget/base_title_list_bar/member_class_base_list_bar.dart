import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/widget/btn/member_base_btn.dart';

import '../../model/member/member_class_model.dart';
import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class MemberClassBaseListBar extends StatelessWidget {
  final String type;
  final String orderId;
  final String checkOutDate;
  final String checkOutTime;
  final List<ClassInnerInfo> classInnerInfo;
  final List<MemberClassModel> memberClassModel;

  const MemberClassBaseListBar({
    super.key,
    required this.type,
    required this.orderId,
    required this.checkOutDate,
    required this.checkOutTime,
    required this.classInnerInfo,
    required this.memberClassModel,
  });

  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    return Container(
      width: 1648.w,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(
          color:  MyColors.grey_235_235_235 ,
          width: 0.5,
        ),
      ),
      child:

      Padding(
        padding:  EdgeInsets.only(left: 37.w,top: 24.5.h,bottom: 24.h),
        child:


        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [

            ///訂單編號
            SizedBox(
                width: 158.w,
                child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(orderId,style: titleStyle,))),

            SizedBox(width: 57.w),

            ///結帳時間
            SizedBox(
              width: 146.w,
              child:
              FittedBox(
                fit: BoxFit.scaleDown,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(checkOutDate,style: titleStyle,),
                    Text(checkOutTime,style: titleStyle,),
                  ],
                ),
              )

            ),

            SizedBox(width: 46.w),

            SizedBox(
                width: (650+91+178+62+196).w,
                child: _list())
          ],
        ),
      ),
    );

  }

  /// 課程列表
  Widget _list() {
    return Column(
      children: List.generate(classInnerInfo.length, (index) {
        final model = classInnerInfo[index];
        return Column(
          children: [
            _baseListWidget(model),
            if (index != classInnerInfo.length - 1)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 24.5.h),
                child: Divider(thickness: 1, height: 1),
              ),
          ],
        );
      }),
    );
  }



  ///base list reserve_calender_widget
  Widget _baseListWidget(ClassInnerInfo model) {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        /// 左側課程名稱、說明
        SizedBox(
          width: 650.w,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                model.className,
                style: titleStyle.copyWith(color: MyColors.brown_57_54_18),
              ),
              SizedBox(height: 8.h),
              Text(
                model.classNameInfo,
                style: titleStyle.copyWith(fontSize: 25.sp),
                overflow: TextOverflow.ellipsis,

              ),
            ],
          ),
        ),


        Spacer(),


        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //已購買堂數
                Text(
                  '已購買堂數：${model.purchasedClass}',
                  style: titleStyle.copyWith(fontSize: 25.sp),
                ),
                SizedBox(height: 8.h),

                //剩餘堂數
                Text(
                  '剩餘堂數：${model.remainingClass}',
                  style: titleStyle.copyWith(
                    fontSize: 25.sp,
                    color: MyColors.orange_240_149_68,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8.h),

                //已付款堂數
                Text(
                  '已付款堂數：${model.paidClass}',
                  style: titleStyle.copyWith(
                    fontSize: 25.sp,
                    color: MyColors.green_157_193_65,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 8.h),

                //未付款堂數
                Text(
                  '未付款堂數：${model.unPaidClass}',
                  style: titleStyle.copyWith(
                    fontSize: 25.sp,
                    color: MyColors.blue101_196_213,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            SizedBox(width: 62.w),
            MemberBaseBtn(title: '預約課程', onTap: () {}, w: 196.w),
            SizedBox(width: 27.w),
          ],
        ),
      ],
    );
  }

}




