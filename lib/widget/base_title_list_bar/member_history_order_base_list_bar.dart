import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/widget/invoice/edit_invoice_screen.dart';
import '../../gen/r.dart';
import '../../resource/MyColor.dart';
import '../../screen/history_order/history_info/history_info_screen_view_model.dart';
import '../../util/StringUtils.dart';
import '../container_with_radius.dart';
import '../dialog/MyDialog.dart';

class MemberHistoryOrderBaseListBar extends StatefulWidget {
  final HistoryInfoScreenViewModel? vm;
  final int? index;
  final bool isTitleBar;
  final String orderId; //訂單編號
  final String invoiceId; //發票號碼
  final String checkOutTime; //結帳時間
  final String somPrice; //銷售訂單母單
  final String price; //本次付款金額
  final String priceInfo; //本次付款金額(說明)
  final String orderStatue; //訂單狀態
  final int orderStatueCode; //訂單狀態(暫定：０為未完成付款，１為其他，２為已銷貨退回)
  final String unPaidPrice; //未付款金額
  final String memberName; //會員
  final String operatorName; //操作員
  final Color backgroundColor;
  final VoidCallback onTap;
  final bool? isHaveOnTap;
  final bool? isHaveEdit;

  const MemberHistoryOrderBaseListBar({
    super.key,

    required this.isTitleBar,
    required this.orderId,
    required this.invoiceId,
    required this.checkOutTime,
    required this.somPrice,
    required this.price,
    required this.priceInfo,
    required this.orderStatue,
    required this.orderStatueCode,
    required this.unPaidPrice,
    required this.memberName,
    required this.operatorName,
    required this.backgroundColor,
    required this.onTap,
    this.isHaveOnTap,
    this.isHaveEdit,
    this.vm, this.index,
  });

  @override
  State<MemberHistoryOrderBaseListBar> createState() => _MemberHistoryOrderBaseListBarState();
}

class _MemberHistoryOrderBaseListBarState extends State<MemberHistoryOrderBaseListBar> {
  @override
  Widget build(BuildContext context) {
    return _baseListBar();
  }

  Widget _baseListBar() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    //未付款金額（標頭）
    final unPaidTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.grey_220_220_213,
    );

    final memberTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w700,
    );

    return InkWell(
      onTap: widget.isTitleBar ? () {} : widget.onTap,
      child: ContainerWithRadius(
        w: double.infinity.w,
        h: 120.h,
        r: 0.r,
        color: widget.backgroundColor,
        isHaveBorder: true,
        child: Padding(
          padding: EdgeInsets.only(left: 58.w, right: 51.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: 220.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///訂單編號
                    widget.isTitleBar
                        ? Text(widget.orderId, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(widget.orderId, style: titleStyle),
                          ),

                    ///發票號碼
                    widget.isTitleBar
                        ? Text(widget.invoiceId, style: titleStyle)
                        : (widget.isHaveEdit ?? false)
                        ? Row(
                            children: [
                              SizedBox(
                                width: 170.w,
                                  child: Text(widget.invoiceId, style: titleStyle,overflow: TextOverflow.ellipsis,)),
                              SizedBox(width: 10.w),
                              InkWell(
                                onTap: ()async{

                                  _editInvoiceScreenDialog();

                                },
                                child: Image(
                                  image: R.image.icon_edit(),
                                  width: 40.w,
                                  height: 40.w,
                                ),
                              ),
                            ],
                          )
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(widget.invoiceId, style: titleStyle),
                          ),
                  ],
                ),
              ),

              SizedBox(width: 50.w),

              ///結帳時間
              SizedBox(
                width: 224.w,
                child: widget.isTitleBar
                    ? Text(widget.checkOutTime, style: titleStyle)
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(widget.checkOutTime, style: titleStyle),
                      ),
              ),

              SizedBox(width: 60.w),

              ///銷售訂單總金額
              widget.isTitleBar
                  ? SizedBox(
                      width: 200.w,
                      child: Text(widget.somPrice, style: titleStyle),
                    )
                  : SizedBox(
                      width: 200.w,
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(widget.somPrice) ?? 0)}',
                          style: titleStyle,
                        ),
                      ),
                    ),

              SizedBox(width: 80.w),

              ///本次付款金額
              widget.isTitleBar
                  ? SizedBox(
                      width: 207.w,
                      child: Text(widget.price, style: titleStyle),
                    )
                  : SizedBox(
                      width: 207.w,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(widget.price) ?? 0)}',
                            style: titleStyle,
                          ),
                          Text(
                            widget.priceInfo,
                            overflow: TextOverflow.ellipsis,
                            style: titleStyle,
                          ),
                        ],
                      ),
                    ),

              Spacer(),

              ///訂單狀態
              SizedBox(
                width: 180.w,
                child: widget.isTitleBar
                    ? Text(
                        widget.orderStatue,
                        style: titleStyle,
                        overflow: TextOverflow.ellipsis,
                      )
                    : Text(
                        widget.orderStatue,
                        overflow: TextOverflow.ellipsis,
                        style: _getOrderStatueTextStyle(),
                      ),
              ),

              SizedBox(width: 20.w),

              ///未付款金額
              SizedBox(
                width: 187.w,
                child: widget.isTitleBar
                    ? Row(
                        children: [
                          Icon(
                            Icons.lock_outline,
                            color: MyColors.grey_220_220_213,
                          ),
                          Text(widget.unPaidPrice, style: unPaidTextStyle),
                        ],
                      )
                    : FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '\$ ${StringUtils.formatMoneyForDouble(double.tryParse(widget.unPaidPrice) ?? 0)}',
                          style: widget.orderStatueCode == 0
                              ? titleStyle.copyWith(
                                  color: MyColors.red_230_75_75,
                                )
                              : titleStyle,
                        ),
                      ),
              ),

              SizedBox(width: 57.w),

              SizedBox(
                width: 84.w,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///會員
                    widget.isTitleBar
                        ? Text(widget.memberName, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(widget.memberName, style: memberTextStyle),
                          ),

                    ///操作員
                    widget.isTitleBar
                        ? Text(widget.operatorName, style: titleStyle)
                        : FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Text(widget.operatorName, style: titleStyle),
                          ),
                  ],
                ),
              ),

              if ((widget.isHaveOnTap ?? true))
                SizedBox(width: widget.isTitleBar ? (140 - 50).w : 63.w),

              if (!widget.isTitleBar && (widget.isHaveOnTap ?? true))
                Icon(Icons.arrow_forward_ios_sharp, size: 20.w),
            ],
          ),
        ),
      ),
    );
  }

  //訂單狀態(暫定：０為未完成付款，１為其他，２為已銷貨退回)
  TextStyle _getOrderStatueTextStyle() {
    final baseStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    switch (widget.orderStatueCode) {
      case 0:
        return baseStyle.copyWith(color: MyColors.red_230_75_75);
      case 1:
        return baseStyle;
      case 2:
        return baseStyle.copyWith(color: MyColors.orange_240_149_68);
      default:
        return baseStyle;
    }
  }

  ///開啟編輯發票彈窗
  void _editInvoiceScreenDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 537.w, vertical: 341.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
                isHaveBorder: true,
                w: double.infinity.w,
                h: double.infinity.h,
                r: 20.r,
                color: Colors.white,
                child: EditInvoiceScreen(initialInvoice: widget.invoiceId, onSubmit: (value){
                  //TODO:編輯發票號碼api
                  context.read<HistoryInfoScreenViewModel>().updateInvoiceId(widget.index ?? -1, value);

                })
            ),
          ),
        ),
      ),
    );
  }
}
