import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class MemberInfoBar extends StatelessWidget {
  final String title;
  const MemberInfoBar({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return  _topBar();
  }

  Widget _topBar() {
    return ContainerWithRadius(
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      isHaveBorder: false,
      child: Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: EdgeInsets.only(left: 25.w),
          child: Text(
            title,
            style: TextStyle(fontSize: 32.sp, color: MyColors.green_121_131_90),
          ),
        ),
      ),
    );
  }
}
