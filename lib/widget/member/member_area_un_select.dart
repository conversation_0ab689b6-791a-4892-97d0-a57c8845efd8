import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';

import '../../gen/r.dart';
import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../screen/search_member/search_member_screen.dart';
import '../btn/member_base_btn.dart';
import '../container_with_radius.dart';
import '../dialog/MyDialog.dart';
import '../shape/circle.dart';

class MemberAreaUnSelect extends StatefulWidget {
  const MemberAreaUnSelect({super.key});

  @override
  State<MemberAreaUnSelect> createState() => _MemberAreaUnSelectState();
}

class _MemberAreaUnSelectState extends State<MemberAreaUnSelect> {
  @override
  Widget build(BuildContext context) {
    return _memberAreaUnSelect();
  }

  ///未選會員時
  Widget _memberAreaUnSelect() {
    return Container(
      height: 138.h,
      width: 460.w,
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [


          Text(
            '尚未指定結帳會員',
            style: TextStyle(
              fontSize: 23.sp,
              color: MyColors.grey_119_119_119,
            ),
          ),
          SizedBox(width: 39.w),
          MemberBaseBtn(
            title: '搜尋會員',
            onTap: () => _searchMemberDialog(),
          ),
        ],
      ),
    );
  }

  ///開啟搜尋會員彈窗
  void _searchMemberDialog() {
    final vm = context.read<ShoppingCarProvider>();
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: true,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: SearchMemberScreen(
                backOnTap: () {
                  Navigator.of(context).pop();
                },
                submitOnTap:
                    (
                      String avatar,
                      String name,
                      String phone,
                      String birthday,
                      String eMail,
                    ) {
                      Navigator.of(context).pop();

                      ///更新會員資訊
                      vm.setMemberInfo(
                        avatar: avatar,
                        name: name,
                        phone: phone,
                        birthday: birthday,
                        email: eMail,
                      );
                    },
              ),
            ),
          ),
        ),
      ),
    );
  }
}
