import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../cached_network_image/error_cached_network_image.dart';
import '../container_with_radius.dart';

class MemberArea extends StatefulWidget {
  const MemberArea({super.key});

  @override
  State<MemberArea> createState() => _MemberAreaState();
}

class _MemberAreaState extends State<MemberArea> {
  @override
  Widget build(BuildContext context) {
    return  _memberArea();
  }

  ///上方會員區塊(已選擇會員)
  Widget _memberArea() {
    final vm = context.read<ShoppingCarProvider>();
    return Container(
      height: 138.h,
      width: 460.w,
      color: Colors.white,
      child: Stack(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [

              // SizedBox(width: 50.w,),
              //
              // //大頭照
              // ClipRRect(
              //   borderRadius: BorderRadius.circular(50.r),
              //   child: CachedNetworkImage(
              //     imageUrl: vm.memberAvatar ?? '',
              //     height: 80.w,
              //     width: 80.w,
              //     fit: BoxFit.cover,
              //     errorWidget: (context, url, error) =>
              //     const ErrorCachedNetworkImage(),
              //   ),
              // ),

              SizedBox(width: 23.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(
                      vm.memberName ?? '',
                      style: TextStyle(
                        fontSize: 28.sp,
                        color: MyColors.brown_57_54_18,
                          fontWeight:  FontWeight.w700,
                      ),
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    vm.memberPhone ?? '',
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ],
              ),
            ],
          ),

          Positioned(
            top: 14.h,
            right: 14.w,
            child: ContainerWithRadius(
              isHaveBorder: true,
              onTap: (){
                final shoppingCarVm = context.read<ShoppingCarProvider>();
                vm.clearMember();
                shoppingCarVm.clearSelectCash();
              },
              w: 50.w,
              h: 50.w,
              r: 20.r,

              color: Colors.white,
              child: Icon(Icons.close,color: MyColors.green_129_128_94,),
            ),
          ),
        ],
      ),
    );
  }
}
