import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../container_with_radius.dart';

class WalletInfo extends StatelessWidget {
  final String cashName;
  final double classOriginalPrice; //課程金額小計(原價)
  final double classDiscountPrice; //課程金額小計(折扣後)
  final double walletPay; //錢包支付金額
  final double unPaid; //課程未付款金額
  final VoidCallback onTap;
  final bool? isHaveOnTap;
  final double? h;

  const WalletInfo({
    super.key,
    required this.cashName,
    required this.classOriginalPrice,
    required this.classDiscountPrice,
    required this.walletPay,
    required this.unPaid,
    required this.onTap, this.isHaveOnTap,
    this.h,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: (isHaveOnTap ?? true) ? onTap : null,
      child: Align(
        alignment: Alignment.centerRight,
        child: ContainerWithRadius(
          w: 474.w,
          h: h ?? 190.h,
          r: 20.r,
          color: Colors.white,
          isHaveBorder: true,
          child: Padding(
            padding: EdgeInsets.only(
              left: 17.w,
              right: 21.w,
              top: 9.h,
              bottom: 4.h,
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Text(
                      cashName,
                      style: TextStyle(
                        fontSize: 28.sp,
                        color: MyColors.green_129_128_94,
                      ),
                    ),

                    if (isHaveOnTap ?? true) ...[
                      Spacer(),
                      Icon(
                        Icons.arrow_forward_ios_rounded,
                        size: 23.w,
                        color: MyColors.grey_204_204_204,
                      ),
                    ]
                  ],
                ),
                SizedBox(height: 9.h),

                //課程金額小計(原價)
                _baseWalletPriceRow('課程金額小計(原價)：', classOriginalPrice),

                //課程金額小計(折扣後)
                _baseWalletPriceRow('課程金額小計(折扣後)：', classDiscountPrice),

                //錢包支付金額
                _baseWalletPriceRow('錢包支付金額：', walletPay, true),

                //課程未付款金額
                _baseWalletPriceRow('課程未付款金額：', unPaid, false, true),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///base wallet price Row
  Widget _baseWalletPriceRow(
    String title,
    double price, [
    bool? isMinus,
    bool? isUnpaid,
  ]) {
    final titleStyle = TextStyle(
      fontSize: 25.sp,
      color: MyColors.grey_119_119_119,
    );
    final priceStyle = TextStyle(
      fontSize: 25.sp,
      color: MyColors.green_129_128_94,
    );
    final unpaidStyle = TextStyle(
      fontSize: 25.sp,
      color: MyColors.red_230_75_75,
    );
    return Row(
      children: [
        Text(title, style: titleStyle),
        Spacer(),

        //正常情況
        if (!(isMinus ?? false) && !(isUnpaid ?? false))
          Text(
            '\$ ${StringUtils.formatMoneyForDouble(price)}',
            style: priceStyle,
          ),

        //錢包支付金額
        if (isMinus ?? false)
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              '- ${StringUtils.formatMoneyForDouble(price)}',
              style: priceStyle,
            ),
          ),

        //課程未支付金額
        if (isUnpaid ?? false)
          Align(
              alignment: Alignment.centerRight,
              child: Text(StringUtils.formatMoneyForDouble(price), style: unpaidStyle)),
      ],
    );
  }
}
