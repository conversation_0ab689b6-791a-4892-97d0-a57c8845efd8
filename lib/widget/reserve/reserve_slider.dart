import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../../screen/reserve_manage/reserve_manage_info.dart';

class ReserveSlider extends StatefulWidget {
  final Animation<double> sizeFactor;
  final String date; //日期
  final String week; //星期
  final String orderId; //訂單編號
  final String className; //課程名稱
  final String memberName; //會員名稱
  final String memberPhone; //會員手機
  final String beautician; //美容師
  final String customerSelectionType; //指定情況(ex:客戶指定)
  final String classDate; //課程日期
  final double price; //金額
  const ReserveSlider({super.key, required this.sizeFactor, required this.date, required this.week, required this.orderId, required this.className, required this.memberName, required this.memberPhone, required this.beautician, required this.customerSelectionType, required this.classDate, required this.price});

  @override
  State<ReserveSlider> createState() => _ReserveSliderState();
}

class _ReserveSliderState extends State<ReserveSlider> {
  @override
  Widget build(BuildContext context) {
    return _sliderArea();
  }

  ///側邊欄
  Widget _sliderArea() {
    return Material(
      child: SizeTransition(
        axis: Axis.horizontal,
        sizeFactor: widget.sizeFactor,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color:MyColors.green_157_193_65, width: 1.w ),
          ),
          width: MediaQuery.of(context).size.width * (460 / 1920),
          height: MediaQuery.of(context).size.height,

          child: ReserveManageInfo(
            date: widget.date,
            week: widget.week,
            orderId: widget.orderId,
            className: widget.className,
            memberName: widget.memberName,
            memberPhone: widget.memberPhone,
            beautician: widget.beautician,
            customerSelectionType: widget.customerSelectionType,
            classDate: widget.classDate,
            price:widget.price,
          ),
        ),
      ),
    );
  }
}
