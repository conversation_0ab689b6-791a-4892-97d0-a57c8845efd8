import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../resource/MyColor.dart';

class ContainerWithRadius extends StatelessWidget {
  final double w;
  final double h;
  final double r;
  final Color color;
  final Color? borderColor;
  final Widget child;
  final Function()? onTap;
  final Function()? onLongPress;
  final bool isHaveBorder;
  final List<BoxShadow>? boxShadow;
  const ContainerWithRadius({super.key, required this.w, required this.h, required this.r,  required this.color, required this.child,this.onTap, this.boxShadow, required this.isHaveBorder, this.onLongPress, this.borderColor, });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onLongPress: onLongPress,
      onTap: onTap,
      child: Container(
        width: w,
        height: h,
        decoration: BoxDecoration(
            border: Border.all(
          color: isHaveBorder ? borderColor ?? MyColors.grey_235_235_235 : Colors.transparent,
          width: 1,
        ),
          color: color,
          borderRadius: BorderRadius.circular(r),
          boxShadow: boxShadow,
        ),
        child: child,
      ),
    );
  }

}
