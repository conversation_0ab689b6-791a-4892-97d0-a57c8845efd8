import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class SwitchTypeRow extends StatefulWidget {
  // final int amount;
  final int amount1;
  final int amount2;
  final int amount3;
  final int selectedType;
  final ValueChanged<int> onChanged;
  const SwitchTypeRow({super.key, required this.amount1, required this.amount2, required this.amount3, required this.selectedType, required this.onChanged, });

  @override
  State<SwitchTypeRow> createState() => _SwitchTypeRowState();
}

class _SwitchTypeRowState extends State<SwitchTypeRow> {
  int _selectedIndex = -1;
  int type = 1;  //預設為商品


  @override
  void initState() {
    super.initState();
    _selectedIndex = widget.selectedType - 1;
  }

  @override
  Widget build(BuildContext context) {
    return  _switchTypeRow();
  }

  Widget _switchTypeRow() {


          return  Row(
            children: [
              _baseTypeBtn('已選商品', widget.amount1, 0),
              _baseTypeBtn('預約與課程', widget.amount2, 1),
              _baseTypeBtn('儲值金', widget.amount3, 2),
            ],
          );


  }

  ///base type btn
  Widget _baseTypeBtn(String title, int amount, int index) {
    final isSelected = _selectedIndex == index;
    final selectTextStyle = TextStyle(
      fontWeight: FontWeight.w700,
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
    );
    final unSelectTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return Row(
      children: [
        ContainerWithRadius(
          onTap: () {
            setState(() {
              _selectedIndex = index;

              // index 對應 type（index: 0=1, 1=2, 2=3）
              type = index + 1;

            });
            widget.onChanged(index + 1);
          },
          w: 200.w,
          h: 48.h,
          r: 0.r,
          color: isSelected ? Colors.white : MyColors.grey_248_248_248,
          isHaveBorder: true,
          child: Padding(
            padding: EdgeInsets.only(left: 16.w, right: 8.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: isSelected ? selectTextStyle : unSelectTextStyle,
                ),
                Spacer(),
                Text(
                  amount.toString(),
                  style: isSelected ? selectTextStyle : unSelectTextStyle,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
