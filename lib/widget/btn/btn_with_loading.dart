import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../resource/MyColor.dart';
import 'base_btn.dart';

class BtnWithLoading extends StatelessWidget {
  final String title;
  final bool isLoading;
  final Function() onTap;

  const BtnWithLoading(
      {super.key,
      required this.title,
      required this.isLoading,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: 300.ms,
      child: !isLoading
          ? BaseBtn(
              key: const Value<PERSON>ey('login'),
              textColor: MyColors.green_129_128_94,
              text: title,
              onTap: onTap,
            )
          : const Center(
              key: Value<PERSON><PERSON>('loading'),
              child: CircularProgressIndicator(color: MyColors.green_67_160_71),
            ),
    );
  }
}
