import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class ReturnAndSubmitBtn extends StatelessWidget {
  final VoidCallback backOnTap;
  final VoidCallback submitOnTap;
  final double? h;
  final Color? color;
  final Color? borderColor;
  final String? submitText;
  final String? cancelText;
  final bool? isHaveBorder;
  final bool? isHaveSubmit;
  final BorderRadius? borderRadius;

  const ReturnAndSubmitBtn({
    super.key,
    required this.backOnTap,
    required this.submitOnTap,
    this.h,
    this.color,
    this.submitText,
    this.isHaveBorder,
    this.cancelText,
    this.borderRadius,
    this.isHaveSubmit,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return _returnAndSubmitBtn();
  }

  ///返回與送出按鈕
  Widget _returnAndSubmitBtn() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: borderRadius,
        border: Border.all(
          color: (isHaveBorder ?? false)
              ? borderColor ?? MyColors.grey_235_235_235
              : Colors.transparent,
          width: 0.5,
        ),
        color: color ?? MyColors.grey_248_248_248,
      ),

      height: h ?? 120.h,
      width: double.infinity.w,

      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _baseBtn(cancelText ?? '返回', backOnTap),

          if (isHaveSubmit ?? true) SizedBox(width: 20.w),
          if (isHaveSubmit ?? true) _baseBtn(submitText ?? '確認', submitOnTap),
        ],
      ),
    );
  }

  ///baseBtn
  Widget _baseBtn(String title, VoidCallback onTap) {
    return ContainerWithRadius(
      onTap: onTap,
      isHaveBorder: true,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      w: 196.w,
      h: 62.h,
      r: 20.r,
      color: Colors.white,
      child: Center(
        child: Text(
          title,
          style: TextStyle(fontSize: 25.sp, color: MyColors.green_121_131_90),
        ),
      ),
    );
  }
}
