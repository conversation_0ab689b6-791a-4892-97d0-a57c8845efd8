import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class DrawerBtn extends StatelessWidget {
  final VoidCallback drawerOnTap;
  const DrawerBtn({super.key, required this.drawerOnTap});

  @override
  Widget build(BuildContext context) {
    return  _drawerBtn();
  }

  ///開啟側邊欄按鈕
  Widget _drawerBtn() {
    return InkWell(
      onTap: drawerOnTap,
      child: Container(
        width: 89.w,
        height: 89.w,
        decoration: BoxDecoration(
          color: MyColors.green_157_193_65,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Icon(Icons.menu, size: 50.w, color: Colors.white),
      ),
    );
  }
}
