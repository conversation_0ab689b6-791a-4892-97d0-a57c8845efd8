import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../container_with_radius.dart';

class MemberBaseBtn extends StatelessWidget {
  final String title;
  final  VoidCallback onTap;
  final double? w;
  final double? h;
  const MemberBaseBtn({super.key, required this.title, required this.onTap, this.w, this.h});

  @override
  Widget build(BuildContext context) {
    return  _baseBtn();
  }

  ///Base Btn
  Widget _baseBtn() {
    return ContainerWithRadius(
      isHaveBorder: true,
      onTap: onTap,
      w: w ?? 148.w,
      h: h ?? 63.h,
      r: 20.r,
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          title,
          style: TextStyle(fontSize: 25.sp, color: MyColors.green_129_128_94),
        ),
      ),
    );
  }
}
