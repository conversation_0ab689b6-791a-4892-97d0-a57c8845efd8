import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../container_with_radius.dart';

class ScanBtn extends StatelessWidget {
  final VoidCallback onTap;
  const ScanBtn({super.key, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return ContainerWithRadius(
      onTap: onTap,
      isHaveBorder: true,
      w: 62.w,
      h: 62.w,
      r: 20.r,
      color: Colors.white,
      child: Icon(Icons.fit_screen_outlined, color: Colors.lightGreen),
    );
  }


}
