import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../resource/MyColor.dart';

class BaseBtn extends StatelessWidget {
  final String text;
  final Widget? widget;
  final VoidCallback? onTap;
  final Color? backgroundColor;
  final Color? textColor;
  final double? fontSize;
  final bool? isExpendW;
  final EdgeInsetsGeometry? padding;
  final double? radius;

  const BaseBtn(
      {super.key,
      required this.text,
      required this.onTap,
      this.backgroundColor,
      this.textColor,
      this.padding,
      this.fontSize,
      this.isExpendW, this.widget, this.radius,});

  @override
  Widget build(BuildContext context) {
    return (isExpendW ?? true)
        ? SizedBox(width: double.infinity, child: _buildElevatedButton())
        : _buildElevatedButton();
  }

  Widget _buildElevatedButton() {
    return ElevatedButton(
      onPressed: onTap,
      style: ElevatedButton.styleFrom(
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        minimumSize: Size.zero,
        padding: padding ??  EdgeInsets.symmetric(vertical: 30.h),

        backgroundColor: backgroundColor ?? Colors.white, // 背景顏色
        foregroundColor: Colors.black, // 文字或圖示顏色
        shadowColor: const Color(0x14000000), // 陰影顏色（#00000014）
        elevation: 6, // 陰影高度
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radius ?? 20),
          side: const BorderSide(color: Color(0xFFEBEBEB)), // 邊框顏色
        ),
      ),
      child: widget ??  Text(text,style: TextStyle(
        // fontFamily: FontFamily.sFProRegular,
          color: textColor ?? MyColors.green_129_128_94,
          fontSize: fontSize ?? 25.sp),),
    );


  }
}
