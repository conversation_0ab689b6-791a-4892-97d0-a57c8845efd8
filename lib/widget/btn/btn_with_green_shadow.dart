import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BtnWithGreenShadow extends StatelessWidget {
  final Widget child;
  final  Function() onTap;
  const BtnWithGreenShadow({super.key, required this.child, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 50.w,
          height: 50.w,
          decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(20.r),
            boxShadow: [
              BoxShadow(
                color: const Color(0x999DC141),
                offset: const Offset(0, 2),
                blurRadius: 10,
              ),
            ],
          ),
          child: child
      ),
    );

  }
}
