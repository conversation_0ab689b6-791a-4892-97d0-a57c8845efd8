import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../container_with_radius.dart';
import '../shape/circle.dart';
import '../textField/base_text_field.dart';

class ManualDiscountRow extends StatelessWidget {
  final String title;
  final double price;
  final bool isMinus;
  final bool isHaveDot;
  final bool? isExpanded;
  final EdgeInsets? padding;
  final TextEditingController? controller;
  final bool? isReadOnly;

  const ManualDiscountRow({
    super.key,
    required this.title,
    required this.price,
    required this.isMinus,
    required this.isHaveDot,
    this.isExpanded,
    this.padding,
    this.controller,
    this.isReadOnly,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: isHaveDot
                ? Row(
                    children: [
                      Circle(color: MyColors.green_121_131_90, size: 15.w),
                      SizedBox(width: 21.w),
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: MyColors.green_129_128_94,
                        ),
                      ),
                    ],
                  )
                : Text(
                    title,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
          ),

          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                isMinus ? Icons.remove : Icons.add,
                color: MyColors.green_129_128_94,
                size: 20.w,
              ),
              SizedBox(width: 23.w),

              BaseTextField(
                enterTextPadding: EdgeInsets.only(right: 20.w),
                textAlign: TextAlign.right,
                isOnlyNum: true,
                isReadOnly: isReadOnly,
                w: 250.w,
                h: 74.h,
                hint: '0',
                controller: controller ?? TextEditingController(),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
