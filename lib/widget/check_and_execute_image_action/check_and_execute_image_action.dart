import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';

import '../../logger/my_print.dart';

Future<void> checkAndExecuteImageAction({
  required BuildContext context,
  required String imageUrl,
  required VoidCallback onSuccess,
  VoidCallback? onError,
}) async {
  try {
    final ImageStream stream = CachedNetworkImageProvider(imageUrl).resolve(const ImageConfiguration());
    final Completer<void> completer = Completer<void>();

    stream.addListener(
      ImageStreamListener(
            (info, _) => completer.complete(),
        onError: (error, stackTrace) => completer.completeError(error),
      ),
    );

    await completer.future; // 等待圖片載入完成
    onSuccess(); // 圖片載入成功，執行 onSuccess
  } catch (e) {
    myPrint("圖片加載失敗: $e");
    // 圖片加載失敗時，執行 onError（如果有提供）
    if (onError != null) {
      onError();
    } else {
      myPrint("圖片加載失敗: $e");
    }
  }
}
