import 'package:flutter/material.dart';

class BaseBackground extends StatelessWidget {
  final Widget child;
  const BaseBackground({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.topLeft, // 放射中心
            radius: 2.5,
            colors: [
              Color(0xFFEFF5D9), // 中心偏亮的淡綠
              Color(0xFFFDFDFD), // 四周偏白的背景
            ],
            stops: [0.0, 1.0],
          ),
        ),
        child: child
      ),
    );
  }
}

// import 'package:flutter/material.dart';
//
// class BaseBackground extends StatelessWidget {
//   final Widget child;
//   const BaseBackground({super.key, required this.child});
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: CustomPaint(
//         painter: _BackgroundPainter(),
//         child: child
//       ),
//     );
//   }
// }
//
// class _BackgroundPainter extends CustomPainter {
//   @override
//   void paint(Canvas canvas, Size size) {
//     final center = Offset(size.width * 0.62, size.height * 0.45); // 中間偏右
//     final radius = size.width * 0.5;
//
//     // 背景漸層：白到淡綠
//     final backgroundPaint = Paint()
//       ..shader = LinearGradient(
//         begin: Alignment.topLeft,
//         end: Alignment.bottomRight,
//         colors: [
//           const Color(0xFFF7FBEF), // 白
//           const Color(0xFFE7F4D6), // 淡綠黃
//         ],
//       ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
//     canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), backgroundPaint);
//
//     // 中央偏右綠色光暈，範圍縮小
//     final glowPaint = Paint()
//       ..shader = RadialGradient(
//         center: Alignment.center,
//         colors: [
//           const Color(0xFFCCE5B0).withOpacity(0.35), // 淡綠
//           const Color(0xFFCCE5B0).withOpacity(0.0),  // 透明
//         ],
//         radius: 0.25, // ↓ 更集中
//       ).createShader(Rect.fromCircle(center: center, radius: radius));
//     canvas.drawCircle(center, radius, glowPaint);
//   }
//
//   @override
//   bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
// }


