import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import 'radio_box.dart';

class RadioBtnWithText extends StatefulWidget {
  final TextStyle subTitleStyle;
  final String text;
  final Widget? widgetText;
  final String? selected;
  final Function(String?) onChange;
  final bool isExpand;
  final Key? positionKey;
  final Color? activeColor;
  final Color? unselectedColor;
  final double? spacing;
  final bool? isCheckBoxOnLeft;

  const RadioBtnWithText({
    Key? key,
    required this.subTitleStyle,
    required this.text,
    this.selected,
    this.isExpand = false,
    required this.onChange,
    this.positionKey,
    this.activeColor,
    this.unselectedColor,
    this.spacing,
    this.widgetText,
    this.isCheckBoxOnLeft,
  }) : super(key: key);

  @override
  State<RadioBtnWithText> createState() => _RadioBtnWithTextState();
}

class _RadioBtnWithTextState extends State<RadioBtnWithText> {
  bool isSelect = false;

  @override
  Widget build(BuildContext context) {
    return (widget.isCheckBoxOnLeft ?? true)
        ? _buildCheckBoxOnLeft()
        : _buildCheckBoxOnRight();
  }

  ///單選ＵＩ靠左
  Widget _buildCheckBoxOnLeft() {
    return Padding(
      padding:  EdgeInsets.only(top: 4.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            child: Padding(
              padding:  EdgeInsets.only(top: 10.h),  //讓他對齊文字
              child: CustomRadioBox<String>(
                  activeColor: widget.activeColor ?? MyColors.green_129_128_94,

                  size: 40.w,
                  value: widget.text,
                  groupValue: widget.selected,
                  borderColor: Colors.black,              // 常駐黑色邊框
                  backgroundColor: Colors.white,          // 常駐白底
                  onChanged: (value) {
                    widget.onChange.call(value);
                  }),
            ),
          ),
          SizedBox(
            width: widget.spacing ?? 6.76,
          ),
          if (widget.isExpand)
            Expanded(
              child: GestureDetector(
                onTap: () {
                  widget.onChange.call(widget.text);
                },
                child: widget.widgetText ??
                    Text(
                      widget.text,
                      style: widget.subTitleStyle,
                    ),
              ),
            )
          else
            GestureDetector(
              onTap: () {
                widget.onChange.call(widget.text);
              },
              child: Padding(
                padding:  EdgeInsets.only(bottom: 10,right: 8,left: 8,top: 6.h),
                child: widget.widgetText ??
                    Text(
                      widget.text,
                      style: widget.subTitleStyle,
                    ),
              ),
            ),
        ],
      ),
    );
  }

  ///單選ＵＩ靠右
  Widget _buildCheckBoxOnRight() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isExpand)
          Expanded(
            child: ExcludeSemantics(
              child: GestureDetector(
                onTap: () {
                  widget.onChange.call(widget.text);
                },
                child: Padding(
                  padding:  EdgeInsets.only(bottom: 10,right: 8,left: 8,top: 6.h),
                  child: widget.widgetText ??
                      Text(
                        widget.text,
                        style: widget.subTitleStyle,
                      ),
                ),
              ),
            ),
          )
        else
          ExcludeSemantics(
            child: GestureDetector(
              onTap: () {
                widget.onChange.call(widget.text);
              },
              child: widget.widgetText ??
                  Text(
                    widget.text,
                    style: widget.subTitleStyle,
                  ),
            ),
          ),
        SizedBox(
          width: widget.spacing ?? 6.76,
        ),
        SizedBox(
          child: Padding(
            padding:  EdgeInsets.only(top: 10.h),  //讓他對齊文字
            child: CustomRadioBox<String>(
                activeColor: widget.activeColor ?? MyColors.red_154_0_40,
                size: 14.24,
                value: widget.text,
                groupValue: widget.selected,
                borderColor: Colors.black,              // 常駐黑色邊框
                backgroundColor: Colors.white,          // 常駐白底
                onChanged: (value) {
                  widget.onChange.call(value);
                }),
          ),
        ),
      ],
    );
  }
}
