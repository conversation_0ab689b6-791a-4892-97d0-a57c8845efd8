import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class CustomRadioBox<T> extends StatelessWidget {
  const CustomRadioBox({
    Key? key,
    required this.size,
    required this.value,
    required this.onChanged,
    required this.groupValue,
    this.activeColor = Colors.green,
    this.borderColor = Colors.black,
    this.backgroundColor = Colors.white,
  }) : super(key: key);

  final double size;
  final T value;
  final T? groupValue;
  final ValueChanged<T?> onChanged;

  final Color activeColor;
  final Color borderColor;
  final Color backgroundColor;

  bool get isSelected => value == groupValue;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onChanged(value),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor, // 常駐白色背景
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor, // 常駐黑色邊框
            width: 2,
          ),
        ),
        child: Center(
          child: AnimatedContainer(
            duration: Duration(milliseconds: 200),
            width: size * 0.5,
            height: size * 0.5,
            decoration: BoxDecoration(
              color: isSelected ? activeColor : Colors.transparent,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ),
    );
  }
}
