import 'package:flutter/cupertino.dart';
import 'package:sbar_pos/widget/radio_btn/radio_box.dart';

import '../../resource/MyColor.dart';


class RadioBtnWithWidget extends StatefulWidget {
  final TextStyle subTitleStyle;
  final Widget widget;
  final String? selected;
  final Function(String?) onChange;
  final Key? positionKey;
  final String text;

  const RadioBtnWithWidget({
    Key? key,
    required this.subTitleStyle,
    this.selected,
    required this.onChange,
    this.positionKey,
    required this.widget,
    required this.text,
  }) : super(key: key);

  @override
  State<RadioBtnWithWidget> createState() => _RadioBtnWithWidgetState();
}

class _RadioBtnWithWidgetState extends State<RadioBtnWithWidget> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        widget.onChange.call(widget.text);
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomRadioBox<String>(
              activeColor: MyColors.red_154_0_40,
              size: 14.24,
              value: widget.text,
              groupValue: widget.selected,
              onChanged:
                  (value){
                widget.onChange.call(value);
              }
          ),
          const SizedBox(
            width: 6.76,
          ),
          widget.widget

        ],
      ),
    );
  }
}



