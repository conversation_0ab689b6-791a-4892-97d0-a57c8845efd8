import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class MyRadioButton extends StatefulWidget {
  final String title;
  final String tip;
  final  bool isRequired;
  final  Axis direction;
  final int initSelectedIndex;
  final List<String> options;
  final Function(int) selectIndex;
  final Color  titleTextColor;
  final double titleSize;


  const MyRadioButton({super.key,
    this.title = '',
    this.tip = '',
    this.isRequired = false,
    this.direction = Axis.horizontal,
    this.initSelectedIndex = 0,
    this.titleTextColor = Colors.black,
    this.titleSize = 13,
    required this.options,
    required this.selectIndex});

  @override
  _MyRadioButtonState createState() => _MyRadioButtonState();
}

class _MyRadioButtonState extends State<MyRadioButton> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        //title
        if (widget.title.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: 5.h),
            child: Row(
              children: [
                Text(
                  (widget.isRequired) ? '*' : '',
                  style: TextStyle(color: Colors.red, fontSize: 15.sp),
                ),
                Text(
                  widget.title,
                  style: TextStyle(color: widget.titleTextColor , fontSize: widget.titleSize),
                ),
              ],
            ),
          ),
        SizedBox(
          width: double.infinity,
          child: Theme(
            data: ThemeData(unselectedWidgetColor: const Color.fromRGBO(0, 173, 216, 1)),
            child: Wrap(
              spacing: 20.w,
              direction: widget.direction,
              children: [
                for (int i = 0; i < widget.options.length; i++)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Radio(
                          visualDensity: const VisualDensity(
                            vertical: VisualDensity.minimumDensity,
                            horizontal: VisualDensity.minimumDensity,
                          ),
                          value: i,
                          activeColor: const Color.fromRGBO(0, 173, 216, 1),
                          groupValue: widget.initSelectedIndex,
                          onChanged: (i) {
                            // setState(() {
                            //   widget.initSelectedIndex = i;
                            //   widget.selectIndex(i);
                            // });
                          }),
                      Text(
                        widget.options[i],
                        style: TextStyle(fontSize: 14.sp, color: const Color.fromRGBO(66, 66, 66, 1)),
                      )
                    ],
                  )
              ],
            ),
          ),
        ),
        //Tip
        if (widget.tip.isNotEmpty)
          Container(
            padding: EdgeInsets.only(top: 5.h, left: 12.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 13.w,
                ),
                SizedBox(width: 6.w),
                Expanded(
                  child: Text(
                    widget.tip,
                    style: TextStyle(fontSize: 12.sp, color: const Color.fromRGBO(145, 145, 145, 1)),
                  ),
                )
              ],
            ),
          )
      ],
    );
  }
}

class StatelessRadioButton extends StatelessWidget {
  const StatelessRadioButton({
    super.key,
    this.title = '',
    this.tip = '',
    this.isRequired = false,
    this.direction = Axis.horizontal,
    this.titleTextColor = Colors.black,
    this.titleSize = 13,
    this.isTitleOnMiddle = false,
    required this.selectedIndex,
    required this.options,
    this.selectIndex,
  this.spacing = 20,
  this.titleBottomPadding = 5,
    this.subTitleBottomPadding = 5,
  this.subTitle ='',
    this.subTitleSize = 12,
    this.subTitleTextColor = Colors.black,
    this.subTitleTopPadding = 5,
  this.isHaveDivider = false,
  this.isHaveSubTitle = false,
  this.contentPadding = 0});

  final String title;
  final String tip;
  final bool isRequired;
  final Axis direction;
  final int selectedIndex;
  final List<String> options;
  final Function(int)? selectIndex;
  final Color titleTextColor;
  final Color subTitleTextColor;
  final double titleSize;
  final double subTitleSize;
  final bool isTitleOnMiddle;
  final double spacing;
  final double titleBottomPadding;
  final double subTitleTopPadding;
  final double subTitleBottomPadding;
  final String subTitle;
  final bool isHaveSubTitle;
  final bool isHaveDivider;
  final double contentPadding;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,

      children: [
        //title
        if (title.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: titleBottomPadding),
            child: Align(
              alignment: isTitleOnMiddle ? Alignment.center : Alignment.centerLeft,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    (isRequired) ? '*' : '',
                    style: TextStyle(color: Colors.red, fontSize: 15.sp),
                  ),
                  Text(
                    title,
                    style: TextStyle(color: titleTextColor, fontSize: titleSize),
                  ),
                ],
              ),
            ),
          ),
        Visibility(
          visible: isHaveDivider == true,
          child: Padding(
            padding:  EdgeInsets.only(top: titleBottomPadding),
            child: Divider( height: 1.h,
              indent:0,
              endIndent: 0,
              color: MyColors.grey_145_145_145,),
          ),
        ),
        Visibility(
            visible: isHaveSubTitle == true,
            child: Padding(
              padding: EdgeInsets.only(top: subTitleTopPadding, bottom: subTitleBottomPadding),
              child: Text(subTitle,style: TextStyle(color: subTitleTextColor, fontSize: subTitleSize)),
            )),
        Padding(
          padding:  EdgeInsets.symmetric(horizontal: contentPadding),
          child: SizedBox(
            width: double.infinity,
            child: Theme(
              data: ThemeData(unselectedWidgetColor: const Color.fromRGBO(0, 173, 216, 1)),
              child: Wrap(
                spacing: spacing,
                direction: direction,
                children: [
                  for (int i = 0; i < options.length; i++)
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Radio(
                            visualDensity: const VisualDensity(
                              vertical: VisualDensity.minimumDensity,
                              horizontal: VisualDensity.minimumDensity,
                            ),
                            value: i,
                            activeColor: const Color.fromRGBO(0, 173, 216, 1),
                            groupValue: selectedIndex,
                            onChanged: (value) {
                              selectIndex?.call(i);
                            }),
                        Text(
                          options[i],
                          style: TextStyle(fontSize: 14.sp, color: const Color.fromRGBO(66, 66, 66, 1)),
                        )
                      ],
                    )
                ],
              ),
            ),
          ),
        ),
        //Tip
        if (tip.isNotEmpty)
          Container(
            padding: EdgeInsets.only(top: 5.h, left: 12.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 13.w,
                ),
                SizedBox(width: 6.w),
                Expanded(
                  child: Text(
                    tip,
                    style: TextStyle(fontSize: 12.sp, color: const Color.fromRGBO(145, 145, 145, 1)),
                  ),
                )
              ],
            ),
          )
      ],
    );
  }
}
