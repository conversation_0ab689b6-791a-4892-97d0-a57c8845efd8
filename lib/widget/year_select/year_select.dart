import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import '../date_bottom_sheet/year_picker_bottom_sheet.dart';

class YearSelect extends StatefulWidget {
  final Function(int year)? onChanged; // 接收外部 callback
  final bool isCanSelectAfterThisYear; //是否可選超過今年
  const YearSelect({super.key, this.onChanged, required this.isCanSelectAfterThisYear});

  @override
  State<YearSelect> createState() => _YearSelectState();
}

class _YearSelectState extends State<YearSelect> {
  late int selectedYear;

  @override
  void initState() {
    super.initState();
    selectedYear = DateTime.now().year;

    // 初始時通知外部目前年份
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onChanged?.call(selectedYear);
    });
  }

  void _changeYear(int offset) {
    int newYear = selectedYear + offset;

    if (!widget.isCanSelectAfterThisYear && newYear > DateTime.now().year) {
      return; // 不可超過今年
    }

    setState(() {
      selectedYear = newYear;
    });

    widget.onChanged?.call(selectedYear);
  }


  @override
  Widget build(BuildContext context) {
    Color forwardIconColor =
    (!widget.isCanSelectAfterThisYear && selectedYear == DateTime.now().year)
        ? MyColors.green_129_128_94.withOpacity(0.3) // 淡一點
        : MyColors.green_129_128_94;
    return Row(
      children: [
        IconButton(
          icon: Icon(Icons.arrow_back_ios,size: 20.w,color: MyColors.green_129_128_94,),
          onPressed: () => _changeYear(-1),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          child: InkWell(
            onTap: (){

              _showDateBottomSheet();
            },
            child: Text(
              '$selectedYear',
              style: TextStyle(fontSize: 30.sp),
            ),
          ),
        ),
        IconButton(
          icon: Transform.rotate(
              angle: 180 * 3.1415926535 / 180,
              child: Icon(Icons.arrow_back_ios,size: 20.w,color: forwardIconColor)),
          onPressed: () => _changeYear(1),
        ),
      ],
    );
  }

  ///日期選擇器
  void _showDateBottomSheet() async {
    final result = await YearPickerBottomSheet.show(
      context,
      initialYear: selectedYear,
      endYear: widget.isCanSelectAfterThisYear
          ? 2100
          : DateTime.now().year,
    );

    if (result == null) return;

    setState(() {
      selectedYear = result;
    });

    widget.onChanged?.call(selectedYear);
  }


}
