import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class CustomVerticalDivider extends StatelessWidget {
  final Color? color;
  final double height;
  final double? width;
  const CustomVerticalDivider({super.key,  this.color, required this.height,  this.width});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: color ?? MyColors.grey_156_157_152,
      width: width ?? 0.5.w,
      height: height,

    );
  }
}
