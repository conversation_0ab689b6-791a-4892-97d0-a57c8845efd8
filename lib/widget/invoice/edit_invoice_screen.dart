import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../btn/return_and_submit_btn.dart';
import '../container_with_radius.dart';
import '../textField/my_text_field.dart';

class EditInvoiceScreen extends StatefulWidget {
  final String initialInvoice; // 原本發票號碼
  final void Function(String) onSubmit; // 儲存回調
  const EditInvoiceScreen({super.key, required this.initialInvoice, required this.onSubmit});

  @override
  State<EditInvoiceScreen> createState() => _EditInvoiceScreenState();
}

class _EditInvoiceScreenState extends State<EditInvoiceScreen> {
  late TextEditingController controller;
  AnimationController? animationInvoiceController;

  @override
  void initState() {
    super.initState();
    controller = TextEditingController(text: widget.initialInvoice);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:  EdgeInsets.symmetric(vertical: 70.h,horizontal:180.w ),
      child: Column(
        children: [
          Text('發票號碼',
            style: TextStyle(fontSize: 32.sp, color: MyColors.grey_112_112_112),),
          SizedBox(height: 22.5.h,),
          Divider(height: 1, thickness: 1,),
          SizedBox(height: 22.5.h,),
          Padding(
            padding:  EdgeInsets.symmetric(horizontal: 30.w),
            child: MyTextField(
              isSingleLine: true,
              hint: '請輸入發票號碼',
              hintFontSize: 28.sp,
              style: TextStyle(fontSize: 28.sp,color: MyColors.brown_57_54_18),
              textAlign: TextAlign.start,
              enterTextPadding: EdgeInsets.only(left: 15.w),
              controller: controller,
              border: 20.h,
              height: 74.h,
            ) .animate(
              autoPlay: false,
              onInit: (controller) {
                animationInvoiceController = controller;
              },
            )
                .shakeX(duration: const Duration(milliseconds: 500)),
          ),
          SizedBox(height: 22.5.h,),
          Text('發票號碼範例：AB123456',
            style: TextStyle(fontSize: 23.sp, color: MyColors.green_129_128_94),),
          SizedBox(height: 48.h,),

          ///返回與送出按鈕
          _returnAndSubmitBtn()

        ],
      ),
    );
  }

  ///返回與送出按鈕
  Widget _returnAndSubmitBtn() {
    return Row(
      children: [
        Expanded(
          flex: 3,
          child: _baseBtn('返回', () {
            Navigator.of(context).pop();
          }),
        ),
        SizedBox(width: 15.w),
        Expanded(
          flex: 7,
          child: _baseBtn('儲存', () {
            if(controller.text.isEmpty){
              animationInvoiceController?.reset();
              animationInvoiceController?.forward();
              return;
            }
            widget.onSubmit(controller.text);
            Navigator.of(context).pop();
          }),
        ),
      ],
    );
  }

  ///baseBtn
  Widget _baseBtn(String title, VoidCallback onTap) {
    return ContainerWithRadius(
      w: 0,
      onTap: onTap,
      isHaveBorder: true,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      h: 63.h,
      r: 20.r,
      color: Colors.white,
      child: Center(
        child: Text(
          title,
          style: TextStyle(fontSize: 25.sp, color: MyColors.green_121_131_90),
        ),
      ),
    );
  }

}
