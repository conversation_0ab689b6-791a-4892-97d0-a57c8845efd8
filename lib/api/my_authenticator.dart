import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:chopper/chopper.dart';

import '../logger/my_print.dart';
import '../model/member/member_repository.dart';

/// 接收回傳如果是Unauthorized的話就重新getToken
class MyAuthenticator implements Authenticator {
  MyAuthenticator(this._repo);

  final MemberRepository _repo;

  // Completer to prevent multiple token refreshes at the same time
  Completer<String>? _completer;

  @override
  FutureOr<Request?> authenticate(
    Request request,
    Response response, [
    Request? originalRequest,
  ]) async {
    if (isUnauthorized(response)) {
      // Trying to update token only 1 time
      if (request.headers['Retry-Count'] != null) {
        myPrint(
          '[MyAuthenticator] Unable to refresh token, retry count exceeded',
        );
        return null;
      }

      try {
        final newToken = _repo.isMember ? '' : await _refreshToken();

        return applyHeaders(
          request,
          {
            HttpHeaders.authorizationHeader: 'Bearer $newToken',
            // Setting the retry count to not end up in an infinite loop
            // of unsuccessful updates
            'Retry-Count': '1',
          },
        );
      } catch (e) {
        myPrint('[MyAuthenticator] Unable to refresh token: $e');
        return null;
      }
    }

    return null;

    // myPrint('[MyAuthenticator] response.statusCode: ${response.statusCode}');
    // myPrint(
    //   '[MyAuthenticator] request Retry-Count: ${request.headers['Retry-Count'] ?? 0}',
    // );
    // // 401
    // if (response.statusCode == HttpStatus.unauthorized) {
    //
    //   // Trying to update token only 1 time
    //   if (request.headers['Retry-Count'] != null) {
    //     myPrint(
    //       '[MyAuthenticator] Unable to refresh token, retry count exceeded',
    //     );
    //     return null;
    //   }
    //
    //   try {
    //     final newToken = _repo.isMember ? '' : await _refreshToken();
    //
    //     return applyHeaders(
    //       request,
    //       {
    //         HttpHeaders.authorizationHeader: 'Bearer $newToken',
    //         // Setting the retry count to not end up in an infinite loop
    //         // of unsuccessful updates
    //         'Retry-Count': '1',
    //       },
    //     );
    //   } catch (e) {
    //     myPrint('[MyAuthenticator] Unable to refresh token: $e');
    //     return null;
    //   }
    // }
    //
    // return null;
  }

  bool isUnauthorized(Response<dynamic> response) {
    try {
      final decoded = json.decode(response.body);
      if (decoded
          case {
            'success': false,
            'errorCode': final int errorCode,
            'message': final message,
          }) {
        if (errorCode == 4) {
          return true;
        }
      }
    } catch (e) {
      myPrint(e);
    }

    return false;
  }

  Future<String> _refreshToken() async {
    var completer = _completer;
    if (completer != null && !completer.isCompleted) {
      myPrint('Token refresh is already in progress');
      return completer.future;
    }

    completer = Completer<String>();
    _completer = completer;

    _repo.refreshToken().then((_) {
      myPrint('newAccessToken: ${_repo.accessToken}');
      // Completing with a new token
      completer?.complete(_repo.accessToken);
    }).onError((error, stackTrace) {
      // Completing with an error
      completer?.completeError(error ?? 'Refresh token error', stackTrace);
    });

    return completer.future;
  }

  @override
  AuthenticationCallback? get onAuthenticationFailed =>
      (Request request, Response<dynamic> response,
          [Request? originalRequest]) {
        /// 如果始終沒有得到可以用的 token 將會進來這裡
        myPrint('onAuthenticationFailed');
        // throw const TokenExpiredException();
        // backToLogin();
        // _repo.logout();
      };

  @override
  AuthenticationCallback? get onAuthenticationSuccessful =>
      (Request request, Response<dynamic> response,
          [Request? originalRequest]) {
        myPrint('onAuthenticationSuccessful');
      };
}

typedef AuthenticationCallback = FutureOr<void> Function(
  Request request,
  Response response, [
  Request? originalRequest,
]);
