import 'dart:async';
import 'dart:io';
import 'package:chopper/chopper.dart';
import '../model/member/member_repository.dart';

/// 增加 Bearer 標頭的攔截器
class AuthInterceptor implements Interceptor {
  const AuthInterceptor(this._repo);

  final MemberRepository _repo;

  @override
  FutureOr<Request> onRequest(Request request) async {
    if (_repo.accessToken.isEmpty) return request;

    final updatedRequest = request.copyWith(
      headers: {
        ...request.headers,
        HttpHeaders.authorizationHeader: 'Bearer ${_repo.accessToken}',
      },
    );

    return updatedRequest;
  }

  @override
  FutureOr<Response<BodyType>> intercept<BodyType>(Chain<BodyType> chain) async {
    final request = await chain.request;
    return chain.proceed(request);
  }
}
