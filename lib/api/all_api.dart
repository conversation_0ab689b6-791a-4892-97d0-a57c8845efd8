import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:sbar_pos/di/locator.dart';
import 'package:sbar_pos/hive/helper/boxes.dart';
import 'package:sbar_pos/model/api_model/login_req.dart';
import 'package:sbar_pos/provider/box_provider.dart';
import 'package:sbar_pos/api/basic_api.dart';

import '../constants/keys.dart';
import '../hive/hive_provider/hive_user_provider.dart';
import '../logger/my_print.dart';
import '../main.dart' as PageName;
import '../model/api_model/basic_res.dart';
import '../model/api_model/login_res.dart';
import '../model/api_model/res_model/res_login.dart';
import '../model/api_model/res_model/res_my_profile.dart';
import '../screen/main/main_screen.dart';
import '../screen/start/my_app.dart';
import '../widget/dialog/sbar_style_dialog.dart';
import 'api_service.dart';

///過期token，測試用
String getExpireToken() {
  return '';
}

class AllApi {
  AllApi(this.basicApi);

  final BasicApi basicApi;

  ///會員登入
  Future<void> apiLogin(
    BuildContext context, {
    required String account,
    required String pwd,
    required String groupName,
    required String groupCode,
  }) async {
    //TODO:記得把帳密改回來
    final res = await ApiService.create().login(
      account,
      pwd,

      // '<EMAIL>',
      // 'password'
    );
    //<EMAIL>
    //password
    if (res.isSuccessful) {
      ResLogin resLogin = ResLogin.fromJson(jsonDecode(res.bodyString));
      if (resLogin.success ?? false) {
        final token = resLogin.data?.accessToken ?? '';
        // 將token寫入GetStorage
        final boxProvider = getIt<BoxProvider>();
        final box = boxProvider.getGsBox(Boxes.setting, withNamespace: false);
        box.write(Keys.token, token);
        // 將api res 寫入user DB
        var user = await HiveUserProvider.getUser();
        user.memberToken = token;
        await HiveUserProvider.updateUser(user);

        var userData = await HiveUserProvider.getUserOrNull();
        final bearerToken = 'Bearer ${userData?.memberToken}';
        final res = await apiMyProfile(
          context,
          bearerToken,
          groupName,
          groupCode,
        );

        if (res) {
          print('登入成功，user 資料： ${user.toString()}');

          pushTo(
            PageName.main.toString(),
            builder: (context) => MainScreen(),
            isPushReplacement: true,
          );
          return;
        }
      }

      //api錯誤處理
      if (!(resLogin.success ?? false)) {
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: resLogin.message ?? '',
          );
        }

        return;
      }
    } else {
      if (context.mounted) {
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return;
    }
    return;
  }

  ///get my_profile
  Future<bool> apiMyProfile(
    BuildContext context,
    String token,
    String groupName,
    String groupCode,
  ) async {
    showLoadingOverlay();
    final res = await ApiService.create().getMyProfile(token);
    if (res.isSuccessful) {
      dismissLoadingOverlay();
      ResMyProfile resMyProfile = ResMyProfile.fromJson(
        jsonDecode(res.bodyString),
      );
      if (resMyProfile.success ?? false) {
        //將api res 寫入user DB
        var user = await HiveUserProvider.getUser();
        user.name = resMyProfile.data?.name ?? '';
        user.email = resMyProfile.data?.email ?? '';
        user.stuffPermissions = resMyProfile.data?.userType ?? '';
        user.memberId = resMyProfile.data?.id;
        await HiveUserProvider.updateUser(user);
        myPrint('groupName: $groupName');
        myPrint(
          'resMyProfile.data?.role?.groupName: ${resMyProfile.data?.role?.groupName}',
        );
        myPrint('groupCode: ${resMyProfile.data?.role?.groupCode}');

        if ((groupName.trim() != resMyProfile.data?.role?.groupName?.trim()) ||
            (groupCode.trim() != resMyProfile.data?.role?.groupCode?.trim())) {
          if (context.mounted) {
            sbarStyleDialog(
              context: context,
              yesAction: () {
                navigatorKey.currentState?.pop();
              },
              title: '錯誤',
              content: '請確認商店名稱或商店代號。',
            );
          }

          return false;
        }
        return true;
      }

      if (!(resMyProfile.success ?? false)) {
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: resMyProfile.message ?? '',
          );
        }

        return false;
      }
    } else {
      dismissLoadingOverlay();
      if (context.mounted) {
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return false;
    }
    return false;
  }

  Future<BasicRes<LoginRes>> postLogin(LoginReq loginReq) async {
    return basicApi.postBasic<LoginRes>(
      '/login',
      data: loginReq.toJson(),
      parse: (j) => LoginRes.fromJson(j as Map<String, dynamic>),
    );
  }
}
