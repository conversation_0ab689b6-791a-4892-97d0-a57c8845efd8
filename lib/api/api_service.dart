import 'dart:io';
import 'package:chopper/chopper.dart';
import 'package:flutter/cupertino.dart';
import '../flavor/flavor.dart';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart' as httpClient;

import '../model/member/member_repository.dart';
import 'auth_interceptor.dart';
import 'my_authenticator.dart';

part 'api_service.chopper.dart';

/*
  修改或新增api之後需要下這個指令：
  dart run build_runner build --delete-conflicting-outputs
 */

/// 可以通過外面注入改掉
ValueGetter<http.BaseClient> createClient = createDefaultClient;

http.BaseClient createDefaultClient() {
  return httpClient.IOClient(
    HttpClient()
      ..connectionTimeout = const Duration(seconds: 60)
      ..badCertificateCallback =
      ((X509Certificate cert, String host, int port) => true),
  );
}

///一般api
@ChopperApi()
abstract class ApiService extends ChopperService {
  static ApiService create() {
    final innerClient = createClient();

    final client = ChopperClient(
      client: innerClient,
      baseUrl: Uri.parse(FlavorConfig().values.baseUrl),
      authenticator: MyAuthenticator(getAuthMemberRepository),
      services: [_$ApiService()],
      converter: const JsonConverter(),
      interceptors: [
        AuthInterceptor(getAuthMemberRepository),
        HttpLoggingInterceptor(),
      ],
    );

    return _$ApiService(client);
  }

  ///會員登入
  @Post(path: '/login')
  Future<Response> login(
      @Field('email') String account,
      @Field('password') String password,
      );

  ///取得商品類別列表
  @GET(path: '/products_types')
  Future<Response> getProductsTypes(
      @Header('Authorization') String token,
      );


  ///取得課程性質列表
  @GET(path: '/programs_course_types')
  Future<Response> getProgramsCourseTypes(
      @Header('Authorization') String token,
      );

  ///Get current user's profile and role
  @GET(path: '/my-profile')
  Future<Response> getMyProfile(
      @Header('Authorization') String token,
      );

  ///取得業績計算人員
  @GET(path: '/employees')
  Future<Response> getEmployees(
      @Header('Authorization') String token,
      @Query('search') String?  search, //Search by employee name, number, or email
      @Query('per_page') int  perPage,
      @Query('page') int  page,
      );


  ///取得品牌會員
  @GET(path: '/member/profile')
  Future<Response> getMemberProfile(
      @Header('Authorization') String token,
      );

  ///取得課程列表
  @GET(path: '/programs')
  Future<Response> getPrograms(
      @Header('Authorization') String token,
      @Query('searchkey') String?  search, //搜尋關鍵字（課程名稱、代碼、狀態）
      @Query('course_type_name') String?  courseTypeName,
      @Query('per_page') int  perPage,
      @Query('page') int  page,
      );

  ///取得商品列表
  @GET(path: '/products')
  Future<Response> getProducts(
      @Header('Authorization') String token,
      @Query('searchkey') String?  search, //搜尋關鍵字可搜尋 ch_name, en_name, code, status）
      @Query('product_type_name') String?  productTypeName,
      @Query('per_page') int  perPage,
      @Query('page') int  page,
      );

  ///取得支付方式
  @GET(path: '/payment-methods')
  Future<Response> getPaymentMethods(
      @Header('Authorization') String token,
      @Query('per_page') int  perPage,
      );

  ///取得儲值金列表
  @GET(path: '/prepaid')
  Future<Response> getPrepaid(
      @Header('Authorization') String token,
      );

}
