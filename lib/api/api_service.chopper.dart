// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_service.dart';

// **************************************************************************
// ChopperGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
final class _$ApiService extends ApiService {
  _$ApiService([ChopperClient? client]) {
    if (client == null) return;
    this.client = client;
  }

  @override
  final Type definitionType = ApiService;

  @override
  Future<Response<dynamic>> login(
    String account,
    String password,
  ) {
    final Uri $url = Uri.parse('/login');
    final $body = <String, dynamic>{
      'email': account,
      'password': password,
    };
    final Request $request = Request(
      'POST',
      $url,
      client.baseUrl,
      body: $body,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getProductsTypes(String token) {
    final Uri $url = Uri.parse('/products_types');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getProgramsCourseTypes(String token) {
    final Uri $url = Uri.parse('/programs_course_types');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getMyProfile(String token) {
    final Uri $url = Uri.parse('/my-profile');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getEmployees(
    String token,
    String? search,
    int perPage,
    int page,
  ) {
    final Uri $url = Uri.parse('/employees');
    final Map<String, dynamic> $params = <String, dynamic>{
      'search': search,
      'per_page': perPage,
      'page': page,
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getMemberProfile(String token) {
    final Uri $url = Uri.parse('/member/profile');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getPrograms(
    String token,
    String? search,
    String? courseTypeName,
    int perPage,
    int page,
  ) {
    final Uri $url = Uri.parse('/programs');
    final Map<String, dynamic> $params = <String, dynamic>{
      'searchkey': search,
      'course_type_name': courseTypeName,
      'per_page': perPage,
      'page': page,
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getProducts(
    String token,
    String? search,
    String? productTypeName,
    int perPage,
    int page,
  ) {
    final Uri $url = Uri.parse('/products');
    final Map<String, dynamic> $params = <String, dynamic>{
      'searchkey': search,
      'product_type_name': productTypeName,
      'per_page': perPage,
      'page': page,
    };
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getPaymentMethods(
    String token,
    int perPage,
  ) {
    final Uri $url = Uri.parse('/payment-methods');
    final Map<String, dynamic> $params = <String, dynamic>{'per_page': perPage};
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      parameters: $params,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }

  @override
  Future<Response<dynamic>> getPrepaid(String token) {
    final Uri $url = Uri.parse('/prepaid');
    final Map<String, String> $headers = {
      'Authorization': token,
    };
    final Request $request = Request(
      'GET',
      $url,
      client.baseUrl,
      headers: $headers,
    );
    return client.send<dynamic, dynamic>($request);
  }
}
