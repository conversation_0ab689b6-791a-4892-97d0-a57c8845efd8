import 'package:dio/dio.dart';
import 'package:get_storage/get_storage.dart';
import '../constants/keys.dart';

/// Dio 的授權攔截器：
/// - 在每個請求加上 Bearer Token（若存在）
/// - 遇到 401 嘗試刷新一次 token 並重試請求
class DioAuthInterceptor extends Interceptor {
  final GetStorage _box;
  DioAuthInterceptor(this._box);

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    final token = _box.read(Keys.token);
    if (token.isNotEmpty) {
      options.headers['Authorization'] = 'Bearer $token';
    }
    super.onRequest(options, handler);
  }
}
