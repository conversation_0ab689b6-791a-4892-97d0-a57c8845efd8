import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:sbar_pos/model/api_model/basic_res.dart';

/// 專責提供以 BasicRes<T> 為包裝的通用 API 呼叫（含 multipart/form-data）
class BasicApi {
  BasicApi(this.dio);
  final Dio dio;

  // -------------------- 共用小工具 --------------------

  // 將 data 轉成 List<T> 的小工具
  List<E> _parseList<E>(Object? json, E Function(Object? e) parseItem) {
    if (json is List) {
      return json.map((e) => parseItem(e)).toList();
    }
    return <E>[]; // 保守處理：不是 List 則回空陣列
  }

  // 將動態資料轉換為 FormData，並附加 files（如有）
  FormData _toFormData(
    dynamic data, {
    Map<String, MultipartFile>? files,
  }) {
    if (data is FormData) {
      if (files != null && files.isNotEmpty) {
        files.forEach((key, file) {
          data.files.add(MapEntry(key, file));
        });
      }
      return data;
    }

    final map = <String, dynamic>{};
    if (data is Map<String, dynamic>) {
      map.addAll(data);
    }
    if (files != null && files.isNotEmpty) {
      map.addAll(files);
    }
    return FormData.fromMap(map);
  }

  // -------------------- JSON: 單一物件 --------------------

  Future<BasicRes<T>> postBasic<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'POST',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.post<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<T>> getBasic<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'GET',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
    );

    final res = await dio.get<dynamic>(
      path,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<T>> putBasic<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PUT',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.put<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<T>> patchBasic<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PATCH',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.patch<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<T>> deleteBasic<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'DELETE',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.delete<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  // -------------------- JSON: 清單物件 --------------------

  Future<BasicRes<List<T>>> postBasicList<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'POST',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.post<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<List<T>>> getBasicList<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'GET',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
    );

    final res = await dio.get<dynamic>(
      path,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<List<T>>> putBasicList<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PUT',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.put<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<List<T>>> deleteBasicList<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'DELETE',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: baseOptions.contentType ?? 'application/json',
    );

    final res = await dio.delete<dynamic>(
      path,
      data: data,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  // -------------------- multipart/form-data: 單一物件 --------------------

  Future<BasicRes<T>> postFormBasic<T>(
    String path, {
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final formData = _toFormData(data, files: files);
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'POST',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: 'multipart/form-data',
    );

    final res = await dio.post<dynamic>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<T>> putFormBasic<T>(
    String path, {
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final formData = _toFormData(data, files: files);
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PUT',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: 'multipart/form-data',
    );

    final res = await dio.put<dynamic>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<T>> patchFormBasic<T>(
    String path, {
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parse,
  }) async {
    final formData = _toFormData(data, files: files);
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PATCH',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: 'multipart/form-data',
    );

    final res = await dio.patch<dynamic>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<T>.fromJson(map, parse);
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  // -------------------- multipart/form-data: 清單物件 --------------------

  Future<BasicRes<List<T>>> postFormBasicList<T>(
    String path, {
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final formData = _toFormData(data, files: files);
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'POST',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: 'multipart/form-data',
    );

    final res = await dio.post<dynamic>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<List<T>>> putFormBasicList<T>(
    String path, {
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final formData = _toFormData(data, files: files);
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PUT',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: 'multipart/form-data',
    );

    final res = await dio.put<dynamic>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }

  Future<BasicRes<List<T>>> patchFormBasicList<T>(
    String path, {
    dynamic data,
    Map<String, MultipartFile>? files,
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    required T Function(Object? json) parseItem,
  }) async {
    final formData = _toFormData(data, files: files);
    final baseOptions = options ?? Options();
    final mergedOptions = baseOptions.copyWith(
      method: 'PATCH',
      headers: {
        if (baseOptions.headers != null) ...baseOptions.headers!,
        if (headers != null) ...headers,
      },
      sendTimeout: sendTimeout ?? baseOptions.sendTimeout,
      receiveTimeout: receiveTimeout ?? baseOptions.receiveTimeout,
      contentType: 'multipart/form-data',
    );

    final res = await dio.patch<dynamic>(
      path,
      data: formData,
      queryParameters: queryParameters,
      options: mergedOptions,
      cancelToken: cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
    );

    final dynamic body = res.data;
    final Map<String, dynamic> map = body is Map<String, dynamic>
        ? body
        : (jsonDecode(body as String) as Map<String, dynamic>);

    final basic = BasicRes<List<T>>.fromJson(
      map,
      (obj) => _parseList<T>(obj, parseItem),
    );
    if (!basic.success) {
      throw ApiException(basic.message ?? 'Unknown API error');
    }
    return basic;
  }
}
