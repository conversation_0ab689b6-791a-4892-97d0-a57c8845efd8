import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/main_provider.dart';
import 'package:sbar_pos/push_to/push_to_web.dart';
import 'package:sbar_pos/screen/login/login_screen_view_model.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../gen/r.dart';
import '../../hive/hive_provider/hive_user_provider.dart';
import '../../logger/my_print.dart';
import '../../model/login/staff_login_list_model.dart';
import '../../provider/version_provider.dart';
import '../../resource/MyColor.dart';
import '../../widget/btn/btn_with_loading.dart';
import '../../widget/cached_network_image/error_cached_network_image.dart';
import '../../widget/dialog/sbar_style_dialog.dart';
import '../../widget/textField/my_text_field.dart';
import '../start/my_app.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final vm = LoginScreenViewModel();


  @override
  void initState() {
    _initVersion();
    _readFromUserDB();
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  ///版本資訊提醒彈窗
  void _appVersionRemind() {
    return WidgetsBinding.instance.addPostFrameCallback((_) {
      final mainVm = context.read<MainProvider>();
      sbarStyleDialog(
          context: context,
          yesAction: () {
            if (Platform.isIOS) {
              //TODO:更換正確網址
              pushToWeb(webUrl: mainVm.appStoreLink);

            } else {
              //TODO:更換正確網址
              pushToWeb(webUrl: mainVm.googlePlayLink);
            }
          },
          title: '提醒您',
          content: '目前已有新版，請至商店更新。(測試階段點擊任意處可關閉彈窗)');
    });
  }

  ///初始化版本號
  void _initVersion() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final versionVm = context.read<VersionProvider>();
      versionVm.initPackageInfo();
    });
  }


  ///初始化userDB
  void _readFromUserDB() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      myPrint('userDBInit');
      var user = await HiveUserProvider.getUserOrNull();
      myPrint(
          'shopId: ${user?.shopId}, shopName: ${user?.shopName}');
      if((user?.shopName ?? '').isEmpty){
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '提醒您！',
          content: '首次使用請先綁定商店名稱與商店代號。',
        );
        return;
      }
      vm.shopNameController.text = user?.shopName ?? '';
      vm.shopCodeController.text = user?.shopId ?? '';
      setState(() {});
      await HiveUserProvider.showData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final mainVm = context.read<MainProvider>();
    return ChangeNotifierProvider.value(
      value: vm,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          body: Stack(
            children: [
              Positioned.fill(
                child: Image(
                  image: R.image.image_login_bg(),
                  fit: BoxFit.cover,
                ),
              ),

              Padding(
                padding: EdgeInsets.symmetric(horizontal: 82.w, vertical: 66.h),
                child: Center(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ///填充用
                      Expanded(
                        child: IgnorePointer(
                          ignoring: true,
                          child: Opacity(
                            opacity: 0.0,
                            child: _staffLoginList(),
                          ),
                        ),
                      ),
                      SizedBox(width: 91.w),

                      ///登入區塊
                      _loginArea(),

                      ///員工登入列表
                      if (mainVm.isShowLoginStaffList) SizedBox(width: 91.w),

                      Expanded(
                        child: IgnorePointer(
                          ignoring: !mainVm.isShowLoginStaffList,
                          child: Opacity(
                            opacity: mainVm.isShowLoginStaffList ? 1 : 0,
                            child: _staffLoginList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///帳號密碼區塊
  Widget _loginArea() {
    return SingleChildScrollView(
      padding: EdgeInsets.zero,
      physics: BouncingScrollPhysics(),
      child: Column(
        children: [
          SizedBox(height: 80.h),

          Image(
            image: R.image.icon_sbar_logo(),
            width: 167.45.w,
            height: 183.3.h,
          ),
          SizedBox(height: 30.h),

          Container(
            width: 636.w,
            height: (735+25).h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(40.r),
              boxShadow: [
                BoxShadow(
                  color: MyColors.green_214_226_179,
                  offset: const Offset(8, 8),
                  blurRadius: 80,
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 94.w, vertical: 68.5.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: Text(
                      'Sbar',
                      style: TextStyle(
                        fontSize: 44.sp,
                        color: MyColors.green_157_193_65,
                      ),
                    ),
                  ),
                  SizedBox(height: 40.h),

                  Row(
                    children: [
                      Text(
                        '門市名稱：',
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: MyColors.green_129_128_94,
                        ),
                      ),
                      _baseTextFieldForShop(vm.shopNameController,'請輸入門市名稱'),
                    ],
                  ),
                  SizedBox(height: 5.h),

                  Row(
                    children: [
                      Text(
                        '門市代號：',
                        style: TextStyle(
                          fontSize: 28.sp,
                          color: MyColors.green_129_128_94,
                        ),
                      ),
                      _baseTextFieldForShop(vm.shopCodeController,'請輸入門市代號'),
                    ],
                  ),

                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 20.h),
                    child: Divider(thickness: 1, height: 1),
                  ),


                  //帳號
                  _accountArea(),
                  SizedBox(height: 17.h),

                  //密碼
                  _pwdArea(),
                  SizedBox(height: 40.h),

                  //登入
                  _loginBtn(),
                  SizedBox(height: 40.h),

                  //版本號
                Selector<VersionProvider, (String, String)>(
                    selector: (context, vm) => (vm.buildVersion, vm.version),
                    shouldRebuild: (pre, next) => true,
                    builder: (context, record, _) {
                      final buildVersion = record.$1;
                      final version = record.$2;
                    return  Align(
                      alignment: Alignment.center,
                      child: Text(
                        'v$version ($buildVersion)',
                        style: TextStyle(
                          fontSize: 23.sp,
                          color: MyColors.green_192_210_136,
                        ),
                      ),
                    );
                    }

                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  ///商店輸入框
  Widget _baseTextFieldForShop(TextEditingController controller,String hint) {
    return Expanded(
                      child: Padding(
                        padding:  EdgeInsets.only(top: 16.h),
                        child: MyTextField(
                          style: TextStyle(
                            fontSize: 28.sp,
                            color: MyColors.green_129_128_94,
                          ),
                          hintStyle: TextStyle(
                            fontSize: 28.sp,
                            color: MyColors.grey_164_164_164,
                          ),
                          hint: hint,
                          textAlign: TextAlign.start,
                          enterTextPadding: EdgeInsets.only(left: 24.w),
                          textAlignVerticalTop: true,
                          controller: controller ,
                          border: 0,
                          borderColor: Colors.transparent,
                          height: 50.h,
                          // underlineColor: MyColors.grey_219_219_219,
                          underlineColor: Colors.transparent,
                          focusUnderlineColor: Color(0xFF007AFF),
                        ),
                      ),
                    );
  }

  ///登入按鈕
  Widget _loginBtn() {
    return Selector<LoginScreenViewModel, bool>(
      selector: (context, vm) => vm.loading,
      builder: (context, loading, _) {
        return BtnWithLoading(
          title: '登入',
          isLoading: loading,
          onTap: () async {
            if (context.mounted) {
              vm.loginLogic(context, () async {});
            }
          },
        );
      },
    );
  }

  ///帳號區塊
  Widget _accountArea() {
    return Row(
          children: [
            Text(
              '帳號：',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(width: 17.w),
            Expanded(
              child: MyTextField(
                isSingleLine: true,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
                borderColor: MyColors.grey_235_235_235,
                hint: '請輸入帳號',
                textAlign: TextAlign.start,
                enterTextPadding: EdgeInsets.only(left: 24.w),
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.grey_164_164_164,
                ),
                controller: vm.accountController,
                border: 100.r,
                height: 74.h,
              ),
            ),
          ],
        )
        .animate(
          autoPlay: false,
          onInit: (controller) {
            vm.animationAccountController = controller;
          },
        )
        .shakeX(duration: const Duration(milliseconds: 500));
  }

  ///密碼區塊
  Widget _pwdArea() {
    return Row(
          children: [
            Text(
              '密碼：',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(width: 17.w),

            Expanded(
              child: MyTextField(
                isSingleLine: true,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.green_129_128_94,
                  letterSpacing: 1.5,
                ),
                obscureText: true,

                borderColor: MyColors.grey_235_235_235,
                hint: '請輸入密碼',
                textAlign: TextAlign.start,
                enterTextPadding: EdgeInsets.only(left: 24.w),
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.grey_164_164_164,
                ),
                controller: vm.pwdController,
                border: 100.r,
                height: 74.h,
              ),
            ),
          ],
        )
        .animate(
          autoPlay: false,
          onInit: (controller) {
            vm.animationPwdController = controller;
          },
        )
        .shakeX(duration: const Duration(milliseconds: 500));
  }

  ///員工登入列表
  Widget _staffLoginList() {
    return Selector<LoginScreenViewModel, List<StaffLoginListModel>>(
      selector: (context, vm) => vm.staffLoginListModel,
      builder: (context, data, _) {
        return ListView.separated(
          padding: EdgeInsets.zero,
          physics: BouncingScrollPhysics(),
          itemCount: data.length,
          itemBuilder: (context, index) {
            final model = data[index];
            return _staffLoginListWidget(model)
                .animate(delay: 100.ms * index)
                .fadeIn(duration: 300.ms)
                .slideY(
                  begin: 0.5,
                  end: 0,
                  duration: 300.ms,
                  curve: Curves.easeOut,
                );
          },
          separatorBuilder: (context, index) => SizedBox(height: 37),
        );
      },
    );
  }

  ///員工登入列表元件
  Widget _staffLoginListWidget(StaffLoginListModel model) {
    return Selector<LoginScreenViewModel, bool>(
      selector: (context, vm) => vm.loading,
      builder: (context, loading, _) {
        return InkWell(
          onTap: () {
            vm.accountController.text = model.name;
            setState(() {});
          },
          child: Container(
            width: 469.w,
            decoration: BoxDecoration(
              color: const Color(0xCCFFFFFF),
              borderRadius: BorderRadius.circular(40.r),
              border: Border.all(color: Color(0xFFEBEBEB), width: 1.w),
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 33.w, vertical: 22.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  //大頭照
                  ClipRRect(
                    borderRadius: BorderRadius.circular(50.r),
                    child: CachedNetworkImage(
                      imageUrl: model.avatar,
                      height: 100.w,
                      width: 100.w,
                      fit: BoxFit.cover,
                      errorWidget: (context, url, error) =>
                          const ErrorCachedNetworkImage(),
                    ),
                  ),

                  SizedBox(width: 29.w),

                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          model.position,
                          style: TextStyle(
                            fontSize: 23.sp,
                            color: MyColors.green_121_131_90,
                          ),
                        ),
                        SizedBox(height: 14.h),
                        Text(
                          model.name,
                          style: TextStyle(
                            fontSize: 28.sp,
                            color: MyColors.brown_57_54_18,
                          ),
                        ),
                        SizedBox(height: 14.h),
                        Text(
                          '${model.loginTime} 登入',
                          style: TextStyle(
                            fontSize: 23.sp,
                            color: MyColors.green_121_131_90,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
