import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';

import '../../model/base/base_model.dart';
import '../../model/class/class_model.dart';
import '../../model/product/product_model.dart';
import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/shape/circle.dart';
import '../shopping_car/shopping_car_screen.dart';
import 'class_screen_view_model.dart';

class ClassScreen extends StatefulWidget {
  const ClassScreen({super.key});

  @override
  State<ClassScreen> createState() => _ClassScreenState();
}

class _ClassScreenState extends State<ClassScreen> {
  final vm = ClassScreenViewModel();
  RefreshController controller = RefreshController();

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: Padding(
        padding: EdgeInsets.only(bottom: 15.h, left: 16.w),
        child: Consumer<ShoppingCarProvider>(
          builder: (context, data, _) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ///左側課程類別列表
                Padding(
                  padding: EdgeInsets.only(top: 138.h), //這邊自己管控高度Padding
                  child: _classTypeList(),
                ),

                ///主課程列表
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(top: 138.h), //這邊自己管控高度Padding
                    child: _classList(),
                  ),
                ),

                SizedBox(width: 17.w),

                ///右側購物車列表
                ShoppingCarScreen(),
              ],
            );
          },
        ),
      ),
    );
  }

  ///左側商品類別列表
  Widget _classTypeList() {
    return SizedBox(
      width: 196.w,
      child: Selector<ShoppingCarProvider, List<BaseModel>>(
        selector: (context, vm) => vm.classType,
        builder: (context, classType, _) {
          return ListView.separated(
            padding: EdgeInsets.zero,
            physics: BouncingScrollPhysics(),
            itemCount: classType.length,
            itemBuilder: (context, index) {
              final model = classType[index];
              return _productTypeListWidget(model, index);
            },
            separatorBuilder: (context, index) => SizedBox(height: 19.h),
          );
        },
      ),
    );
  }

  ///課程類別列表元件(左側)
  Widget _productTypeListWidget(BaseModel model, int index) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final bool isSelected = index == shoppingCarVm.classSelectedTypeIndex;
    return ContainerWithRadius(
      isHaveBorder: true,
      onTap: () async {

          shoppingCarVm.selectClassType(index);


        // 重置分頁資訊
        shoppingCarVm.currentProgramPage = 1;
        shoppingCarVm.hasMoreProgram = true;

        await shoppingCarVm.apiGetPrograms(
          context,
          courseTypeName: shoppingCarVm.classType[shoppingCarVm.classSelectedTypeIndex].title,
          clearData: true,
          isHaveLoading: true
        ); //取得課程列表
      },
      w: 196.w,
      h: 63.h,
      r: 20.r,
      color: isSelected ? MyColors.green_157_193_65 : Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          model.title,
          style: TextStyle(
            fontSize: 25.sp,
            color: isSelected ? Colors.white : MyColors.green_129_128_94,
          ),
        ),
      ),
    );
  }

  ///主課程列表
  Widget _classList() {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.classModel,
      builder: (context, classModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        return shoppingCarVm.mainLoading ? SizedBox.shrink() : SmartRefresher(
                enablePullDown: true,
                enablePullUp: true,
                controller: controller,
                onRefresh: () async {
                  try {
                    await shoppingCarVm.apiProgramsPullDown(
                      context,
                      courseTypeName: shoppingCarVm.classType[shoppingCarVm.classSelectedTypeIndex].title,
                    );
                  } finally {
                    controller.refreshCompleted(); // 告訴刷新結束
                  }
                },
                onLoading: () async {
                  try {
                    await shoppingCarVm.apiProgramsLoading(
                      context,
                      courseTypeName: shoppingCarVm.classType[shoppingCarVm.classSelectedTypeIndex].title,
                    ); // 上拉加載更多
                  } finally {
                    if (shoppingCarVm.hasMoreProgram) {
                      controller.loadComplete(); // 還有更多資料
                    } else {
                      controller.loadNoData(); // 沒有更多資料
                    }
                  }
                },
                child: ListView.separated(
                  padding: EdgeInsets.only(left: 27.w),
                  physics: BouncingScrollPhysics(),
                  itemCount: classModel.length,
                  itemBuilder: (context, index) {
                    final model = classModel[index];
                    return _classListWidget(model);
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 20.h),
                ),
              );
      },
    );
  }



  /// 課程主列表元件
  Widget _classListWidget(ProductModel model) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();

    // 判斷是否已選取
    final isSelected = shoppingCarVm.shoppingCarListModel.any(
          (e) => e.productId == model.productId,
    );

    // 取得選取數量
    final selectAmount = shoppingCarVm.shoppingCarListModel
        .firstWhere(
          (e) => e.productId == model.productId,
      orElse: () => ProductModel(
        productId: model.productId,
        title: '',
        price: 0,
        selectAmount: 0,
        totalAmount: 0,
        type: 1,
        isSelect: false,
        isTempSelection: false,
      ),
    )
        .selectAmount;

    return InkWell(
      onTap: () {
        if (!isSelected) {
          shoppingCarVm.addToShoppingCarByProductId(productId: model.productId);
        } else {
          shoppingCarVm.removedShoppingCarByProductId(productId: model.productId);
        }
        setState(() {});
      },
      child: Container(
        width: double.infinity.w,
        height: 188.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(3, 6),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            /// 課程名稱
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 15.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.title,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    model.subTitle ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ],
              ),
            ),

            /// 價格文字
            Positioned(
              left: isSelected ? 105.w : 19.w,
              bottom: 17.h,
              child: Text(
                '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
                style: TextStyle(
                  fontSize: 25.sp,
                  color: MyColors.grey_119_119_119,
                ),
              ),
            ),

            /// 左下選取數量 (依 isSelected 顯示，即使 selectAmount 為 0)
            if (isSelected)
              Positioned(
                bottom: -3.h,
                left: -5.w,
                child: Circle(
                  color: MyColors.yellow_251_227_140,
                  size: 63.w,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x669DC141),
                      offset: Offset(3, 6),
                      blurRadius: 10,
                    ),
                  ],
                  child: Center(
                    child: Text(
                      selectAmount.toString(),
                      style: TextStyle(
                        color: MyColors.green_129_128_94,
                        fontSize: 25.sp,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

}
