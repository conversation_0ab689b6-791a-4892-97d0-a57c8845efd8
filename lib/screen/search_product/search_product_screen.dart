import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/screen/search_product/search_product_screen_view_model.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/hint/empty_hint.dart';

import '../../model/class/class_model.dart';
import '../../model/product/product_model.dart';
import '../../provider/shopping_car_provider.dart';
import '../../util/MyRoutes.dart';
import '../../util/StringUtils.dart';
import '../../widget/btn/return_and_submit_btn.dart';
import '../../widget/btn/scan_btn.dart';
import '../../widget/radio_btn/radio_btn_with_text.dart';
import '../../widget/scanner/scanner.dart';
import '../../widget/shape/circle.dart';
import '../../widget/textField/my_text_field.dart';
import '../scan/scan_screen.dart';
import '../start/my_app.dart';

class SearchProductScreen extends StatefulWidget {
  final VoidCallback backOnTap; //返回
  final Function(ProductModel?, String type) submitOnTap; //送出
  const SearchProductScreen({
    super.key,
    required this.backOnTap,
    required this.submitOnTap,
  });

  @override
  State<SearchProductScreen> createState() => _SearchProductScreenState();
}

class _SearchProductScreenState extends State<SearchProductScreen> {
  final vm = SearchProductScreenViewModel();
   int _selectedIndex = -1;
  RefreshController controller = RefreshController();

  @override
  void initState() {
    _initType();
    super.initState();
  }

  ///預設類別
  void _initType() {
    return WidgetsBinding.instance.addPostFrameCallback((_) {
      vm.type = '商品';
      setState(() {});
    });
  }

  @override
  void dispose() {
    vm.dispose();
    vm.searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(top: 75.h),
          child: Column(
            children: [
              Text(
                '搜尋商品',
                style: TextStyle(
                  fontSize: 44.sp,
                  color: MyColors.brown_57_54_18,
                ),
              ),
              SizedBox(height: 35.h),

              ///搜尋列
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 145.h),
                child: _searchBar(),
              ),

              SizedBox(height: 35.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 145.h),
                child: Divider(thickness: 1, height: 1),
              ),
              SizedBox(height: 51.h),

              ///列表
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 145.h),
                  child: vm.type == '商品' ? _productList() : _classList(),
                ),
              ),

              ///返回與送出按鈕
              ReturnAndSubmitBtn(
                backOnTap: (){
                  context.read<ShoppingCarProvider>().restoreAfterSearch();
                  widget.backOnTap();
                },
                submitOnTap: () {
                  final shoppingCarVm = context.read<ShoppingCarProvider>();

                  ProductModel? selectedModel;
                  String? selectedId;
                  String? type;

                  if (vm.type == '商品' &&
                      _selectedIndex >= 0 &&
                      _selectedIndex < shoppingCarVm.productModel.length) {
                    selectedModel = shoppingCarVm.productModel[_selectedIndex];
                    selectedId = selectedModel.productId;
                    type = '商品';
                  } else if (vm.type == '課程' &&
                      _selectedIndex >= 0 &&
                      _selectedIndex < shoppingCarVm.classModel.length) {
                    selectedModel = shoppingCarVm.classModel[_selectedIndex];
                    selectedId = selectedModel.productId;
                    type = '課程';
                  }
                  print('selectedId: ${selectedId}');

                  shoppingCarVm.restoreAfterSearch(
                    selectedId: selectedId,
                    type: type,
                  );

                  widget.submitOnTap(selectedModel, vm.type ?? '');
                },

              ),
            ],
          ),
        ),
      ),
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return Selector<SearchProductScreenViewModel, String?>(
      selector: (context, vm) => vm.type,
      builder: (context, value, _) {
        return ContainerWithRadius(
          isHaveBorder: false,
          w: double.infinity.w,
          h: 86.h,
          r: 20.r,
          color: MyColors.green_157_193_65.withOpacity(0.3),
          child: Padding(
            padding: EdgeInsets.only(left: 55.w, right: 71.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ///類別單選區塊
                _typeRadioBtnArea(),

                SizedBox(width: 50.w),

                ///輸入框
                Expanded(
                  child: MyTextField(
                    isSingleLine: true,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                    borderColor: MyColors.grey_235_235_235,
                    hint: '請輸入名稱',
                    textAlign: TextAlign.start,
                    enterTextPadding: EdgeInsets.only(left: 24.w),
                    hintStyle: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                    controller: vm.searchController,
                    border: 20.r,
                    height: 54.h,
                  ),
                ),

                SizedBox(width: 31.w),

                ///掃碼按鈕
                ScanBtn(
                  onTap: () {
                    runBarCodeScannerCamForOrder();
                  },
                ),

                SizedBox(width: 31.w),

                ///搜尋按鈕
                ContainerWithRadius(
                  onTap: () async {
                    final shoppingCarVm = context.read<ShoppingCarProvider>();
                    if (vm.searchController.text.isEmpty) return;

                    vm.setSearching(true);


                    if (vm.type == '課程') {
                      await shoppingCarVm.apiGetPrograms(
                        context,
                        search: vm.searchController.text,
                        clearData: true,
                        isHaveLoading: true,
                        isSearch: true
                      );
                    }
                    if (vm.type == '商品') {
                      print(
                        ' vm.searchController.text: ${vm.searchController.text}',
                      );

                      await shoppingCarVm.apiGetProduct(
                        context,
                        search: vm.searchController.text,
                        clearData: true,
                        isHaveLoading: true,
                          isSearch: true
                      );
                    }
                  },
                  isHaveBorder: true,
                  w: 196.w,
                  h: 62.w,
                  r: 20.r,
                  color: MyColors.green_157_193_65,
                  child: Center(
                    child: Text(
                      '搜尋',
                      style: TextStyle(fontSize: 25.sp, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  ///類別單選
  Widget _typeRadioBtnArea() {
    final TextStyle typeStyle = TextStyle(
      fontSize: 36.sp,
      color: MyColors.green_129_128_94,
    );
    return Padding(
      padding: EdgeInsets.only(top: 4.h),
      child: Row(
        children: [
          RadioBtnWithText(
            isExpand: false,
            selected: vm.type,
            unselectedColor: MyColors.grey_112_112_112,
            subTitleStyle: typeStyle,
            text: '課程',
            onChange: (value) async {
              vm.type = value;
              (context as Element).markNeedsBuild();
            },
          ),
          SizedBox(width: 50.w),
          RadioBtnWithText(
            isExpand: false,
            selected: vm.type,
            unselectedColor: MyColors.grey_112_112_112,
            subTitleStyle: typeStyle,
            text: '商品',
            onChange: (value) async {
              vm.type = value;
              (context as Element).markNeedsBuild();
            },
          ),
        ],
      ),
    );
  }

  ///商品列表
  Widget _productList() {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.productModel,
      shouldRebuild: (p, n) => true,
      builder: (context, productModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        if (!vm.isOnSearch) {
          return EmptyHint(hint: '輸入關鍵字，幫你找找看有沒有合適的商品喔～');
        }
        if (productModel.isEmpty && vm.isOnSearch) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _onSearchResultEmpty();
          });
          return EmptyHint(hint: '暫時沒有結果，換個關鍵字搜尋吧～');
        }
        return SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          controller: controller,
          onRefresh: () async {
            try {
              if (vm.searchController.text.isNotEmpty) {
                await shoppingCarVm.apiProductPullDown(
                  context,
                  productTypeName: vm.searchController.text,
                );
              }
            } finally {
              controller.refreshCompleted();
            }
          },
          onLoading: () async {
            try {
              if (vm.searchController.text.isNotEmpty) {
                await shoppingCarVm.apiProductLoading(
                  context,
                  productTypeName: vm.searchController.text,
                ); // 上拉加載更多
              }
            } finally {
              if (shoppingCarVm.hasMoreProgram) {
                controller.loadComplete();
              } else {
                controller.loadNoData();
              }
            }
          },
          child: GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.only(bottom: 42.h),
            physics: const BouncingScrollPhysics(),
            itemCount: productModel.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 5,
              crossAxisSpacing: 20.w,
              mainAxisSpacing: 20.h,
              childAspectRatio: 286 / 188,
            ),
            itemBuilder: (context, index) {
              final model = productModel[index];
              return _productListWidget(model, index);
            },
          ),
        );
      },
    );
  }

  ///商品列表元件
  Widget _productListWidget(ProductModel model, int index) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final isSelected = shoppingCarVm.shoppingCarListModel.any(
      (e) => e.productId == model.productId,
    );

    return InkWell(

      onTap: model.totalAmount <= 0
          ? () {}
          : () {

        final shoppingCarVm = context.read<ShoppingCarProvider>();

        if (!isSelected) {
          shoppingCarVm.addToShoppingCarByProductId(
            productId: model.productId,
          );
          setState(() {
            _selectedIndex = index; // <- 這裡選中
          });
        } else {
          shoppingCarVm.removedShoppingCarByProductId(
            productId: model.productId,
          );
          setState(() {
            _selectedIndex = -1; // <- 取消選中
          });
        }
      },

      child: Container(
        width: 286.w,
        height: 188.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(3, 6),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            /// 商品名稱
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 20.h),
              child: Text(
                model.title,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
              ),
            ),

            /// 價格文字
            Positioned(
              left: 19.w,
              bottom: 17.h,
              child: Text(
                '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
                style: TextStyle(
                  fontSize: 25.sp,
                  color: MyColors.grey_119_119_119,
                ),
              ),
            ),

            /// 右下角半圓角區塊
            Align(
              alignment: Alignment.bottomRight,
              child: Container(
                width: 76.w,
                height: 54.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    bottomRight: Radius.circular(20.r),
                  ),
                ),
                child: model.totalAmount <= 0
                    ? Center(
                        child: Text(
                          '售完',
                          style: TextStyle(
                            fontSize: 25.sp,
                            color: MyColors.red_230_75_75,
                          ),
                        ),
                      )
                    : Center(
                        child: Text(
                          model.totalAmount.toString(),
                          style: TextStyle(
                            fontSize: 20.sp,
                            color: MyColors.grey_119_119_119,
                          ),
                        ),
                      ),
              ),
            ),

            ///右上勾勾
            if (isSelected)
              Positioned(
                right: 11.w,
                top: 11.h,
                child: Circle(
                  color: MyColors.yellow_251_227_140,
                  size: 40.w,
                  child: Icon(Icons.check, color: MyColors.green_129_128_94),
                ),
              ),
          ],
        ),
      ),
    );
  }

  ///課程列表
  Widget _classList() {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.classModel,
      shouldRebuild: (p, n) => true,
      builder: (context, classModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        if (!vm.isOnSearch) {
          return EmptyHint(hint: '輸入關鍵字，幫你找找看有沒有合適的課程喔～');
        }
        if (classModel.isEmpty && vm.isOnSearch) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _onSearchResultEmpty();
          });

          return EmptyHint(hint: '暫時沒有結果，換個關鍵字搜尋吧～');
        }
        return SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          controller: controller,
          onRefresh: () async {
            try {
              if (vm.searchController.text.isEmpty) {
                await shoppingCarVm.apiProgramsPullDown(
                  context,
                  courseTypeName: vm.searchController.text,
                );
              }
            } finally {
              controller.refreshCompleted(); // 告訴刷新結束
            }
          },
          onLoading: () async {
            try {
              if (vm.searchController.text.isEmpty) {
                await shoppingCarVm.apiProgramsLoading(
                  context,
                  courseTypeName: vm.searchController.text,
                ); // 上拉加載更多
              }
            } finally {
              if (shoppingCarVm.hasMoreProgram) {
                controller.loadComplete(); // 還有更多資料
              } else {
                controller.loadNoData(); // 沒有更多資料
              }
            }
          },
          child: ListView.separated(
            padding: EdgeInsets.only(left: 155.w, right: 155.w, bottom: 77.h),
            itemCount: classModel.length,
            itemBuilder: (context, index) {
              final model = classModel[index];
              return _classListWidget(model, index);
            },
            separatorBuilder: (context, index) => SizedBox(height: 19.h),
          ),
        );
      },
    );
  }

  ///課程列表元件
  Widget _classListWidget(ProductModel model, int index) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final isSelected = shoppingCarVm.shoppingCarListModel.any(
      (e) => e.productId == model.productId,
    );

    return InkWell(
      onTap: model.totalAmount <= 0
          ? () {}
          : () {
              if (!isSelected) {
                shoppingCarVm.addToShoppingCarByProductId(
                  productId: model.productId,
                );
                setState(() {
                  _selectedIndex = index;
                });

              } else {
                shoppingCarVm.removedShoppingCarByProductId(
                  productId: model.productId,
                );
                setState(() {
                  _selectedIndex = -1;
                });
              }
            },
      child: Container(
        width: double.infinity.w,
        height: 188.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(3, 6),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            /// 商品名稱
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.title,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    model.subTitle ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ],
              ),
            ),

            /// 價格文字
            Positioned(
              left: 19.w,
              bottom: 17.h,
              child: Text(
                '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
                style: TextStyle(
                  fontSize: 25.sp,
                  color: MyColors.grey_119_119_119,
                ),
              ),
            ),

            // /// 右下角半圓角區塊
            // Align(
            //   alignment: Alignment.bottomRight,
            //   child: Container(
            //     width: 76.w,
            //     height: 54.h,
            //     decoration: BoxDecoration(
            //       color: const Color(0xFFF5F5F5),
            //       borderRadius: BorderRadius.only(
            //         topLeft: Radius.circular(20.r),
            //         bottomRight: Radius.circular(20.r),
            //       ),
            //     ),
            //     child: model.totalAmount <= 0
            //         ? Center(
            //       child: Text(
            //         '售完',
            //         style: TextStyle(
            //             fontSize: 25.sp, color: MyColors.red_230_75_75),
            //       ),
            //     )
            //         : Center(
            //       child: Text(
            //         model.totalAmount.toString(),
            //         style: TextStyle(
            //             fontSize: 20.sp, color: MyColors.grey_119_119_119),
            //       ),
            //     ),
            //   ),
            // ),

            ///右上勾勾
            if (isSelected)
              Positioned(
                right: 11.w,
                top: 11.h,
                child: Circle(
                  color: MyColors.yellow_251_227_140,
                  size: 40.w,
                  child: Icon(Icons.check, color: MyColors.green_129_128_94),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _onSearchResultEmpty() {
    _selectedIndex = -1; // 清除選擇

  }

}
