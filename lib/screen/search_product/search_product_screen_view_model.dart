
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';

import '../../model/product/product_model.dart';
import '../../provider/shopping_car_provider.dart';



class SearchProductScreenViewModel with ChangeNotifier{
  TextEditingController searchController = TextEditingController();

  //-----|radioBtn｜-----
  String? type;

  bool isOnSearch = false;

  void setSearching(bool value) {
    isOnSearch = value;
    notifyListeners();
  }






}