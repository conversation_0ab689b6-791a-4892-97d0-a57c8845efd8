import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/dialog/MyDialog.dart';

import '../../../model/member/member_cash_model.dart';
import '../../../provider/shopping_car_provider.dart';
import '../../../resource/MyColor.dart';
import '../../../util/StringUtils.dart';
import '../../../widget/btn/return_and_submit_btn.dart';
import '../../../widget/dialog/sbar_style_dialog.dart';
import '../../../widget/hint/empty_hint.dart';
import '../../../widget/number_keyboard/number_keyboard.dart';
import '../../../widget/payment/manual_discount_row.dart';
import '../../../widget/shape/circle.dart';
import '../../start/my_app.dart';
import 'member_cash_screen_view_model.dart';

class MemberCashScreen extends StatefulWidget {
  final VoidCallback backOnTap; //返回
  final VoidCallback submitOnTap; //送出

  const MemberCashScreen({
    super.key,
    required this.backOnTap,
    required this.submitOnTap,
  });

  @override
  State<MemberCashScreen> createState() => _MemberCashScreenState();
}

class _MemberCashScreenState extends State<MemberCashScreen> {
  final vm = MemberCashScreenViewModel();
  double overage = 0;

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Consumer<ShoppingCarProvider>(
          builder: (context, shoppingCarVm, _) {
            return Padding(
              padding: EdgeInsets.only(top: 35.h),
              child: Column(
                children: [
                  SizedBox(height: 35.h),
                  Text(
                    '會員儲值金',
                    style: TextStyle(
                      fontSize: 44.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(height: 89.h),

                  Expanded(
                    child: Row(
                      children: [
                        ///儲值金錢包列表
                        SizedBox(

                          width: (480 + 42).w,
                          child: _cashList(),
                        ),

                        ///明細與計算器區塊
                        Padding(
                          padding: EdgeInsets.only(bottom: 50.h),
                          child: ContainerWithRadius(
                            w: 1178.w,
                            h: 741.h,
                            r: 20.r,
                            color: Colors.white,
                            isHaveBorder: true,
                            child: _priceDetailAndCalculatorArea(),
                          ),
                        ),
                      ],
                    ),
                  ),

                  ///返回與送出按鈕
                  ReturnAndSubmitBtn(
                    backOnTap: widget.backOnTap,
                    submitOnTap: () {
                      //輸入金額不得大於餘額
                      if (shoppingCarVm.inputValue > overage) {
                        sbarStyleDialog(
                          context: context,
                          yesAction: () {
                            navigatorKey.currentState?.pop();
                          },
                          title: '提醒您！',
                          content: '輸入金額不得大於餘額',
                        );

                        return;
                      }

                      widget.submitOnTap();
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  ///明細與計算器區塊
  Widget _priceDetailAndCalculatorArea() {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return Padding(
      padding: EdgeInsets.only(
        left: 50.w,
        right: 30.w,
        top: 55.h,
        // bott om: 67.h,
      ),
      child: Row(
        children: [
          SizedBox(width: 598.w, child: _priceDetail()),

          SizedBox(width: 64.5.w),
          Expanded(
            child: NumberKeyboard(
              onValueChanged: (String value) {
                shoppingCarVm.inputValue = int.tryParse(value) ?? 0;
                setState(() {});
              },
            ),
          ),
        ],
      ),
    );
  }

  ///儲值金錢包列表
  Widget _cashList() {
    return Selector<ShoppingCarProvider, List<MemberCashModel>>(
      selector: (context, vm) => vm.memberCashModel,
      builder: (context, memberCashModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();

        return SmartRefresher(
          controller: shoppingCarVm.memberWalletCashRefreshController,
          enablePullDown: false,  //TODO:fix 這邊不知道為啥下拉後列表就無法滑動
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            shoppingCarVm.memberWalletCashRefreshController.refreshCompleted();
          },
          child:
          memberCashModel.isEmpty
              ? ListView(
                  // 用 ListView 包住 EmptyHint，這樣才能下拉
                  children: [
                    SizedBox(
                      height:
                          MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
                    ),
                    Center(child: EmptyHint(hint: '尚無儲值金')),
                  ],
                )
              :
          ListView.separated(
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.only(
                    left: 44.w,
                    right: 42.w,
                    bottom: 50.h,
                  ),
                  itemCount: memberCashModel.length,
                  itemBuilder: (context, index) {
                    final model = memberCashModel[index];
                    return _cashListWidget(index, model, shoppingCarVm);
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 19.h),
                ),
        );
      },
    );
  }




  ///儲值金錢包列表元件
  Widget _cashListWidget(
    int index,
    MemberCashModel model,
    ShoppingCarProvider shoppingCarVm,
  ) {
    return InkWell(
      onTap: () {
        shoppingCarVm.toggleSelectCash(index);
      },
      child: Container(
        width: 480.w,
        height: 188.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(color: MyColors.grey_235_235_235),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(3, 6),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            /// 錢包名稱
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 20.h),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.cashName,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(height: 8.h),

                  ///錢包說明
                  Text(
                    model.describe,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ],
              ),
            ),

            Positioned(
              left: 19.w,
              right: 19.w,
              bottom: 17.h,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  /// 價格文字
                  Text(
                    '\$ ${StringUtils.formatMoneyForDouble(model.cashOriginalPrice)}',
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.grey_119_119_119,
                    ),
                  ),

                  /// 餘額
                  Text(
                    '餘額：\$ ${StringUtils.formatMoneyForDouble(model.overage)}',
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.orange_240_149_68,
                    ),
                  ),
                ],
              ),
            ),

            ///右上選擇勾勾
            if (model.isSelect)
              Positioned(
                right: 11.w,
                top: 11.h,
                child: Circle(
                  color: MyColors.yellow_251_227_140,
                  size: 40.w,
                  child: Icon(Icons.check, color: MyColors.green_129_128_94),
                ),
              ),
          ],
        ),
      ),
    );
  }

  ///明細
  Widget _priceDetail() {
    return Selector<ShoppingCarProvider, List<MemberCashModel>>(
      selector: (context, vm) =>
          vm.memberCashModel.where((item) => item.isSelect).toList(),
      builder: (context, selectedCashList, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        final courseOriginalAmount = shoppingCarVm.getAmountByType(2).toInt();
        final unpaidAmount = courseOriginalAmount - shoppingCarVm.inputValue;

        if (selectedCashList.isEmpty) {
          return Center(
            child: Text(
              '尚未選擇儲值金錢包',
              style: TextStyle(
                color: MyColors.grey_145_145_145,
                fontSize: 28.sp,
              ),
            ),
          );
        }

        final resData = selectedCashList.first;
        overage = resData.overage;

        return Column(
          children: [
            //錢包名稱
            Text(
              resData.cashName,
              style: TextStyle(fontSize: 36.sp, color: MyColors.brown_57_54_18),
            ),
            SizedBox(height: 10.h),

            //錢包說明
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Text(
                resData.describe,
                style: TextStyle(
                  overflow: TextOverflow.ellipsis,
                  fontSize: 25.sp,
                  color: MyColors.green_129_128_94,
                ),
              ),
            ),

            SizedBox(height: 40.h),
            _baseDivider(),
            SizedBox(height: 40.h),

            //課程金額小計(原價)
            _baseWalletPriceRow('課程金額小計(原價)：', courseOriginalAmount),
            SizedBox(height: 41.h),

            //課程金額小計(折扣後)
            //TODO:獲取折扣後金額
            _baseWalletPriceRow('課程金額小計(折扣後)：', courseOriginalAmount),
            SizedBox(height: 41.h),

            //錢包支付金額
            _baseWalletPriceRow('錢包支付金額：', shoppingCarVm.inputValue, true),
            SizedBox(height: 10.h),

            //餘額
            Align(
              alignment: Alignment.centerRight,
              child: Padding(
                padding: EdgeInsets.only(right: 40.w),
                child: Text(
                  '餘額：\$ ${StringUtils.formatMoneyForDouble(resData.overage)}',
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: MyColors.orange_240_149_68,
                  ),
                ),
              ),
            ),
            SizedBox(height: 41.h),

            //課程未付款金額
            _baseWalletPriceRow('課程未付款金額：', unpaidAmount, false, true),
          ],
        );
      },
    );
  }

  ///
  Widget _baseDivider() {
    return Divider(thickness: 1, height: 1, color: MyColors.grey_156_157_152);
  }

  ///
  Widget _baseWalletPriceRow(
    String title,
    int price, [
    bool? isMinus,
    bool? isUnpaid,
  ]) {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    final priceStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
    );
    final unpaidStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.red_230_75_75,
    );

    /// Case 1: isMinus (錢包折抵金額)
    if (isMinus ?? false) {
      return ManualDiscountRow(
        controller: TextEditingController(text: price.toString()),
        padding: EdgeInsets.only(right: 40.w),
        isExpanded: true,
        isHaveDot: false,
        title: title,
        price: price.toDouble(),
        isMinus: true,
      );
    }

    /// Case 2: 其他情況
    return Padding(
      padding: EdgeInsets.only(right: 40.w),
      child: Row(
        children: [
          Text(title, style: titleStyle),
          Spacer(),

          // Case: 一般金額顯示
          if (!(isUnpaid ?? false))
            Text(
              '\$ ${StringUtils.formatMoneyForDouble(price.toDouble())}',
              style: priceStyle,
            ),

          // Case: 課程未支付金額
          if (isUnpaid ?? false)
            Text(
              StringUtils.formatMoneyForDouble(price.toDouble()),
              style: unpaidStyle,
            ),
        ],
      ),
    );
  }
}
