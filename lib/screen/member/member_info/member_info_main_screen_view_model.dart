import 'package:flutter/material.dart';

import '../../../model/member/member_reserve_model.dart';

class MemberInfoMainScreenViewModel with ChangeNotifier {
  MemberReserveModel? selectedModel;

  void updateSelectedModel(MemberReserveModel? model) {
    selectedModel = model;
    notifyListeners();
  }


  bool isOpenSlider = false;
  late AnimationController infoAniController;


  void toggleSlider({MemberReserveModel? model}) {
    if (isOpenSlider) {
      closeSlider();
    } else {
      openSlider(model);
    }
  }

  void openSlider(MemberReserveModel? model) {
    selectedModel = model;
    isOpenSlider = true;
    infoAniController.forward();
    notifyListeners();
  }

  void closeSlider() {
    infoAniController.reverse().then((_) {
      selectedModel = null;
      isOpenSlider = false;
      notifyListeners();
    });
  }

  void initController(TickerProvider vsync) {
    infoAniController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: vsync,
    );
  }

  void disposeController() {
    infoAniController.dispose();
  }
}
