import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../model/top_up_record/top_up_record_model.dart';
import '../../../../model/widget_model/drop_down_model.dart';

class TopUpRecordScreenViewModel with ChangeNotifier {
  RefreshController refreshController = RefreshController();
  TextEditingController orderIdController = TextEditingController(); //訂單編號
  TextEditingController walletNameController = TextEditingController(); //錢包名稱

  //-----|日期|-----
  DateTime rangeStartDate = DateTime.now();
  DateTime rangeEndDate = DateTime.now();

  String startStr = '';
  String endStr = '';
  String year = '';

  //下拉
  DropDownModel? typeDropDownModel; //類別下拉model

  //-----|dropDown|-----
  //類別下拉資料
  List<DropDownModel> typedDropDownList = [
    DropDownModel(id: 0, code: '', name: '全部'),
    DropDownModel(id: 1, code: '', name: 'A'),
    DropDownModel(id: 2, code: '', name: 'B'),
    DropDownModel(id: 3, code: '', name: 'C'),
  ];

  ///儲值金列表
  List<TopUpRecordModel> topUpRecordModel = [
    TopUpRecordModel(
      orderId: 'A123456789',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包A',
      memberName: '陳國華',
      operatorName: '小華',
      isPriceMinus: false,
    ),
    TopUpRecordModel(
      orderId: 'A123456789',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包A',
      memberName: '張德陽',
      operatorName: '小羊',
      isPriceMinus: false,
    ),
    TopUpRecordModel(
      orderId: 'A123456789',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包A',
      memberName: '蔡岳霖',
      operatorName: '小林',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: 'A123456789',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包A',
      memberName: '金小虎',
      operatorName: '小虎',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: '-',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包B',
      memberName: '陳曉華',
      operatorName: '小華',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: '-',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包B',
      memberName: '陳曉華',
      operatorName: '小華',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: '-',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包B',
      memberName: '陳曉華',
      operatorName: '小華',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: '-',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包B',
      memberName: '陳曉華',
      operatorName: '小華',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: '-',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包B',
      memberName: '陳曉華',
      operatorName: '小華',
      isPriceMinus: true,
    ),
    TopUpRecordModel(
      orderId: '-',
      cashId: 'M123456878',
      checkOutTime: '2025-07-02 15:20',
      somPrice: '35000',
      price: '30000',
      useFor: '購買儲值金',
      walletName: '儲值金錢包B',
      memberName: '陳曉華',
      operatorName: '小華',
      isPriceMinus: true,
    ),
  ];
}
