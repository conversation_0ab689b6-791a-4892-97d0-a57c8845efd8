import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../../resource/MyColor.dart';
import '../../../../../util/StringUtils.dart';
import '../../../../../widget/btn/return_and_submit_btn.dart';
import '../../../../../widget/container_with_radius.dart';
import '../../../../../widget/dialog/MyDialog.dart';
import '../../../../../widget/dialog/sbar_style_dialog.dart';
import '../../../../../widget/number_keyboard/number_keyboard.dart';
import '../../../../../widget/payment/manual_discount_row.dart';
import '../../../../start/my_app.dart';

class TopUpRefundScreen extends StatefulWidget {
  final String walletName;
  final String walletInfo;
  final double overage;
  const TopUpRefundScreen({super.key, required this.walletName, required this.walletInfo, required this.overage});

  @override
  State<TopUpRefundScreen> createState() => _TopUpRefundScreenState();
}

class _TopUpRefundScreenState extends State<TopUpRefundScreen> {
  double _inputValue = 0 ;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 35.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 35.h),
          Text(
            '儲值金退款',
            style: TextStyle(
              fontSize: 44.sp,
              color: MyColors.brown_57_54_18,
            ),
          ),


          SizedBox(height: 157.h),

          ///明細與計算器區塊
          Expanded(
            child: _priceDetailAndCalculatorArea(),
          ),

          ///返回與送出按鈕
          ReturnAndSubmitBtn(
              backOnTap: (){
                Navigator.of(context).pop();
              },
              submitOnTap: (){

                //輸入金額不得大於餘額
                if(_inputValue > widget.overage){
                  sbarStyleDialog(
                      context: context,
                      yesAction: () {
                        navigatorKey.currentState?.pop();
                      },
                      title: '提醒您！',
                      content: '輸入金額不得大於餘額');
                  return;
                }


                Navigator.of(context).pop();
              }
          ),
        ],
      ),
    );
  }

  ///明細與計算器區塊
  Widget _priceDetailAndCalculatorArea() {

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
            height: 600.h,
            width: 598.w,
            child: _priceDetail()),

        SizedBox(width: 64.5.w),
        SizedBox(
          height: 600.h,
            width: 450.w,
            child: NumberKeyboard(onValueChanged: (String value){
          _inputValue = double.tryParse(value) ?? 0;
          setState(() {

          });

        },)),
      ],
    );
  }



  ///明細
  Widget _priceDetail() {
    return  Column(
      children: [
        //錢包名稱
        Text(
          widget.walletName,
          style: TextStyle(fontSize: 36.sp, color: MyColors.brown_57_54_18),
        ),
        SizedBox(height: 10.h),

        //錢包說明
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Text(
            widget.walletInfo,
            style: TextStyle(
              overflow: TextOverflow.ellipsis,
              fontSize: 25.sp,
              color: MyColors.green_129_128_94,
            ),
          ),
        ),

        SizedBox(height: 40.h),
        _baseDivider(),
        SizedBox(height: 40.h),


        //錢包退款金額
        _baseWalletPriceRow('錢包退款金額：', _inputValue, ),
        SizedBox(height: 10.h),


        //餘額
        Align(
          alignment: Alignment.centerRight,
          child: Padding(
            padding:  EdgeInsets.only(right: 40.w),
            child: Text(
              '餘額：\$ ${StringUtils.formatMoneyForDouble(widget.overage)}',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.orange_240_149_68,
              ),
            ),
          ),
        ),




      ],
    );
  }

  ///
  Widget _baseDivider() {
    return Divider(thickness: 1, height: 1, color: MyColors.grey_156_157_152);
  }

  Widget _baseWalletPriceRow(
      String title,
      double price, ) {
    return ManualDiscountRow(
      controller: TextEditingController(text: price.toString()),
      padding: EdgeInsets.only(right: 40.w),
      isExpanded: true,
      isHaveDot: false,
      title: title,
      price: price,
      isMinus: true,
    );


  }

}
