import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../model/member/member_class_model.dart';
import '../../../../resource/MyColor.dart';
import '../../../../util/MyRoutes.dart';
import '../../../../widget/base_title_list_bar/member_class_base_list_bar.dart';
import '../../../../widget/base_title_list_bar/member_class_base_title_bar.dart';
import '../../../../widget/btn/scan_btn.dart';
import '../../../../widget/container_with_radius.dart';
import '../../../../widget/hint/empty_hint.dart';
import '../../../../widget/scanner/scanner.dart';
import '../../../../widget/textField/base_text_field.dart';
import '../../../scan/scan_screen.dart';
import '../../../start/my_app.dart';
import 'member_class_screen_view_model.dart';

class MemberClassScreen extends StatefulWidget {
  const MemberClassScreen({super.key});

  @override
  State<MemberClassScreen> createState() => _MemberClassScreenState();
}

class _MemberClassScreenState extends State<MemberClassScreen> {
  final vm = MemberClassScreenViewModel();
  int _selectedIndex = 0;  //預設給全部

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,

      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(top: 142.h, left: 16.w, right: 33.w),
          child: Column(
            children: [
              ///搜尋列
              _searchBar(),
              SizedBox(height: 22.h),

              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ///左側課程類別列表
                    _classTypeList(),

                    SizedBox(width: 27.w),

                    ///會員課程區塊
                    _memberClassArea(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(left: 25.w, right: 18.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ///會員課程
            Text(
              '會員課程',
              style: TextStyle(
                fontSize: 32.sp,
                color: MyColors.green_121_131_90,
              ),
            ),

            Spacer(),

            ///課程名稱輸入框
            BaseTextField(
              w: 710.w,
              hint: '請輸入課程名稱',
              controller: vm.classNameController,
            ),
            SizedBox(width: 31.w),

            ///掃碼按鈕
            ScanBtn(onTap: () {
              runBarCodeScannerCamForOrder();
            }),

            SizedBox(width: 32.w),

            ///搜尋按鈕
            ContainerWithRadius(
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///左側課程類別列表
  Widget _classTypeList() {
    return SizedBox(
      width: 196.w,
      child: Selector<MemberClassScreenViewModel, List<MemberClassModel>>(
        selector: (context, vm) => vm.memberClassModel,
        builder: (context, memberClassModel, _) {
          //這邊提取tape，並去重
          final List<String> uniqueTypes = memberClassModel
              .map((e) => e.type)
              .toSet()
              .toList();

          return ListView.separated(
            padding: EdgeInsets.zero,
            physics: BouncingScrollPhysics(),
            itemCount: uniqueTypes.length,
            itemBuilder: (context, index) {
              final typeName = uniqueTypes[index];
              return _classTypeListWidget(typeName, index);
            },
            separatorBuilder: (context, index) => SizedBox(height: 19.h),
          );
        },
      ),
    );
  }

  ///課程類別列表元件
  Widget _classTypeListWidget(String typeName, int index) {
    final bool isSelected = index == _selectedIndex;
    return ContainerWithRadius(
      isHaveBorder: true,
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
      },
      w: 196.w,
      h: 62.h,
      r: 20.r,
      color: isSelected ? MyColors.green_157_193_65 : Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          typeName,
          style: TextStyle(
            fontSize: 25.sp,
            color: isSelected ? Colors.white : MyColors.green_129_128_94,
          ),
        ),
      ),
    );
  }

  ///會員課程區塊
  Widget _memberClassArea() {
    return SizedBox(
      width: 1648.w,
      child: Column(
        children: [
          //會員課程標頭
          MemberClassBaseTitleBar(),
          Expanded(child: _memberClassList()),
        ],
      ),
    );
  }

  ///會員課程
  //TODO:下拉刷新
  Widget _memberClassList() {
    return Selector<MemberClassScreenViewModel, List<MemberClassModel>>(
      selector: (context, vm) => vm.memberClassModel,
      builder: (context, memberClassModel, _) {
        // 篩選 (暫定：0 全部，1 臉部，2 身體，3 手足，4 頭部舒壓，5 孕媽咪，6 男士保養，7 眉睫)
        final filteredList = _selectedIndex == 0
            ? memberClassModel
            : memberClassModel
                  .where((e) => e.typeId == _selectedIndex)
                  .toList();

        if (filteredList.isEmpty) {
          return EmptyHint(hint: '尚無會員課程');
        }

        return ListView.builder(
          padding: EdgeInsets.only(bottom: 50.h),
          physics: BouncingScrollPhysics(),
          itemCount: filteredList.length,
          itemBuilder: (context, index) {
            final model = filteredList[index];
            return MemberClassBaseListBar(
              orderId: model.orderId,
              checkOutTime: model.checkOutTime,
              checkOutDate: model.checkOutDate,
              type: model.type,
              classInnerInfo: model.classInnerInfo,
              memberClassModel: vm.memberClassModel,
            );
          },
        );
      },
    );
  }

}
