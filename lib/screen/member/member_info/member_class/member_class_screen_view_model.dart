import 'package:flutter/cupertino.dart';

import '../../../../model/member/member_class_model.dart';

class MemberClassScreenViewModel with ChangeNotifier {
  TextEditingController classNameController = TextEditingController(); //課程名稱

  ///會員課程列表
  ///(暫定：0 全部，1 臉部，2 身體，3 手足，4 頭部舒壓，5 孕媽咪，6 男士保養，7 眉睫)
  List<MemberClassModel> memberClassModel = [
    MemberClassModel(
      type: '全部',
      typeId: 0,
      orderId: 'A123456879',
      checkOutDate: '2025-02-01',
      checkOutTime: '15:02',
      classInnerInfo: [
        ClassInnerInfo(
          className: '課程名稱A',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
          paidClass: 8,
          unPaidClass: 5
        ),
        ClassInnerInfo(
          className: '課程名稱B',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '臉部',
      typeId: 1,
      orderId: 'B123456879',
      checkOutDate: '2025-02-01',
      checkOutTime: "15:02",
      classInnerInfo: [
        ClassInnerInfo(
          className: '臉部清潔A',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 100,
          remainingClass: 87,
            paidClass: 8,
            unPaidClass: 5
        ),
        ClassInnerInfo(
          className: '臉部清潔B',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '身體',
      typeId: 2,
      orderId: 'C123456879',
      checkOutTime: '15:20',
      checkOutDate: '2025-02-01',
      classInnerInfo: [
        ClassInnerInfo(
          className: '身體舒壓A',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
        ClassInnerInfo(
          className: '身體舒壓B',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '手足',
      typeId: 3,
      orderId: 'D123456879',
      checkOutTime: '15:20',
      checkOutDate: '2025-02-01',
      classInnerInfo: [
        ClassInnerInfo(
          className: '手足保養',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '頭部舒壓',
      typeId: 4,
      orderId: 'E123456879',
      checkOutTime: '15:20',
      checkOutDate: '2025-02-01',
      classInnerInfo: [
        ClassInnerInfo(
          className: '頭部舒壓',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '孕媽咪',
      typeId: 5,
      orderId: 'F123456879',
      checkOutTime: '15:20',
      checkOutDate: '2025-02-01',
      classInnerInfo: [
        ClassInnerInfo(
          className: '孕媽咪護理',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '男士保養',
      typeId: 6,
      orderId: 'G123456879',
      checkOutTime: '15:20',
      checkOutDate: '2025-02-01',
      classInnerInfo: [
        ClassInnerInfo(
          className: '男士保養',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
    MemberClassModel(
      type: '眉睫',
      typeId: 7,
      orderId: 'H123456879',
      checkOutTime: '15:20',
      checkOutDate: '2025-02-01',
      classInnerInfo: [
        ClassInnerInfo(
          className: '眉型整修與護理',
          classNameInfo: '課程名稱說明，' * 10,
          purchasedClass: 10,
          remainingClass: 7,
            paidClass: 8,
            unPaidClass: 5
        ),
      ],
    ),
  ];
}
