import 'package:flutter/cupertino.dart';

class MemberInfoScreenViewModel with ChangeNotifier{
  TextEditingController nameController = TextEditingController();  //姓名Controller
  TextEditingController phoneController = TextEditingController();  //手機Controller
  TextEditingController mailController = TextEditingController();  //mail Controller
  TextEditingController birthdayController = TextEditingController();  //生日Controller

  //-----|日期|-----
  DateTime selectBirthdayDate = DateTime.now();
}