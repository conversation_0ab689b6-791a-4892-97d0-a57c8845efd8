import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/member/member_info_bar.dart';

import '../../../../gen/r.dart';
import '../../../../resource/MyColor.dart';
import '../../../../widget/btn/return_and_submit_btn.dart';
import '../../../../widget/cached_network_image/error_cached_network_image.dart';
import '../../../../widget/calender/build_single_date_calender.dart';
import '../../../../widget/divider/custom_vertical_divider.dart';
import '../../../../widget/textField/my_text_field.dart';
import '../../../start/my_app.dart';
import 'member_info_screen_view_model.dart';

class MemberInfoScreen extends StatefulWidget {
  const MemberInfoScreen({super.key});

  @override
  State<MemberInfoScreen> createState() => _MemberInfoScreenState();
}

class _MemberInfoScreenState extends State<MemberInfoScreen> {
  final vm = MemberInfoScreenViewModel();

  @override
  void initState() {
    ///初始化會員資料
    _initInfo();
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  ///初始化會員資料
  void _initInfo() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final shoppingCarVm = context.read<ShoppingCarProvider>();
      vm.nameController.text = shoppingCarVm.memberName ?? '';
      vm.phoneController.text = shoppingCarVm.memberPhone ?? '-';
      vm.birthdayController.text = shoppingCarVm.memberBirthday ?? '';
      vm.mailController.text = shoppingCarVm.memberEMail ?? '';
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(top: 142.h, left: 16.w, right: 33.w),
          child: SingleChildScrollView(
            padding: EdgeInsets.zero,
            physics: BouncingScrollPhysics(),
            child: Column(
              children: [
                MemberInfoBar(title: '基本資料'),
                SizedBox(height: 79.h),
                _memberArea(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///會員區塊
  Widget _memberArea() {
    return ContainerWithRadius(
      w: 969.w,
      h: 767.h,
      r: 20.r,
      boxShadow: [
        BoxShadow(
          color: MyColors.green_214_226_179,
          offset: const Offset(8, 8),
          blurRadius: 80,
        ),
      ],
      color: Colors.white,
      isHaveBorder: true,
      child: Column(

        children: [
          ///基本資料區塊
          _infoArea(),

          Spacer(),

          ///返回與儲存按鈕
          ReturnAndSubmitBtn(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(20.r),
              bottomRight: Radius.circular(20.r),
            ),
            cancelText: '取消',
            color: Colors.white,
            isHaveBorder: true,
            h: 151.h,
            submitText: '儲存',
            backOnTap: () {
              Navigator.of(context).pop();
            },
            submitOnTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _infoArea() {
    return Padding(
      padding:  EdgeInsets.only(top: 108.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _baseInfoRow('姓名', '請輸入姓名', vm.nameController),
          SizedBox(height: 55.h),

          _baseInfoRow(
            '電話',
            '請輸入電話',
            vm.phoneController,
            isPureText: true,
            info: vm.phoneController.text,
          ),
          SizedBox(height: 55.h),

          _baseInfoRow('Email', '請輸入E-mail', vm.mailController),
          SizedBox(height: 55.h),

          _baseInfoRow(
            isReadOnly: true,
            '生日',
            '請選擇生日',
            vm.birthdayController,
            leadWidget: Image(
              image: R.image.icon_calendar(),
              width: 36.w,
              height: 40.h,
            ),
            onTap: () {
              buildSingleDateCalender(
                context: context,
                onDaySelected: (selectDate, date, events) {
                  vm.selectBirthdayDate = selectDate;
                },
                onSubmit: () {
                  if (vm.birthdayController.text.isEmpty) {
                    vm.birthdayController.text = DateTime.now()
                        .toDateYMDString()
                        .replaceAll('-', '/');
                  }

                  vm.birthdayController.text = vm.selectBirthdayDate
                      .toDateYMDString()
                      .replaceAll('-', '/');
                  navigatorKey.currentState?.pop();
                },
                initDate: vm.selectBirthdayDate,
              );
            },
            enterTextPadding: EdgeInsets.only(left: 12.w),
          ),
          SizedBox(height: 55.h),
        ],
      ),
    );
  }

  // base info row
  Widget _baseInfoRow(
    String title,
    String hint,
    TextEditingController controller, {
    Widget? leadWidget,
    VoidCallback? onTap,
    EdgeInsets? enterTextPadding,
    bool? isPureText, //唯讀不給輸入框
    String? info, // //唯讀才會有
    bool? isReadOnly,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: 100.w,
          child: Text(
            title,
            style: TextStyle(fontSize: 36.sp, color: MyColors.green_129_128_94),
          ),
        ),

        SizedBox(width: 50.w),

        if (isPureText ?? false)
          SizedBox(
            width: 300.w,
            child: Text(
              info ?? '-',
              style: TextStyle(fontSize: 28.sp, color: MyColors.brown_57_54_18),
            ),
          ),

        if (!(isPureText ?? false))
          SizedBox(
            width: 350.w,
            child: Expanded(
              child: MyTextField(
                isSingleLine: true,
                readOnly: isReadOnly,
                leadWidget: leadWidget,
                leadWidgetPadding: EdgeInsets.only(left: 15.w),
                onTap: onTap,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
                borderColor: MyColors.grey_235_235_235,
                hint: hint,
                textAlign: TextAlign.start,
                enterTextPadding: enterTextPadding,
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
                controller: controller,
                border: 20.r,
                height: 74.h,
              ),
            ),
          ),
      ],
    );
  }
}
