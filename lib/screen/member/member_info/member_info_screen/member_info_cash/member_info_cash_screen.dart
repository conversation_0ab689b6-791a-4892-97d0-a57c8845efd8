import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/hint/empty_hint.dart';

import '../../../../../model/member/member_cash_model.dart';
import '../../../../../util/StringUtils.dart';
import '../../../../../widget/dialog/MyDialog.dart';
import '../../../../../widget/member/member_info_bar.dart';
import '../../top_up_record/top_up_refund/top_up_refund_screen.dart';
import 'member_info_cash_screen_view_model.dart';

class MemberInfoCashScreen extends StatefulWidget {
  const MemberInfoCashScreen({super.key});

  @override
  State<MemberInfoCashScreen> createState() => _MemberInfoCashScreenState();
}

class _MemberInfoCashScreenState extends State<MemberInfoCashScreen> {
  final vm = MemberInfoCashScreenViewModel();

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(top: 142.h, left: 16.w, right: 33.w),
          child: Column(
            children: [
              MemberInfoBar(title: '會員儲值金'),

              Expanded(child: _cashList()),
            ],
          ),
        ),
      ),
    );
  }

  ///儲值金列表
  Widget _cashList() {
    return Selector<ShoppingCarProvider, List<MemberCashModel>>(
      selector: (context, vm) => vm.memberCashModel,
      builder: (context, memberCashModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();

        return SmartRefresher(
          controller: shoppingCarVm.memberCashRefreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            shoppingCarVm.memberCashRefreshController.refreshCompleted();
          },
          child: memberCashModel.isEmpty
              ? ListView(
                  // 用 ListView 包住 EmptyHint，這樣才能下拉
                  children: [
                    SizedBox(
                      height:
                          MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
                    ),
                    Center(child: EmptyHint(hint: '尚無尚無會員儲值金')),
                  ],
                )
              : ListView.separated(
                  physics: BouncingScrollPhysics(),
                  padding: EdgeInsets.only(
                    left: (210 - 16).w,
                    right: (210 - 33).w,
                    bottom: 200.h,
                    top: 82.h,
                  ),
                  itemCount: memberCashModel.length,
                  itemBuilder: (context, index) {
                    final model = memberCashModel[index];
                    return _cashListWidget(model);
                  },
                  separatorBuilder: (context, index) => SizedBox(height: 19.h),
                ),
        );
      },
    );
  }

  ///儲值金列表元件
  Widget _cashListWidget(MemberCashModel model) {
    return ContainerWithRadius(
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      w: double.infinity.w,
      h: 188.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(
          top: 15.h,
          left: 19.w,
          right: 34.w,
          bottom: 17.h,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //名稱
                Text(
                  model.cashName,
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: MyColors.brown_57_54_18,
                  ),
                ),
                SizedBox(height: 7.h),

                //描述
                Padding(
                  padding: EdgeInsets.only(right: 350.w),
                  child: Text(
                    model.describe,
                    style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      fontSize: 25.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ),

                Spacer(),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    //原價
                    Text(
                      '\$ ${StringUtils.formatMoneyForDouble(model.cashOriginalPrice)}',
                      style: TextStyle(
                        fontSize: 25.sp,
                        color: MyColors.grey_119_119_119,
                      ),
                    ),

                    //餘額
                    Text(
                      '餘額：\$ ${StringUtils.formatMoneyForDouble(model.overage)}',
                      style: TextStyle(
                        fontSize: 25.sp,
                        color: MyColors.orange_240_149_68,
                      ),
                    ),
                  ],
                ),
              ],
            ),

            //退款按鈕
            Positioned(
              top: 30.h,
              right: 0.w,
              child: _refundBtn(
                walletName: model.cashName,
                walletInfo: model.describe,
                overage: model.overage,
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///退款按鈕
  Widget _refundBtn({
    required String walletName,
    required String walletInfo,
    required double overage,
  }) {
    return ContainerWithRadius(
      onTap: () {
        _topUpRefundDialog(
          walletName: walletName,
          walletInfo: walletInfo,
          overage: overage,
        );
      },
      w: 196.w,
      h: 63.h,
      r: 20.r,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      color: Colors.white,
      isHaveBorder: true,
      child: Center(
        child: Text(
          '退款',
          style: TextStyle(fontSize: 25.sp, color: MyColors.green_129_128_94),
        ),
      ),
    );
  }

  //
  ///開啟退款彈窗
  void _topUpRefundDialog({
    required String walletName,
    required String walletInfo,
    required double overage,
  }) {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: true,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: TopUpRefundScreen(
                walletName: walletName,
                walletInfo: walletInfo,
                overage: overage,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
