import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../../model/member/member_history_order_model.dart';
import '../../../../model/widget_model/drop_down_model.dart';

class MemberHistoryOrderScreenViewModel with ChangeNotifier {
  RefreshController refreshController = RefreshController();
  TextEditingController orderIdController = TextEditingController(); //訂單編號
  TextEditingController productNameController = TextEditingController(); //商品名稱

  //-----|日期|-----
  DateTime rangeStartDate = DateTime.now();
  DateTime rangeEndDate = DateTime.now();

  String startStr = '';
  String endStr = '';
  String year = '';

  //下拉
  DropDownModel? typeDropDownModel; //類別下拉model

  //-----|dropDown|-----
  //類別下拉資料
  List<DropDownModel> typedDropDownList = [
    DropDownModel(id: 0, code: '', name: '全部'),
    DropDownModel(id: 1, code: '', name: 'A'),
    DropDownModel(id: 2, code: '', name: 'B'),
    DropDownModel(id: 3, code: '', name: 'C'),
  ];

  //歷史訂單資料
  List<MemberHistoryOrderModel> memberHistoryOrderModel = [
    MemberHistoryOrderModel(
        orderId: "A123456789",
        invoiceId: "FE12345687",
        checkOutTime: '2025-02-21 15:20',
        somPrice: '35000',
        price: '35000',
        priceInfo: '現金',
        orderStatue: '完成付款',
        orderStatueCode: 1,  //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
        unPaidPrice: '0',
        memberName: '王小美',
        operatorName: '小美',
        totalSalesOrderAmount: '5000'
    ),
    MemberHistoryOrderModel(
        orderId: "A123456789",
        invoiceId: "FE12345687",
        checkOutTime: '2025-02-21 15:20',
        somPrice: '35000',
        price: '35000',
        priceInfo: '現金',
        orderStatue: '已銷貨退回',
        orderStatueCode: 2,  //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
        unPaidPrice: '0',
        memberName: '孫總',
        operatorName: '阿孫',
        totalSalesOrderAmount: '5000'
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '28000',
      priceInfo: '現金',
      orderStatue: '未完成付款',
      orderStatueCode: 0,  //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '28000',
      memberName: '洪詩詩',
      operatorName: '詩詩',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '50000',
      price: '35000',
      priceInfo: '百貨優惠券,現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,  //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '0',
      memberName: '金小五',
      operatorName: '小五',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: 'LINE Pay',
      orderStatue: '未完成付款',
      orderStatueCode: 0,  //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '35000',
      memberName: '劉曉琪',
      operatorName: '小琪',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金,信用卡',
      orderStatue: '完成付款',
      orderStatueCode: 1,  //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '0',
      memberName: '陳小羊',
      operatorName: '小羊',
    ),
  ];
}
