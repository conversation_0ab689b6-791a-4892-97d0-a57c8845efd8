import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/extension/my_extension.dart';

import '../../../../gen/r.dart';
import '../../../../logger/my_print.dart';
import '../../../../model/member/member_reserve_model.dart';
import '../../../../model/widget_model/drop_down_model.dart';
import '../../../../resource/MyColor.dart';
import '../../../../widget/base_title_list_bar/member_reserve_order_base_list_bar.dart';
import '../../../../widget/container_with_radius.dart';
import '../../../../widget/drop_down/drop_down_button.dart';
import '../../../../widget/hint/empty_hint.dart';
import '../../../../widget/reserve/reserve_slider.dart';
import '../../../../widget/textField/base_text_field.dart';
import '../../../../widget/textField/sbar_style_range_date_Text_field.dart';
import '../member_info_main_screen_view_model.dart';
import 'member_reserve_screen_view_model.dart';

class MemberReserveScreen extends StatefulWidget {
  final MemberInfoMainScreenViewModel vm;

  const MemberReserveScreen({super.key, required this.vm});

  @override
  State<MemberReserveScreen> createState() => _MemberReserveScreenState();
}

class _MemberReserveScreenState extends State<MemberReserveScreen>
    with TickerProviderStateMixin {
  final vm = MemberReserveScreenViewModel();

  MemberReserveModel? selectedModel;

  @override
  void initState() {
    _initRangeDate();

    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  ///初始化日期區間
  void _initRangeDate() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      vm.startStr = startOfMonth.toDateYMDString();
      vm.endStr = endOfMonth.toDateYMDString();

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,

      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(top: 142.h, left: 16.w, right: 33.w),
              child: Column(
                children: [
                  ///搜尋列
                  _searchBar(),
                  SizedBox(height: 22.h),

                  ///預約訂單列表區塊
                  Expanded(child: _reserveOrderArea()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///預約訂單列表區塊
  Widget _reserveOrderArea() {
    return Column(
      children: [
        _reserveOrderTitleBar(),
        Expanded(child: _reserveOrderList()),
      ],
    );
  }

  ///預約訂單列表
  Widget _reserveOrderList() {
    return Selector<MemberReserveScreenViewModel, List<MemberReserveModel>>(
      selector: (context, vm) => vm.memberReserveModel,
      builder: (context, memberReserveModel, _) {
        return SmartRefresher(
          controller: vm.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            vm.refreshController.refreshCompleted();
          },
          child: memberReserveModel.isEmpty
              ? ListView(
                  // 用 ListView 包住 EmptyHint，這樣才能下拉
                  children: [
                    SizedBox(
                      height:
                          MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
                    ),
                    Center(child: EmptyHint(hint: '尚無預約訂單紀錄')),
                  ],
                )
              : ListView.builder(
                  padding: EdgeInsets.only(bottom: 50.h),
                  physics: BouncingScrollPhysics(),
                  itemCount: memberReserveModel.length,
                  itemBuilder: (context, index) {
                    final model = memberReserveModel[index];
                    return MemberReserveOrderBaseListBar(
                      isTitleBar: false,
                      orderId: model.orderId,
                      memberName: model.memberName,
                      backgroundColor: Colors.white,
                      onTap: () {
                        widget.vm.toggleSlider();
                        widget.vm.updateSelectedModel(model);
                      },
                      orderStatue: model.orderStatue,
                      reserveId: model.reserveId,
                      orderTime: model.orderTime,
                      orderClassTime: model.orderClassTime,
                      className: model.className,
                      memberPhone: model.memberPhone,
                      beautician: model.beautician,
                      customerSelectionType: model.customerSelectionType,
                      orderStatueInfo: model.orderStatueInfo,
                      classNameInfo: model.classNameInfo,
                      // isClassConfirm: reserve_calendar_model.isClassConfirm,
                    );
                  },
                ),
        );
      },
    );
  }

  ///預約訂單列表標頭
  Widget _reserveOrderTitleBar() {
    return MemberReserveOrderBaseListBar(
      // isClassConfirm: false,
      isTitleBar: true,
      orderId: '訂單編號',
      reserveId: '預約單編號',
      orderTime: '預約下單時間',
      orderClassTime: '預約課程時間',
      className: '課程名稱',
      memberName: '會員',
      beautician: '美容師',
      orderStatue: '訂單狀態',
      backgroundColor: MyColors.grey_247_247_247,
      onTap: () {},
      memberPhone: '',
      customerSelectionType: '',
      orderStatueInfo: '',
      classNameInfo: '',
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(right: 12.w, left: 25.w),
        child: Row(
          children: [
            ///預約訂單
            Text(
              '預約訂單',
              style: TextStyle(
                fontSize: 32.sp,
                color: MyColors.green_121_131_90,
              ),
            ),

            Spacer(),

            ///訂單狀態（下拉選單輸入框）
            _statueDropDown(),
            SizedBox(width: 22.w),

            ///訂單來源（下拉選單輸入框）
            _sourceDropDown(),
            SizedBox(width: 22.w),

            ///日期選擇
            _dateTextField(),
            SizedBox(width: 11.w),

            ///訂單編號輸入框
            BaseTextField(hint: '請輸入訂單編號', controller: vm.orderIdController),
            SizedBox(width: 11.w),

            ///課程名稱輸入框
            BaseTextField(hint: '請輸入課程名稱', controller: vm.classNameController),
            SizedBox(width: 11.w),

            ///搜尋按鈕
            ContainerWithRadius(
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///訂單狀態類別下拉
  Widget _statueDropDown() {
    return Selector<MemberReserveScreenViewModel, DropDownModel?>(
      selector: (context, vm) => vm.statueDropDownModel,
      shouldRebuild: (p, n) => true,
      builder: (context, statueDropDownModel, _) {
        return _baseDropDown(
          selectedItem: vm.statueDropDownModel,
          items: vm.statueDropDownList,
          onChanged: (DropDownModel? value) async {
            vm.statueDropDownModel = value;

            (context as Element).markNeedsBuild();
          },
        );
      },
    );
  }

  ///訂單來源類別下拉
  Widget _sourceDropDown() {
    return Selector<MemberReserveScreenViewModel, DropDownModel?>(
      selector: (context, vm) => vm.sourceDropDownModel,
      shouldRebuild: (p, n) => true,
      builder: (context, sourceDropDownModel, _) {
        return _baseDropDown(
          selectedItem: vm.sourceDropDownModel,
          items: vm.sourceDropDownList,
          onChanged: (DropDownModel? value) async {
            vm.sourceDropDownModel = value;

            (context as Element).markNeedsBuild();
          },
        );
      },
    );
  }

  ///日期選擇輸入框
  Widget _dateTextField() {
    return SbarStyleRangeDateTextField(
      isCanSelectAfterThisYear: true,
      rangeStartDate: vm.rangeStartDate,
      rangeEndDate: vm.rangeEndDate,
      startStr: vm.startStr,
      endStr: vm.endStr,
      onRangeSubmit: (start, end) {
        setState(() {
          vm.rangeStartDate = start;
          vm.rangeEndDate = end;
          vm.startStr = start.toDateYMDString();
          vm.endStr = end.toDateYMDString();
          myPrint('start: $start');
          myPrint('end: $end');
        });
      },
      onSpeedSelectMonthSubmit: (int year, int month) {
        vm.year = year.toString();

        // 該月的最後一天
        final lastDay = DateTime(year, month + 1, 0);

        // 格式化為 yyyy-MM-dd
        vm.startStr = "$year-${month.toString().padLeft(2, '0')}-01";
        vm.endStr =
            "$year-${month.toString().padLeft(2, '0')}-${lastDay.day.toString().padLeft(2, '0')}";

        myPrint("開始日期: ${vm.startStr}");
        myPrint("結束日期: ${vm.endStr}");

        setState(() {});
      },
    );
  }

  ///base drop down
  Widget _baseDropDown({
    required DropDownModel? selectedItem,
    required List<DropDownModel> items,
    required Function(DropDownModel? value)? onChanged,
  }) {
    return ContainerWithRadius(
      w: 218.w,
      h: 54.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 15.w, right: 8.w, top: 7.h, bottom: 7.h),
        child: MyDropdownButtonT<DropDownModel>(
          tailWidget: Image(
            image: R.image.icon_arrow_down_with_circle(),
            width: 40.w,
            height: 40.w,
          ),
          isExpanded: false,
          underline: SizedBox.shrink(),
          hint: Text(
            '請選擇類型',
            style: TextStyle(
              fontSize: 23.sp,
              color: MyColors.grey_60_60_67.withOpacity(0.3),
            ),
          ),
          textStyle: TextStyle(fontSize: 23.sp, color: MyColors.brown_57_54_18),
          selectedItem: selectedItem,
          labelSelector: (item) {
            return item.name;
          },
          items: items,
          onChanged: onChanged,
        ),
      ),
    );
  }
}
