import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/screen/member/member_info/top_up_record/top_up_record_screen.dart';

import '../../../gen/r.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/background/base_background.dart';
import '../../../widget/tab/base_tab_widget.dart';
import '../../../widget/tab/member_main_tab_widget.dart';
import 'member_class/member_class_screen.dart';
import 'member_history_order/member_history_order_screen.dart';
import 'member_info_main_screen_view_model.dart';
import 'member_info_screen/member_info_cash/member_info_cash_screen.dart';
import 'member_info_screen/member_info_screen.dart';
import 'member_reserve/member_reserve_screen.dart';

class MemberInfoMainScreen extends StatefulWidget {
  final int initIndex;
  const MemberInfoMainScreen({super.key, required this.initIndex});

  @override
  State<MemberInfoMainScreen> createState() => _MemberInfoMainScreenState();
}

class _MemberInfoMainScreenState extends State<MemberInfoMainScreen> {
  final vm = MemberInfoMainScreenViewModel();
  late final List<Widget Function(bool isSelected)> tabs;

  @override
  void initState() {

    tabs = [
          (isSelected) => BaseTabWidget(
        title: '基本資料',
        isSelected: isSelected,
        icon:
            Image(image: R.image.icon_member_info(),width: 30.w,height: 30.w,fit: BoxFit.cover, color: isSelected ? Colors.white : MyColors.green_129_128_94,)

      ),
          (isSelected) => BaseTabWidget(
        title: '歷史訂單',
        isSelected: isSelected,
        icon:  Image(image: R.image.icon_member_history_record(),width: 30.w,height: 30.w,fit: BoxFit.cover, color: isSelected ? Colors.white : MyColors.green_129_128_94,)
      ),
          (isSelected) => BaseTabWidget(
        title: '預約訂單',
        isSelected: isSelected,
        icon:  Image(image: R.image.icon_member_reserve(),width: 30.w,height: 30.w,fit: BoxFit.cover, color: isSelected ? Colors.white : MyColors.green_129_128_94,)
      ),
          (isSelected) => BaseTabWidget(
        title: '會員儲值金',
        isSelected: isSelected,
        icon: Image(image: R.image.icon_member_top_up(),width: 30.w,height: 30.w,fit: BoxFit.cover, color: isSelected ? Colors.white : MyColors.green_129_128_94,)
      ),
          (isSelected) => BaseTabWidget(
        title: '儲值紀錄',
        isSelected: isSelected,
        icon:  Image(image: R.image.icon_member_top_up_record(),width: 30.w,height: 30.w,fit: BoxFit.cover, color: isSelected ? Colors.white : MyColors.green_129_128_94,)
      ),
          (isSelected) => BaseTabWidget(
        title: '會員課程',
        isSelected: isSelected,
        icon:  Image(image: R.image.icon_member_class(),width: 30.w,height: 30.w,fit: BoxFit.cover, color: isSelected ? Colors.white : MyColors.green_129_128_94,)
      ),
    ];
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
        value: vm,
        child: SafeArea(
          child: Scaffold(
            body: MemberMainTabWidget(
              vm: vm,
              initIndex: widget.initIndex,
              childScreen: [
                ///基本資料
                BaseBackground(child: MemberInfoScreen(),),

                ///歷史訂單
                BaseBackground( child: MemberHistoryOrderScreen(),),
                ///預約訂單
                BaseBackground( child: MemberReserveScreen(vm: vm,),),
                ///會員儲值金
                BaseBackground( child: MemberInfoCashScreen(),),
                ///儲值紀錄
                BaseBackground( child: TopUpRecordScreen(),),
                ///會員課程
                BaseBackground( child: MemberClassScreen(),),

              ],
              tabs: tabs,
            ),
          ),
        ));
  }
}
