import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/screen/reserve_class/reserve_class_screen_view_model.dart';
import 'package:sbar_pos/screen/reserve_class/select_class/reserve_class_dialog_screen.dart';
import 'package:sbar_pos/screen/reserve_class/select_class/select_beautician_and_space.dart';
import 'package:sbar_pos/screen/reserve_class/select_class/select_class_screen.dart';
import 'package:sbar_pos/screen/reserve_class/select_class/select_date_screen.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';

import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../widget/btn/return_and_submit_btn.dart';
import '../../widget/dialog/MyDialog.dart';
import '../shopping_car/shopping_car_screen.dart';
import '../start/my_app.dart';

class ReserveClassScreen extends StatefulWidget {
  const ReserveClassScreen({super.key});

  @override
  State<ReserveClassScreen> createState() => _ReserveClassScreenState();
}

class _ReserveClassScreenState extends State<ReserveClassScreen> {
  final vm = ReserveClassScreenViewModel();


  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _releaseClassDialog();
    });
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: Padding(
        padding: EdgeInsets.only(bottom: 15.h, left: 16.w),
        child: Consumer<ShoppingCarProvider>(
          builder: (context, data, _) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 17.h),
                    child: Column(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              ///左側課程列表
                              Padding(
                                padding: EdgeInsets.only(top: 138.h),
                                //這邊自己管控高度Padding
                                child: SelectClassScreen(),
                              ),

                              ///中間日期選擇
                              Padding(
                                padding: EdgeInsets.only(top: 138.h),
                                //這邊自己管控高度Padding
                                child: SelectDateScreen(),
                              ),

                              ///選擇美容師與空間
                              Padding(
                                padding: EdgeInsets.only(top: 138.h),
                                //這邊自己管控高度Padding
                                child: SelectBeauticianAndSpace(),
                              ),
                            ],
                          ),
                        ),

                        ///返回與送出按鈕
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: MyColors.green_157_193_65,
                            ),
                          ),
                          child: ReturnAndSubmitBtn(
                            h: 120.h,
                            submitText: '確認預約',
                            cancelText: '取消',
                            backOnTap: () {
                              // Navigator.of(context).pop();
                            },
                            submitOnTap: () {},
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                SizedBox(width: 17.w),

                ///最右側購物車列表
                ShoppingCarScreen(),
              ],
            );
          },
        ),
      ),
    );
  }

  ///開啟選擇課程彈窗
  void _releaseClassDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: true,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: ReserveClassDialogScreen(

              ),
            ),
          ),
        ),
      ),
    );
  }
}
