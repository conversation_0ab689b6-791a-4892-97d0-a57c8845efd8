import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import 'package:sbar_pos/logger/my_print.dart';
import '../../../gen/r.dart';
import '../../../provider/shopping_car_provider.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/calender/build_single_date_calender.dart';
import '../../../widget/container_with_radius.dart';
import '../../../widget/textField/base_text_field.dart';
import '../../start/my_app.dart';

class SelectDateScreen extends StatefulWidget {
  const SelectDateScreen({super.key});

  @override
  State<SelectDateScreen> createState() => _SelectDateScreenState();
}

class _SelectDateScreenState extends State<SelectDateScreen> {
  TextEditingController dateController = TextEditingController(); //日期Controller

  //-----|日期|-----
  DateTime dateSelect = DateTime.now();

  final Set<DateTime> _selectedTimes = {};

  @override
  void dispose() {
    dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return ContainerWithRadius(
      w: 584.w,
      h: 925.h,
      r: 0.r,
      color: MyColors.grey_245_245_245_1,
      isHaveBorder: true,
      borderColor: MyColors.green_157_193_65,
      child: Padding(
        padding: EdgeInsets.only(
          left: 18.w,
          right: 10.w,
          top: 13.h,
          bottom: 13.h,
        ),
        child: Column(
          children: [
            Text(
              '選擇日期與時間',
              style: TextStyle(
                fontSize: 25.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(height: 12.h),

            //日期選擇框
            _dateSelect(),
            SizedBox(height: 23.h),

            //時間格子列表
            Expanded(
              child: _timeList(
                openTime: DateTime(
                  dateSelect.year,
                  dateSelect.month,
                  dateSelect.day,
                  11,
                  0,
                ),
                closeTime: DateTime(
                  dateSelect.year,
                  dateSelect.month,
                  dateSelect.day,
                  19,
                  45,
                ),
                unavailableList: shoppingCarVm.generateUnavailableList(
                  dateSelect.year,
                  dateSelect.month,
                  dateSelect.day,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///日期選擇框
  Widget _dateSelect() {
    return BaseTextField(
      leadWidgetPadding: EdgeInsets.only(left: 15.w, right: 156.w),
      hint: DateTime.now().toDateYMDString(),
      controller: dateController,
      w: 534.w,
      leadWidget: Image(
        image: R.image.icon_calendar(),
        width: 36.w,
        height: 40.h,
      ),
      onTap: () {
        //開啟單選日曆
        buildSingleDateCalender(
          context: context,
          onDaySelected: (selectDate, date, events) {
            dateSelect = selectDate;
          },
          onSubmit: () {
            if (dateController.text.isEmpty) {
              dateController.text = DateTime.now().toDateYMDString();
            }

            dateController.text = dateSelect.toDateYMDString();
            navigatorKey.currentState?.pop();
          },
          initDate: dateSelect,
        );
      },
      enterTextPadding: EdgeInsets.only(left: 12.w),
      isReadOnly: true,
    );
  }

  /// 時間格子列表
  Widget _timeList({
    required DateTime openTime,
    required DateTime closeTime,
    required List<DateTime> unavailableList,
  }) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final intervals = shoppingCarVm.generate15MinIntervals(openTime, closeTime);
    final disabledSlots = unavailableList
        .map((e) => e.toIso8601String())
        .toSet();
    final textStyle = TextStyle(
      fontSize: 25.sp,
      color: MyColors.green_129_128_94,
    );

    return GridView.builder(
      padding: EdgeInsets.only(bottom: 42.h),
      physics: const BouncingScrollPhysics(),
      itemCount: intervals.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 10.w,
        mainAxisSpacing: 20.h,
        childAspectRatio: 126 / 63,
      ),
      itemBuilder: (context, index) {
        final time = intervals[index];
        final isDisabled = disabledSlots.contains(time.toIso8601String());
        final isSelected = _selectedTimes.contains(time);

        return GestureDetector(
          onTap: isDisabled
              ? null
              : () {
                  setState(() {
                    if (isSelected) {
                      _selectedTimes.remove(time);
                    } else {
                      _selectedTimes.add(time);
                    }
                  });
                  onSlotSelected(time);
                },
          child: ContainerWithRadius(
            w: 126.w,
            h: 63.h,
            r: 20.r,
            color: isDisabled
                ? MyColors.grey_249_249_247
                : isSelected
                ? MyColors.yellow_251_227_140
                : Colors.white,
            isHaveBorder: false,
            boxShadow: [
              BoxShadow(
                color: const Color(0x14000000),
                offset: const Offset(3, 6),
                blurRadius: 10.r,
              ),
            ],
            child: Center(
              child: Text(
                DateFormat.Hm().format(time),
                style: textStyle.copyWith(
                  color: isDisabled
                      ? MyColors.grey_211_211_200
                      : MyColors.green_129_128_94,
                  fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void onSlotSelected(DateTime time) {
    //TODO:api
    myPrint('_selectedTimes: $_selectedTimes');
  }
}
