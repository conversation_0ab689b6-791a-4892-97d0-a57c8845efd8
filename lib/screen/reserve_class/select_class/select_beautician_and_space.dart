import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:http/http.dart';
import 'package:provider/provider.dart';

import '../../../model/base/base_model.dart';
import '../../../provider/shopping_car_provider.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/container_with_radius.dart';
import '../../../widget/shape/circle.dart';

class SelectBeauticianAndSpace extends StatefulWidget {
  const SelectBeauticianAndSpace({super.key});

  @override
  State<SelectBeauticianAndSpace> createState() =>
      _SelectBeauticianAndSpaceState();
}

class _SelectBeauticianAndSpaceState extends State<SelectBeauticianAndSpace> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithRadius(
      w: 420.w,
      h: 925.h,
      r: 0.r,
      color: MyColors.grey_245_245_245_1,
      isHaveBorder: true,
      borderColor: MyColors.green_157_193_65,
      child: Column(
        children: [
          //選擇美容師區塊
          _baseBlock(true),

          Divider(height: 1, thickness: 1, color: MyColors.green_157_193_65),

          //選擇房間區塊
          _baseBlock(false),
        ],
      ),
    );
  }

  ///
  Widget _baseBlock(bool isBeautician) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      child: ContainerWithRadius(
        w: 420.w,
        h: 400.h,
        r: 0.r,
        color: MyColors.grey_245_245_245_1,
        isHaveBorder: false,
        child: Column(
          children: [
            Text(
              isBeautician ? '選擇美容師' : '選擇房間',
              style: TextStyle(
                fontSize: 25.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(height: 7.h),

            Expanded(child: _baseList(isBeautician)),
          ],
        ),
      ),
    );
  }

  /// base list
  Widget _baseList(bool isBeautician) {
    return Selector<ShoppingCarProvider, List<BaseModel>>(
      selector: (context, vm) =>
      isBeautician ? vm.beauticianModel : vm.spaceModel,
      builder: (context, data, _) {
        // 如果是房間，就過濾掉剩餘數 <= 0 的資料
        final filteredData = isBeautician
            ? data
            : data.where((m) {
          final remaining = int.tryParse(m.content ?? '') ?? -1;
          return remaining > 0;
        }).toList();

        return ListView.separated(
          padding: EdgeInsets.zero,
          physics: BouncingScrollPhysics(),
          itemCount: filteredData.length,
          itemBuilder: (context, index) {
            final model = filteredData[index];
            return _baseListWidget(model, isBeautician);
          },
          separatorBuilder: (context, index) => SizedBox(height: 5.h),
        );
      },
    );
  }


  ///base list reserve_calender_widget
  Widget _baseListWidget(BaseModel model, bool isBeautician) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    final bool isUnableSelect = (int.tryParse(model.content ?? '') ?? -1) <= 0;
    return ContainerWithRadius(
      onTap: () {
        if (isBeautician) {
          shoppingCarVm.selectBeautician(model);
          return;
        } else {
          if (isUnableSelect) return;

          shoppingCarVm.selectSpace(model);
        }
      },
      w: double.infinity.w,
      h: 60.h,
      r: 20.r,
      color: (model.isSelect ?? false)
          ? Colors.white
          : MyColors.grey_249_249_247,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 18.w, right: 10.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (isBeautician)
              Text(
                model.title,
                style: (model.isSelect ?? false)
                    ? textStyle.copyWith(color: MyColors.brown_57_54_18)
                    : textStyle,
              ),

            if (!isBeautician)
              Text(
                '${model.title} (剩餘 ${model.content})',
                style: (model.isSelect ?? false)
                    ? textStyle.copyWith(color: MyColors.brown_57_54_18)
                    : textStyle.copyWith(
                        color: isUnableSelect
                            ? MyColors.green_200_203_191
                            : MyColors.green_129_128_94,
                      ),
              ),

            if (model.isSelect ?? false)
              Circle(
                color: MyColors.yellow_251_227_140,
                size: 40.w,
                child: Icon(Icons.check, color: MyColors.green_129_128_94),
              ),
          ],
        ),
      ),
    );
  }
}
