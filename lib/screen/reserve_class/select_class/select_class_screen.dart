import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/screen/reserve_class/select_class/reserve_class_dialog_screen.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';

import '../../../model/class/reserve_class_model.dart';
import '../../../widget/dialog/MyDialog.dart';
import '../../../widget/shape/circle.dart';
import '../reserve_class_screen_view_model.dart';

class SelectClassScreen extends StatefulWidget {
  const SelectClassScreen({super.key});

  @override
  State<SelectClassScreen> createState() => _SelectClassScreenState();
}

class _SelectClassScreenState extends State<SelectClassScreen> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithRadius(
      w: 420.w,
      h: 925.h,
      r: 0.r,
      color: MyColors.grey_245_245_245_1,
      isHaveBorder: true,
      borderColor: MyColors.green_157_193_65,
      child: Padding(
        padding: EdgeInsets.only(
          left: 18.w,
          right: 10.w,
          top: 13.h,
          bottom: 13.h,
        ),
        child: Column(
          children: [
            //選擇課程
            _selectClass(),
            SizedBox(height: 27.h),

            //選擇課程列表
            Expanded(child: _selectClassList()),
          ],
        ),
      ),
    );
  }

  ///選擇課程
  Widget _selectClass() {
    return InkWell(
      onTap: () {
        _releaseClassDialog();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '選擇課程',
            style: TextStyle(fontSize: 25.sp, color: MyColors.green_129_128_94),
          ),
          Icon(
            Icons.arrow_forward_ios,
            size: 24.w,
            color: MyColors.green_129_128_94,
          ),
        ],
      ),
    );
  }

  ///選擇課程列表
  Widget _selectClassList() {
    return Selector<ShoppingCarProvider, List<ReserveClassModel>>(
      selector: (context, vm) {
        // 如果沒選任何課程，就回傳全部 reserveClassModel
        if (vm.selectedIdsRes.isEmpty) {
          return vm.reserveClassModel;
        }
        // 否則篩選selectedIdsRes
        return vm.reserveClassModel
            .where((model) => vm.selectedIdsRes.contains((model.typeId ?? '').toString()))
            .toList();
      },
      shouldRebuild: (prev, next) => !ListEquality().equals(prev, next),
      builder: (BuildContext context, reserveClassModel, _) {
        return ListView.separated(
          physics: BouncingScrollPhysics(),
          padding: EdgeInsets.zero,
          itemCount: reserveClassModel.length,
          itemBuilder: (context, index) {
            final model = reserveClassModel[index];
            return _selectClassListWidget(model);
          },
          separatorBuilder: (context, index) => SizedBox(height: 16.h),
        );
      },
    );
  }

  ///選擇課程列表元件
  Widget _selectClassListWidget(ReserveClassModel model) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );

    return ContainerWithRadius(
      onTap: () {
        shoppingCarVm.selectReserveClass(model);
      },
      w: 400.w,
      h: 60.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 18.w, right: 10.w),
        child: Row(
          children: [
            Expanded(
              child: Text(
                model.className,
                style: model.isSelect
                    ? textStyle.copyWith(color: MyColors.brown_57_54_18)
                    : textStyle,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 11.w),

            if (model.isSelect)
              Circle(
                color: MyColors.yellow_251_227_140,
                size: 40.w,
                child: Icon(Icons.check, color: MyColors.green_129_128_94),
              ),
          ],
        ),
      ),
    );
  }

  ///開啟選擇課程彈窗
  void _releaseClassDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: true,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: ReserveClassDialogScreen(

              ),
            ),
          ),
        ),
      ),
    );
  }
}
