import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/dialog/MyDialog.dart';

import '../../../model/base/base_model.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/btn/return_and_submit_btn.dart';

class ReserveClassDialogScreen extends StatefulWidget {
  const ReserveClassDialogScreen({super.key, });

  @override
  State<ReserveClassDialogScreen> createState() =>
      _ReserveClassDialogScreenState();
}

class _ReserveClassDialogScreenState extends State<ReserveClassDialogScreen> {
  bool isAllSelected = false;

  @override
  Widget build(BuildContext context) {
    final textStyle = TextStyle(
      fontSize: 23.sp,
      color: MyColors.green_129_128_94,
    );
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 154.w),
          child: Column(
            children: [
              SizedBox(height: 35.w),
              Text(
                '預約課程',
                style: TextStyle(
                  fontSize: 44.sp,
                  color: MyColors.brown_57_54_18,
                ),
              ),
              SizedBox(height: 40.w),
              Text('請選擇課程類別', style: textStyle),
              SizedBox(height: 73.w),


              //已買課程區塊
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('已買課程', style: textStyle.copyWith(fontSize: 28.sp)),
                  SizedBox(height: 31.w),
                  _baseListWidget(
                    '全部',
                        () {
                      final provider = context.read<ShoppingCarProvider>();

                      // 清除已買課程
                      final boughtList = provider.boughtCourseModel;
                      for (final model in boughtList) {
                        model.isSelect = false;
                      }

                      // 清除單堂可買課程
                      final singleList = provider.singleCoursesAvailableModel;
                      for (final model in singleList) {
                        model.isSelect = false;
                      }

                      setState(() {
                        isAllSelected = true;
                      });
                    },
                    isAllSelected ? MyColors.green_157_193_65 : Colors.white,
                    isAllSelected ? Colors.white : MyColors.green_129_128_94,
                  ),
                  SizedBox(height: 31.w),
                  _baseList(false),
                ],
              ),

              Padding(
                padding: EdgeInsets.symmetric(vertical: 40.h),
                child: Divider(height: 1, thickness: 1),
              ),


              //單堂可買課程
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  '單堂可買課程',
                  style: textStyle.copyWith(fontSize: 28.sp),
                ),
              ),
              SizedBox(height: 31.w),
              _baseList(true),


            ],
          ),
        ),



        Spacer(),

        ///返回與送出按鈕
        ReturnAndSubmitBtn(
          submitText: '確認',
          cancelText: '返回',
          backOnTap: () {
            Navigator.of(context).pop();
          },
          submitOnTap: () {
            final shoppingCarVm = context.read<ShoppingCarProvider>();

            // 取得所有被選到的課程 id（單堂 + 已買）
            final selectedIds = [
              ...shoppingCarVm.singleCoursesAvailableModel,
              ...shoppingCarVm.boughtCourseModel,
            ]
                .where((e) => e.isSelect == true && e.iD != null)
                .map((e) => e.iD!)
                .toList();

            shoppingCarVm.updateSelectedIds(selectedIds);

            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }

  ///base list
  Widget _baseList(bool isSingleCoursesAvailable) {
    return Selector<ShoppingCarProvider, List<BaseModel>>(
      selector: (context, vm) => isSingleCoursesAvailable
          ? vm.singleCoursesAvailableModel
          : vm.boughtCourseModel,
      builder: (context, data, _) {
        return SizedBox(
          height: 96.h,
          child: ListView.separated(
            clipBehavior: Clip.none,
            scrollDirection: Axis.horizontal,
            physics: BouncingScrollPhysics(),
            padding: EdgeInsets.zero,
            itemCount: data.length,
            itemBuilder: (context, index) {
              final model = data[index];
              return _baseListWidget(
                model.title,
                () {
                  model.isSelect = !(model.isSelect ?? false);
                  setState(() {
                    isAllSelected = false;
                  });
                },
                (model.isSelect ?? false)
                    ? MyColors.green_157_193_65
                    : Colors.white,
                (model.isSelect ?? false)
                    ? Colors.white
                    : MyColors.green_129_128_94,
              );
            },
            separatorBuilder: (context, index) => SizedBox(width: 20.w),
          ),
        );
      },
    );
  }

  ContainerWithRadius _baseListWidget(
    String title,
    VoidCallback onTap,
    Color color,
    Color textColor,
  ) {
    return ContainerWithRadius(
      onTap: onTap,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      w: 196.w,
      h: 96.h,
      r: 20.r,
      color: color,
      isHaveBorder: true,
      child: Center(
        child: Text(
          title,
          style: TextStyle(fontSize: 25.sp, color: textColor),
        ),
      ),
    );
  }
}
