import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import 'package:sbar_pos/screen/reserve_calendar/calendar_screen.dart';

import '../../widget/reserve_calender_widget/state_table_widget/state_table.dart';

class ReserveCalendarScreen extends StatefulWidget {
  const ReserveCalendarScreen({super.key});

  @override
  State<ReserveCalendarScreen> createState() => _ReserveCalendarScreenState();
}

class _ReserveCalendarScreenState extends State<ReserveCalendarScreen>{

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(child: CalendarScreen()),

        StateTable()
      ],
    );
  }
}