import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/reserve_calendar_provider.dart';
import '../../../widget/reserve_calender_widget/custom_tabbarview.dart';
import '../../../widget/reserve_calender_widget/date_attendance_sheet.dart';
import '../../../widget/reserve_calender_widget/date_picker_title_widget/date_picker_title_widget_for_day.dart';
import '../../../widget/reserve_calender_widget/date_picker_title_widget/date_picker_title_widget_for_month.dart';
import '../../../widget/reserve_calender_widget/date_picker_title_widget/date_picker_title_widget_for_week.dart';
import '../../../widget/reserve_calender_widget/reservation_calendar.dart';
import '../../../widget/reserve_calender_widget/single_choice.dart';
import '../../../widget/reserve_calender_widget/week_calendar_table_widget.dart';

class CalendarScreen extends StatefulWidget{
  const CalendarScreen({super.key});

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> with TickerProviderStateMixin{


  late AnimationController _infoAniController;

  @override
  void initState() {

    _infoAniController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    super.initState();
  }

  @override
  void dispose() {
    _infoAniController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {

    int index = 0;

    return Padding(
      padding: EdgeInsets.only(top: 138.h),
      child: Column(
        children: [
          Container(
            color: Colors.transparent,
            height: 60.h, // 保持原有高度，這是頂部導航的高度
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Consumer<ReserveCalendarScreenProvider>(
                      builder: (context, vm, _) {

                        //月
                        if (vm.selectCalender == Calendar.month) {
                          final now = DateTime.now();
                          return DatePickerTitleWidgetForMonth(
                            firstDate:  DateTime(now.year - 20, 1, 1),
                            lastDate:DateTime(now.year + 20, 12, 31),
                            onDateChanged: (newDate) {
                              context
                                  .read<ReserveCalendarScreenProvider>()
                                  .updateCurrentDateForMonth(newDate);
                            },
                          );
                        }
                        //週
                        if(vm.selectCalender == Calendar.week){
                          final now = DateTime.now();
                          return DatePickerTitleWidgetForWeek(
                            firstDate:  DateTime(now.year - 20, 1, 1),
                            lastDate:DateTime(now.year + 20, 12, 31),
                            onDateChanged: (newDate) {
                              context
                                  .read<ReserveCalendarScreenProvider>()
                                  .updateCurrentDateForWeek(newDate);
                            },
                          );
                        }

                        //日
                        if(vm.selectCalender == Calendar.day){
                          final now = DateTime.now();
                          return DatePickerTitleWidgetForDay(
                            firstDate:  DateTime(now.year - 20, 1, 1),
                            lastDate:DateTime(now.year + 20, 12, 31),
                            onDateChanged: (newDate) {
                              context
                                  .read<ReserveCalendarScreenProvider>()
                                  .updateCurrentDateForDay(newDate);
                            },
                          );
                        }

                        else {
                          return SizedBox.shrink();
                        }
                      },
                    ),
                  ),
                ),


                Positioned(
                  right: MediaQuery.of(context).size.width * 0.02,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: Consumer<ReserveCalendarScreenProvider>(
                      builder: (context, vm, _) {

                        print(
                          'Consumer rebuild: ${vm.selectCalender}',
                        ); // debug 輸出
                        return SingleChoice(
                          initialValue: vm.selectCalender,
                          onSelectionChanged: (Calendar newSelection) {
                            print(
                              'SingleChoice 選擇: $newSelection',
                            ); // debug 輸出

                            vm.selectCalender = newSelection;
                            index= vm.currentTabIndex;

                            if(index==vm.currentTabIndex){
                              print('CheckEvent: ${vm.CheckEvent}');
                              print('indextpag: $index');
                              vm.setCheckEvent(false);
                            }


                          },
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),

          Expanded(
            child: Consumer<ReserveCalendarScreenProvider>(
              builder: (context, vm, _) {
                print(
                  'CustomTabBarView Consumer rebuild: ${vm.selectCalender}, currentIndex: ${vm.currentTabIndex}',
                ); // debug 輸出
                return CustomTabBarView(
                  currentIndex: vm.currentTabIndex,
                  children: [
                    //日
                    Center(child:  DataAttendanceSheet()),

                    //週
                    Center(child: WeekCalendarTableView()),

                    //月
                    Container(
                      color: Color(0xC5F9FCF2),
                      child: Center(child: ReservationCalendar()),
                    ),
                    
                    Container(
                      color: Colors.orange.withOpacity(0.1),
                      child: Center(
                        child: Text(
                          '年視圖',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}