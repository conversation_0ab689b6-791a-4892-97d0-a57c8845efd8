// 日曆狀態管理
import 'package:flutter/cupertino.dart';

class CalendarController extends ChangeNotifier {
  DateTime _selectedDate = DateTime.now();
  DateTime _focusedMonth = DateTime.now();

  DateTime get selectedDate => _selectedDate;
  DateTime get focusedMonth => _focusedMonth;

  void selectDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  void changeMonth(int monthOffset) {
    _focusedMonth = DateTime(
      _focusedMonth.year,
      _focusedMonth.month + monthOffset,
    );
    notifyListeners();
  }

  void goToMonth(DateTime month) {
    _focusedMonth = DateTime(month.year, month.month);
    notifyListeners();
  }
}