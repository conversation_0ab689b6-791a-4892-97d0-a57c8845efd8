import 'package:flutter/material.dart';
import '../../../model/reserve_calendar_model/models/event.dart';

class CalendarProvider extends ChangeNotifier {
 // List<Event> _events = [];
  // 修改為 Map<DateTime, List<EventTile>> 結構
  Map<DateTime,List<Event>> _events={};
  Event? _selectedEvent;

  //List<Event> get events => _events;
  Map<DateTime,List<Event>> get events=>_events;
  Event? get selectedEvent => _selectedEvent;

  CalendarProvider() {
    //_loadSampleEvents();
  }

  void setEventMap(Map<DateTime, List<Event>> events){
    _events=events;
    notifyListeners();
  }

  // 支援舊的 List<Event> 格式
  void setEvent(List<Event> events){
    _events=_convertListToMap(events);
    notifyListeners();
  }

  void setEventMapWithTime(Map<DateTime, List<Event>> eventsWithTime) {
    Map<DateTime, List<Event>> processedEvents = {};

    eventsWithTime.forEach((date, eventList) {
      List<Event> processedEventList = eventList.map((event) {
        // 如果事件的日期是佔位日期（1970/1/1），則需要更新為正確日期
        if (event.start.year == 1970) {
          return event.copyWithDate(date);
        }
        return event;
      }).toList();

      processedEvents[date] = processedEventList;
    });

    _events = processedEvents;
    notifyListeners();
  }


  // 新增方法：將 List<EventTile> 轉換為 Map<DateTime, List<EventTile>>
  Map<DateTime,List<Event>> _convertListToMap(List<Event> event){
    Map<DateTime, List<Event>> eventMap = {};

    for(Event events in event){
      // 取得當天的日期（去除時間部分）
      DateTime dateKey = DateTime(events.start.year,events.start.month,events.start.day);

      if(eventMap.containsKey(dateKey)){
        eventMap[dateKey]!.add(events);
      }else{
        eventMap[dateKey]=[events];
      }
    }
    return eventMap;
  }

  //為特定日期添加事件
  void addEventToDate(DateTime date,Event event){
    DateTime dateKey=DateTime(date.year,date.month,date.day);

    if(_events.containsKey(dateKey)){
      _events[dateKey]!.add(event);
    }else{
      _events[dateKey]=[event];
    }
    notifyListeners();
  }

  void addEvent(Event addEvents){
    DateTime dateKey = DateTime(addEvents.start.year, addEvents.start.month, addEvents.start.day);

    if(_events.containsKey(dateKey)){
      _events[dateKey]!.add(addEvents);
    }else{
      _events[dateKey]=[addEvents];
    }
    notifyListeners();
  }

  // 獲取特定日期的事件
List<Event>getEventsForDate(DateTime date){
    DateTime dateKey=DateTime(date.year,date.month,date.day);
    return _events[dateKey]??[];
}

  //獲取所有事件的平面列表（用於向後兼容）
  List<Event> getAllEvents(){
    List<Event> allEvents = [];
    for (var eventList in _events.values) {
      allEvents.addAll(eventList);
    }
    return allEvents;
  }




  void selectEvent(Event event) {
    _selectedEvent = event;
    notifyListeners();
    print('Selected event: ${event.title}, ${event.member}'); // 用於調試
  }
}