import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:sbar_pos/model/reserve_calendar_model/timetable_item.dart';
import 'package:sbar_pos/widget/reserve_calender_widget/timetable_controller.dart';

import '../../../provider/reserve_calendar_provider.dart';


class Person<T> {
  final String name;
  final List<TimetableItem<T>> items;

  Person({required this.name, this.items = const []});

  Person<T> copyWith({
    String? name,
    List<TimetableItem<T>>? items,
  }) {
    return Person<T>(
      name: name ?? this.name,
      items: items ?? this.items,
    );
  }
}

class TimetableProvider<T> extends ChangeNotifier {
  TimetableController _controller;
  List<Person<T>> _people = [];

  // UI Configuration
  Color? _eventCardBackgroundColor;
  Color? _eventCardBorderColor;
  CardType _eventCardType = CardType.rounded;
  bool _useTimetableEventCard = false;

  // 存儲監聽器 ID
  int? _listenerId;

  TimetableProvider({
    required TimetableController controller,
    List<Person<T>>? initialPeople,
    Color? eventCardBackgroundColor,
    Color? eventCardBorderColor,
    CardType eventCardType = CardType.rounded,
    bool useTimetableEventCard = false,
  }) : _controller = controller {
    _people = initialPeople ?? [];
    _eventCardBackgroundColor = eventCardBackgroundColor;
    _eventCardBorderColor = eventCardBorderColor;
    _eventCardType = eventCardType;
    _useTimetableEventCard = useTimetableEventCard;

    // 監聽 controller 事件並保存監聽器 ID
    _listenerId = _controller.addListener(_onControllerEvent);
  }

  // Getters
  TimetableController get controller => _controller;
  List<Person<T>> get people => List.unmodifiable(_people);
  Color? get eventCardBackgroundColor => _eventCardBackgroundColor;
  Color? get eventCardBorderColor => _eventCardBorderColor;
  CardType get eventCardType => _eventCardType;
  bool get useTimetableEventCard => _useTimetableEventCard;

  // 合併相同名稱的人員
  List<Person<T>> get mergedPeople {
    final Map<String, List<TimetableItem<T>>> map = {};
    for (final person in _people) {
      map.putIfAbsent(person.name, () => []).addAll(person.items);
    }
    return map.entries.map((e) => Person(name: e.key, items: e.value)).toList();
  }

  void _onControllerEvent(TimetableControllerEvent event) {
    notifyListeners();
  }

  // Data Management
  void setPeople(List<Person<T>> people) {
    _people = people;
    notifyListeners();
  }

  void addPerson(Person<T> person) {
    _people.add(person);
    notifyListeners();
  }

  void removePerson(String name) {
    _people.removeWhere((person) => person.name == name);
    notifyListeners();
  }

  void updatePerson(String oldName, Person<T> newPerson) {
    final index = _people.indexWhere((person) => person.name == oldName);
    if (index != -1) {
      _people[index] = newPerson;
      notifyListeners();
    }
  }

  // UI Configuration
  void setEventCardBackgroundColor(Color? color) {
    _eventCardBackgroundColor = color;
    notifyListeners();
  }

  void setEventCardBorderColor(Color? color) {
    _eventCardBorderColor = color;
    notifyListeners();
  }

  void setEventCardType(CardType type) {
    _eventCardType = type;
    notifyListeners();
  }

  void setUseTimetableEventCard(bool use) {
    _useTimetableEventCard = use;
    notifyListeners();
  }

  @override
  void dispose() {
    // 使用監聽器 ID 來移除監聽器
    if (_listenerId != null) {
      _controller.removeListener(_listenerId!);
    }
    super.dispose();
  }
}