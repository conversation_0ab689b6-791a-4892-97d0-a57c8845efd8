import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/screen/start/my_app.dart';
import 'package:sbar_pos/util/MyRoutes.dart';
import 'package:sbar_pos/widget/shape/circle.dart';
import '../../model/product/product_model.dart';
import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../../widget/btn/btn_with_green_shadow.dart';
import '../../widget/cached_network_image/error_cached_network_image.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/dialog/MyDialog.dart';
import '../../widget/dialog/sbar_style_dialog.dart';
import '../../widget/member/member_area.dart';
import '../../widget/member/member_area_un_select.dart';
import '../../widget/shopping/shopping_list.dart';
import '../payment/payment_screen.dart';
import '../search_member/search_member_screen.dart';

class ShoppingCarScreen extends StatefulWidget {
  const ShoppingCarScreen({super.key});

  @override
  State<ShoppingCarScreen> createState() => _ShoppingCarScreenState();
}

class _ShoppingCarScreenState extends State<ShoppingCarScreen> {



  @override
  Widget build(BuildContext context) {
    return SafeArea(child: _shoppingCarList());
  }

  ///右側購物車列表
  Widget _shoppingCarList() {
    return Consumer<ShoppingCarProvider>(
      builder: (context, vm ,_){
        return  ColoredBox(
          color: MyColors.grey_249_249_249,
          child: SizedBox(
            width: 460.w,
            child: Column(
              children: [
                ///上方會員區塊
                if(vm.memberName == null)
                  _memberAreaUnSelect(),

                if(vm.memberName != null)
                  _memberArea(),

                ///中間列表
                Expanded(child: ShoppingList(
                  padding: EdgeInsets.symmetric(horizontal: 17.w, vertical: 17.h),
                  w: 428.w,
                  h: 145.h,  )),

                ///下方結帳與清空按鈕
                _payAndClearBtn(),
              ],
            ),
          ),
        );
      },

    );


  }

  ///上方會員區塊(未選擇會員)
  Widget _memberAreaUnSelect() {
    return MemberAreaUnSelect();
  }

  ///上方會員區塊(已選擇會員)
  Widget _memberArea() {
   return MemberArea();
  }



  ///結帳與清空按鈕
  Widget _payAndClearBtn() {
    return SizedBox(
      height: 140.h,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 17.w),
        child: Row(
          children: [
            //清空按鈕
            Consumer<ShoppingCarProvider>(
              builder: (context, shoppingCarVm, _) {
                return ContainerWithRadius(
                  isHaveBorder: true,
                  onTap: () {
                    shoppingCarVm.clearShoppingCar();
                  },
                  w: 138.w,
                  h: 120.h,
                  r: 20.r,
                  color: MyColors.red_224_129_125,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x14000000),
                      offset: const Offset(3, 6),
                      blurRadius: 10.r,
                    ),
                  ],
                  child: Center(
                    child: Text(
                      '清空',
                      style: TextStyle(color: Colors.white, fontSize: 28.sp),
                    ),
                  ),
                );
              },
            ),

            SizedBox(width: 19.w),

            //結帳按鈕
            ContainerWithRadius(
              onTap: ()async{
                final shoppingCarVm = context.read<ShoppingCarProvider>();
                if(shoppingCarVm.shoppingCarListModel.isEmpty){
                  sbarStyleDialog(
                      context: context,
                      yesAction: () {
                        navigatorKey.currentState?.pop();
                      },
                      title: '提醒您！',
                      content: '購物車尚未選擇商品。');
                  return;
                }

                final bool res = await Navigator.of(context).push(MaterialPageRoute( settings: RouteSettings(
                    name: PageName.payment.toString()),
                    builder: (BuildContext context) => PaymentScreen(pageName: PageName.payment.toString(),)));

                if(res){
                  setState(() {

                  });
                }
              },
              isHaveBorder: true,
              w: 268.w,
              h: 120.h,
              r: 20.r,
              color: MyColors.yellow_251_227_140,
              boxShadow: [
                BoxShadow(
                  color: const Color(0x14000000),
                  offset: const Offset(3, 6),
                  blurRadius: 10.r,
                ),
              ],
              child: Center(
                child: Text(
                  '進入結帳',
                  style: TextStyle(
                    color: MyColors.brown_57_54_18,
                    fontSize: 28.sp,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


}
