import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../model/class/class_confirm_model.dart';

class ClassConfirmScreenViewModel with ChangeNotifier {
  RefreshController refreshController = RefreshController();
  TextEditingController memberNameController = TextEditingController(); //會員名稱
  TextEditingController classNameController = TextEditingController(); //課程名稱
  TextEditingController phoneController = TextEditingController(); //電話號碼

  //-----|日期|-----
  DateTime rangeStartDate = DateTime.now();
  DateTime rangeEndDate = DateTime.now();

  String startStr = '';
  String endStr = '';
  String year = '';

  ///
  List<ClassConfirmModel> classConfirmModel = [
    ClassConfirmModel(
      orderId: 'A123456',
      memberName: '王小明',
      memberPhone: '09125467899',
      classConfirmInnerInfo: [
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
      ],
      isAllSign: true,
    ),
    ClassConfirmModel(
      orderId: 'A123456',
      memberName: '陳金鋒',
      memberPhone: '09125467899',
      classConfirmInnerInfo: [
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
      ],
      isAllSign: false,
    ),
    ClassConfirmModel(
      orderId: 'A123456',
      memberName: '張德陽',
      memberPhone: '09125467899',
      classConfirmInnerInfo: [
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
      ],
      isAllSign: false,
    ),
    ClassConfirmModel(
      orderId: 'A123456',
      memberName: '陸曉琪',
      memberPhone: '09125467899',
      classConfirmInnerInfo: [
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
        ClassConfirmInfo(
          classId: 'C1252',
          className: '課程名稱' * 10,
          classConfirmInnerInfo: [
            ClassConfirmInnerInfo(
              reserveId: 'AC152500',
              reserveClassTime: '2025-08-31 15:00-16:15',
              isSign: false,
            ),
          ],
        ),
      ],
      isAllSign: false,
    ),
  ];
}
