import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/extension/my_extension.dart';

import '../../logger/my_print.dart';
import '../../model/class/class_confirm_model.dart';
import '../../model/member/member_class_model.dart';
import '../../resource/MyColor.dart';
import '../../widget/base_title_list_bar/class_confirm/class_confirm_base_list_bar.dart';
import '../../widget/base_title_list_bar/class_confirm/class_confirm_base_title_bar.dart';
import '../../widget/base_title_list_bar/member_class_base_list_bar.dart';
import '../../widget/base_title_list_bar/member_class_base_title_bar.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/hint/empty_hint.dart';
import '../../widget/textField/base_text_field.dart';
import '../../widget/textField/sbar_style_range_date_Text_field.dart';
import '../main/drawer/base_scaffold_with_drawer.dart';
import 'class_confirm_screen_view_model.dart';

class ClassConfirmScreen extends StatefulWidget {
  const ClassConfirmScreen({super.key});

  @override
  State<ClassConfirmScreen> createState() => _ClassConfirmScreenState();
}

class _ClassConfirmScreenState extends State<ClassConfirmScreen> {

  final vm = ClassConfirmScreenViewModel();

  @override
  void initState() {
    _initRangeDate();
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  ///初始化日期區間
  void _initRangeDate() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      vm.startStr = startOfMonth.toDateYMDString();
      vm.endStr = endOfMonth.toDateYMDString();

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,

      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Stack(
          children: [
            BaseScaffoldWithDrawer(
              title: '課程確認書查詢',
              child: Padding(
                padding: EdgeInsets.only(
                  top: 142.h,
                  left: 19.w,
                  right: 23.w,
                  bottom: 19.h,
                ),
                child: Column(
                  children: [

                    ///搜尋列
                    _searchBar(),

                    SizedBox(height: 13.h),

                    ///預約訂單列表區塊
                    Expanded(child: _classConfirmArea())
                  ],
                ),
              ),
            ),


          ],
        ),
      ),
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(left: 25.w, right: 12.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [



            ///日期選擇
            _dateTextField(),
            SizedBox(width: 7.w),

            ///會員名稱輸入框
            BaseTextField(
                hint: '請輸入會員名稱', controller: vm.memberNameController),
            SizedBox(width: 11.w),

            ///課程名稱輸入框
            BaseTextField(
                hint: '請輸入課程名稱', controller: vm.classNameController),
            SizedBox(width: 11.w),

            ///電話號碼輸入框
            BaseTextField(
              isOnlyNum: true,
              w: 233.w,
              hint: '請輸入電話號碼',
              controller: vm.phoneController,

            ),
            SizedBox(width: 14.w),

            ///搜尋按鈕
            ContainerWithRadius(
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///課程確認區塊
  Widget _classConfirmArea() {
    return SizedBox(
      width: double.infinity.w,
      child: Column(
        children: [
          //標頭
          ClassConfirmBaseTitleBar(),
          Expanded(child: _classConfirmList()),
        ],
      ),
    );
  }

  ///課程確認列表
  Widget _classConfirmList() {
    return Selector<ClassConfirmScreenViewModel, List<ClassConfirmModel>>(
      selector: (context, vm) => vm.classConfirmModel,
      builder: (context, classConfirmModel, _) {

        return SmartRefresher(
          controller: vm.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            vm.refreshController.refreshCompleted();
          },
          child:  classConfirmModel.isEmpty
              ? ListView(
            // 用 ListView 包住 EmptyHint，這樣才能下拉
            children: [
              SizedBox(
                height:
                MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
              ),
              Center(child: EmptyHint(hint: '尚無課程確認書列表')),
            ],
          )
              : ListView.builder(
            padding: EdgeInsets.only(bottom: 50.h),
            physics: BouncingScrollPhysics(),
            itemCount: classConfirmModel.length,
            itemBuilder: (context, index) {
              final model = classConfirmModel[index];
              return ClassConfirmBaseListBar(
                orderId: model.orderId,
                isAllSign: model.isAllSign,
                memberName: model.memberName,
                memberPhone: model.memberPhone,
                classConfirmInnerInfo: model.classConfirmInnerInfo,
                classConfirmModel: classConfirmModel,
              );
            },
          ),
        );
      },
    );

  }

  ///日期選擇輸入框
  Widget _dateTextField() {
    return SbarStyleRangeDateTextField(
      isCanSelectAfterThisYear: true,
      rangeStartDate: vm.rangeStartDate,
      rangeEndDate: vm.rangeEndDate,
      startStr: vm.startStr,
      endStr: vm.endStr,
      onRangeSubmit: (start, end) {
        setState(() {
          vm.rangeStartDate = start;
          vm.rangeEndDate = end;
          vm.startStr = start.toDateYMDString();
          vm.endStr = end.toDateYMDString();
          myPrint('start: $start');
          myPrint('end: $end');
        });
      },
      onSpeedSelectMonthSubmit: (int year,int month) {
        vm.year = year.toString();

        // 該月的最後一天
        final lastDay = DateTime(year, month + 1, 0);

        // 格式化為 yyyy-MM-dd
        vm.startStr = "$year-${month.toString().padLeft(2, '0')}-01";
        vm.endStr =
        "$year-${month.toString().padLeft(2, '0')}-${lastDay.day
            .toString()
            .padLeft(2, '0')}";

        myPrint("開始日期: ${vm.startStr}");
        myPrint("結束日期: ${vm.endStr}");

        setState(() {});
      },
    );
  }

}
