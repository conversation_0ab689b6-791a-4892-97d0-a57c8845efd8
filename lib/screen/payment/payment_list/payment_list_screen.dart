import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/textField/my_text_field.dart';

import '../../../gen/r.dart';
import '../../../model/performance/performance_model.dart';
import '../../../provider/shopping_car_provider.dart';
import '../../../resource/MyColor.dart';
import '../../../util/MyRoutes.dart';
import '../../../util/StringUtils.dart';
import '../../../widget/dialog/MyDialog.dart';
import '../../choose_performance_personnel/choose_performance_personnel_screen.dart';

class PaymentListScreen extends StatefulWidget {
  final String pageName;

  const PaymentListScreen({super.key, required this.pageName});

  @override
  State<PaymentListScreen> createState() => _PaymentListScreenState();
}

class _PaymentListScreenState extends State<PaymentListScreen> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: _shoppingCarList());
  }

  ///右側金額列表
  Widget _shoppingCarList() {
    return Consumer<ShoppingCarProvider>(
      builder: (context, vm, _) {
        return ColoredBox(
          color: Colors.white.withOpacity(0.5),
          child: SizedBox(
            width: 460.w,
            child: Column(
              children: [
                ///上方銷售人員區塊
                _salesInfo(),

                ///中間列表
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 21.w),
                    child: _paymentList(),
                  ),
                ),

                ///提示字
                _hintArea(),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 43.25.w),
                  child: SizedBox(height: 15.h),
                ),

                ///下方按鈕
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 21.w),
                  child: _btnRow(),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  ///提示字區塊
  Widget _hintArea() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.warning, color: Colors.red),
        SizedBox(width: 11.5.w),
        Text(
          '付款總金額必須大於商品總金額',
          style: TextStyle(fontSize: 23.sp, color: MyColors.red_230_75_75),
        ),
      ],
    );
  }

  ///上方銷售人員區塊
  Widget _salesInfo() {
    return Selector<ShoppingCarProvider, String?>(
      selector: (context, vm) {
        if (vm.staff.isNotEmpty) {
          return vm.staff.values.first;
        }
        return null;
      },
      builder: (context, staffName, _) {
        return InkWell(
          onTap: () {
            _performancePersonnelDialog();
          },
          child: Container(
            height: 94.h,
            color: MyColors.yellow_251_227_140,
            child: Padding(
              padding: EdgeInsets.only(right: 21.w, left: 25.w),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '銷售人員：',
                    style: TextStyle(
                      fontSize: 23.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                  Spacer(),
                  Text(
                    staffName ?? '未選擇',
                    style: TextStyle(
                      fontSize: 23.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Image(
                    image: R.image.icon_arrow_down_with_circle(),
                    width: 40.w,
                    height: 40.w,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  ///金額列表
  Widget _paymentList() {
    final titleStyle = TextStyle(
      fontSize: 32.sp,
      color: MyColors.green_129_128_94,
      fontWeight: FontWeight.w700,
    );
    final titlePriceStyle = TextStyle(
      fontSize: 32.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w700,
    );

    final shoppingCarVm = context.watch<ShoppingCarProvider>();
    return Column(
      children: [
        SizedBox(height: 23.5.h),

        //未付款金額
        Row(
          children: [
            Text('未付款金額', style: titleStyle),
            Spacer(),
            Text(
              '\$ ${StringUtils.formatMoneyForDouble(shoppingCarVm.getFinalAmount())}',
              style: titlePriceStyle,
            ),
          ],
        ),

        Padding(
          padding: EdgeInsets.only(top: 23.5.h, bottom: 13.h),
          child: Divider(thickness: 1, height: 1),
        ),

        // //現金
        // _basePriceRow('現金', shoppingCarVm.cashController),
        // SizedBox(height: 15.h),
        //
        // //信用卡
        // _basePriceRow('信用卡', shoppingCarVm.creditCardController),
        // SizedBox(height: 15.h),
        //
        // //LINE Pay
        // _basePriceRow('LINE Pay', shoppingCarVm.linePayController),
        // SizedBox(height: 15.h),
        Column(
          children: shoppingCarVm.paymentTypes.map((type) {
            final controller = context
                .read<ShoppingCarProvider>()
                .getController(type);
            return Padding(
              padding: EdgeInsets.only(bottom: 15.h),
              child: _basePriceRow(type, controller),
            );
          }).toList(),
        ),

        Divider(thickness: 1, height: 1),
        SizedBox(height: 23.5.h),

        //找零
        Row(
          children: [
            Text('找零', style: titleStyle),
            Spacer(),
            Text(
              '\$ ${StringUtils.formatMoneyForDouble(shoppingCarVm.getChangeAmount())}',
              style: titlePriceStyle,
            ),
          ],
        ),
        SizedBox(height: 40.5.h),

        //發票號碼
        _invoiceIdArea(),
      ],
    );
  }

  ///按鈕列
  Widget _btnRow() {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final bool isOnlyShowPayBtn =
        widget.pageName == PageName.historyInfo.toString();

    //判斷是否有課程
    final bool hasClass = shoppingCarVm.shoppingCarListModel.any(
      (item) => item.type == 2,
    );

    if (isOnlyShowPayBtn) {
      return SizedBox(
        width: double.infinity,
        child: _baseBtn(() {}, '確認付款', MyColors.brown_57_54_18),
      );
    }

    // 如果有課程，就顯示兩顆按鈕
    if (hasClass) {
      return Row(
        children: [
          _baseBtn(() {}, '先成單，後付款', MyColors.orange_240_149_68),
          SizedBox(width: 11.w),
          _baseBtn(() {}, '確認付款', MyColors.brown_57_54_18),
        ],
      );
    }

    // 沒有則只顯示「確認付款」
    return SizedBox(
      width: double.infinity,
      child: _baseBtn(() {}, '確認付款', MyColors.brown_57_54_18),
    );
  }

  ///base price row
  Widget _basePriceRow(String title, TextEditingController controller) {
    final subTitleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    final subTitlePriceStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
    );
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: subTitleStyle),
        SizedBox(
          width: 250.w,
          child: MyTextField(
            isSingleLine: true,
            style: subTitlePriceStyle,
            isOnlyNum: true,
            controller: controller,
            border: 20.r,
            height: 74.h,
          ),
        ),
      ],
    );
  }

  ///base btn
  Widget _baseBtn(VoidCallback onTap, String title, Color color) {
    return ContainerWithRadius(
      onTap: onTap,
      isHaveBorder: true,
      w: 198.w,
      h: 120.h,
      r: 20.r,
      color: color,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          title,
          style: TextStyle(color: MyColors.grey_248_248_248, fontSize: 25.sp),
        ),
      ),
    );
  }

  ///發票號碼區塊
  Widget _invoiceIdArea() {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return Container(
      width: 416.w,
      height: 120.h,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        border: Border.all(color: MyColors.grey_235_235_235, width: 1),
        boxShadow: [
          BoxShadow(
            color: const Color(0x14000000),
            offset: const Offset(3, 6),
            blurRadius: 10.r,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w, vertical: 13.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '發票號碼',
              style: TextStyle(
                fontSize: 25.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(height: 8.h),
            MyTextField(
              isSingleLine: true,
              hint: '請輸入發票號碼',
              style: TextStyle(fontSize: 23.sp, color: MyColors.brown_57_54_18),
              textAlign: TextAlign.start,
              enterTextPadding: EdgeInsets.only(left: 15.w),
              controller: shoppingCarVm.invoiceController,
              border: 20.h,
              height: 54.h,
            ),
          ],
        ),
      ),
    );
  }

  ///開啟選擇業績人員彈窗
  void _performancePersonnelDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: true,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: ChoosePerformancePersonnelScreen(),
            ),
          ),
        ),
      ),
    );
  }
}
