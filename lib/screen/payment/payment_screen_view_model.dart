import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';

class PaymentScreenViewModel with ChangeNotifier {
  final TextEditingController accountController = TextEditingController();
  final TextEditingController pwdController = TextEditingController();
  final TextEditingController inputDiscountController =
  TextEditingController(); //折扣數輸入


  List<int> speedDiscountModel = [75, 70, 65, 60, 55, 50]; //快速折扣



  AnimationController? animationAccountController;
  AnimationController? animationPwdController;

  bool _isChangeAccount = false; //是否點擊切換帳號

  bool get isChangeAccount => _isChangeAccount;

  set isChangeAccount(bool value) {
    _isChangeAccount = value;
    notifyListeners();
  }

  bool _loading = false;

  bool get loading => _loading;

  set loading(bool value) {
    _loading = value;
    notifyListeners();
  }

  ///登入邏輯
  void loginLogic(BuildContext context, Function()? yesAction) async {
    if (loading) return;

    //帳號為空處理
    if (accountController.text.isEmpty) {
      animationAccountController?.reset();
      animationAccountController?.forward();
      return;
    }

    //密碼為空處理
    if (pwdController.text.isEmpty) {
      animationPwdController?.reset();
      animationPwdController?.forward();
      return;
    }
    loading = true;

    //TODO:api（這邊不把會員寫進DB）
    await Future.delayed(500.ms);
    isChangeAccount = false;
  }
}
