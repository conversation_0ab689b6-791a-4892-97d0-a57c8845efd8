import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/shape/circle.dart';
import 'package:sbar_pos/widget/wallet/wallet_info.dart';

import '../../../model/member/member_cash_model.dart';
import '../../../provider/shopping_car_provider.dart';
import '../../../util/StringUtils.dart';
import '../../../widget/dialog/MyDialog.dart';
import '../../../widget/dialog/sbar_style_dialog.dart';
import '../../../widget/payment/manual_discount_row.dart';
import '../../member/member_cash/member_cash_screen.dart';
import '../../start/my_app.dart';
import '../payment_discount/payment_discount_screen.dart';
import '../payment_screen_view_model.dart';

class PaymentDetailsArea extends StatefulWidget {
  final EdgeInsets? padding;
  final bool? isHistoryInfo;
  final TextEditingController? controller;
  final PaymentScreenViewModel? paymentScreenViewModel;

  //這邊只有這邊只有訂單內容才需傳入
  final String? cashName;
  final int? classOriginalPrice; //課程金額小計(原價)
  final int? classDiscountPrice; //課程金額小計(折扣後)
  final int? walletPay; //錢包支付金額
  final int? unPaid; //課程未付款金額
  final int? payCount; //購買數量

  const PaymentDetailsArea({
    super.key,
    this.padding,
    this.isHistoryInfo,
    this.cashName,
    this.classOriginalPrice,
    this.classDiscountPrice,
    this.walletPay,
    this.unPaid,
    this.payCount,
    this.controller,
    this.paymentScreenViewModel,
  });

  @override
  State<PaymentDetailsArea> createState() => _PaymentDetailsAreaState();
}

class _PaymentDetailsAreaState extends State<PaymentDetailsArea> {

  @override
  void initState() {
    super.initState();

    widget.controller?.addListener(() {
      final shoppingCarVm = context.read<ShoppingCarProvider>();
      // 把值同步給 provider
      shoppingCarVm.setManualDiscount(
        double.tryParse(widget.controller?.text ?? '') ?? 0,
      );
    });
  }


  @override
  Widget build(BuildContext context) {
    final titleStyle = TextStyle(
      fontSize: widget.isHistoryInfo ?? false ? 28.sp : 32.sp,
      color: MyColors.green_129_128_94,
      fontWeight: FontWeight.w700,
    );
    final titlePriceStyle = TextStyle(
      fontSize: widget.isHistoryInfo ?? false ? 28.sp : 32.sp,
      color: MyColors.brown_57_54_18,
      fontWeight: FontWeight.w700,
    );
    return Consumer<ShoppingCarProvider>(
      builder: (context, shoppingCarVm, _) {
        return Padding(
          padding:
              widget.padding ??
              EdgeInsets.only(
                top: 34.h,
                bottom: 23.5.h,
                left: 19.w,
                right: 22.w,
              ),
          child: Column(
            children: [
              //購買數量
              _baseDetailsRow(
                '購買數量：',
                0,
                child: Text(
                  (widget.payCount ?? shoppingCarVm.shoppingCarListModel.length)
                      .toString(),
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: MyColors.brown_57_54_18,
                  ),
                ),
              ),
              SizedBox(height: 20.5.h),
              _baseDivider(),
              SizedBox(height: 20.5.h),

              //商品金額小計
              _baseDetailsRow(
                '商品金額小計：',
                shoppingCarVm.getAmountByType(1),
              ),

              SizedBox(height: 20.5.h),
              _baseDivider(),
              SizedBox(height: 20.5.h),

              //課程金額小計
              SizedBox(
                height: (widget.isHistoryInfo ?? false) ? 300.h : 280.5.h,
                child:
                    Selector<
                      ShoppingCarProvider,
                      (List<MemberCashModel>, bool)
                    >(
                      selector: (context, vm) => (
                        List<MemberCashModel>.from(
                          vm.memberCashModel.where((item) => item.isSelect),
                        ),
                        vm.isSelectWallet,
                      ),

                      builder: (context, record, _) {
                        final selectedCashList = record.$1;
                        final bool isShowWalletPayment =
                            selectedCashList.isNotEmpty ||
                            shoppingCarVm.isSelectWallet;
                        return Column(
                          children: [
                            //課程金額小計
                            _baseDetailsRow(
                              '課程金額小計：',
                              shoppingCarVm.getAmountByType(2),
                            ),
                            SizedBox(height: isShowWalletPayment ? 2.h : 17.h),

                            //錢包已付總價
                            // _baseDetailsRow('錢包已付總價：', 0, isHaveDot: false,isHaveMinus: true),
                            // SizedBox(height: 15.h),

                            //錢包已付總價
                            if (isShowWalletPayment)
                              _baseDetailsRow(
                                '錢包已付總價：',
                                //TODO:歷史訂單內頁修改為此筆訂單的錢包已付總價
                                shoppingCarVm.inputValue.toDouble(),
                                isHaveDot: false,
                                  isHaveMinus: true
                              ),
                            if (isShowWalletPayment) SizedBox(height: 17.h),

                            //錢包(訂單內容)
                            if (widget.isHistoryInfo ?? false)
                              _orderInfoWallet(),

                            //錢包
                            if (!(widget.isHistoryInfo ?? false))
                              _selectWallet(),
                          ],
                        );
                      },
                    ),
              ),

              SizedBox(height: (widget.isHistoryInfo ?? false) ? 0.h : 20.5.h),
              _baseDivider(),
              SizedBox(height: (widget.isHistoryInfo ?? false) ? 10.h : 20.5.h),

              //儲值金小計
              if (!(widget.isHistoryInfo ?? false))
                _baseDetailsRow(
                  '儲值金小計：',
                  shoppingCarVm.getAmountByType(3),
                ),

              if (!(widget.isHistoryInfo ?? false))
                Column(
                  children: [
                    SizedBox(height: 25.5.h),
                    _baseDivider(),
                    SizedBox(height: 20.5.h),

                    //現場折價
                    _baseDetailsRow(
                      '現場折價：',
                      shoppingCarVm.getDiscountAmount(),
                      child: InkWell(
                        onTap: (widget.cashName ?? '').isNotEmpty
                            ? null
                            : () {
                                _paymentDiscountScreenDialog();
                              },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '\$ ${StringUtils.formatMoneyForDouble(shoppingCarVm.getDiscountAmount())}',
                              style: TextStyle(
                                fontSize: 28.sp,
                                color: MyColors.brown_57_54_18,
                              ),
                            ),

                            SizedBox(width: 22.w),
                            ContainerWithRadius(
                              w: 40.w,
                              h: 40.h,
                              r: 20.r,
                              color: Colors.white,
                              isHaveBorder: true,
                              child: Center(
                                child: Icon(
                                  size: 40.w,
                                  Icons.arrow_drop_down,
                                  color: MyColors.grey_214_213_213,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    SizedBox(height: 20.5.h),
                    _baseDivider(),
                    SizedBox(height: 4.h),

                    //現場減價
                    _manualDiscountRow('現場減價', 0, true),

                    SizedBox(height: 4.h),
                    _baseDivider(),
                    SizedBox(height: 4.h),

                    // //額外費用
                    // _manualDiscountRow('額外費用', 0),
                    // SizedBox(height: 4.h),
                    // _baseDivider(),
                  ],
                ),

              Spacer(),

              //商品總價
              Row(
                children: [
                  Text('商品總價', style: titleStyle),
                  Spacer(),
                  Text(
                    '\$ ${StringUtils.formatMoneyForDouble(shoppingCarVm.getFinalAmount())}',
                    style: titlePriceStyle,
                  ),
                ],
              ),

              SizedBox(height: 9.h),

              //已付款金額
              Row(
                children: [
                  Text('已付款金額', style: titleStyle),
                  Spacer(),
                  Text(
                    '\$ ${StringUtils.formatMoneyForDouble(0)}',
                    style: titlePriceStyle,
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  ///現場減價 & 額外費用
  Widget _manualDiscountRow(String title, double price, [bool? isMinus]) {
    return ManualDiscountRow(
      isReadOnly: widget.cashName?.isNotEmpty,
      controller: widget.controller,
      isHaveDot: true,
      title: title,
      price: price,
      isMinus: isMinus ?? false,
    );
  }

  ///錢包(未選)
  Widget _unSelectWallet() {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return InkWell(
      onTap: () {
        if ((shoppingCarVm.memberPhone ?? '').isEmpty) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '提醒您！',
            content: '請先選擇會員',
          );
          return;
        }
        _memberCashDialog();
      },
      child: Align(
        alignment: Alignment.centerRight,
        child: ContainerWithRadius(
          w: 474.w,
          h: 60.h,
          r: 20.r,
          color: Colors.white,
          isHaveBorder: true,
          child: Padding(
            padding: EdgeInsets.only(left: 17.w, right: 21.w),
            child: Row(
              children: [
                Text(
                  '請先選擇扣款儲值錢包',
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: MyColors.green_129_128_94,
                  ),
                ),
                Spacer(),
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: 23.w,
                  color: MyColors.grey_204_204_204,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///錢包(已選)
  Widget _selectWallet() {
    return Selector<ShoppingCarProvider, (List<MemberCashModel>, bool)>(
      selector: (context, vm) => (
        List<MemberCashModel>.from(
          vm.memberCashModel.where((item) => item.isSelect),
        ),
        vm.isSelectWallet,
      ),

      builder: (context, record, _) {
        final selectedCashList = record.$1;
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        if (selectedCashList.isEmpty || !shoppingCarVm.isSelectWallet) {
          return _unSelectWallet();
        }

        final resData = selectedCashList.first;
        return WalletInfo(
          isHaveOnTap: !(widget.isHistoryInfo ?? false),
          onTap: () {
            _memberCashDialog();
          },
          cashName: resData.cashName,
          classOriginalPrice:   shoppingCarVm.getAmountByType(2),
          classDiscountPrice:   shoppingCarVm.getAmountByType(2), //TODO:需釐清需釐清折扣金額
          walletPay: shoppingCarVm.inputValue.toDouble(),
          unPaid: shoppingCarVm.getAmountByType(2) - shoppingCarVm.inputValue.toDouble(),//TODO:需釐清需釐清折扣金額
        );
      },
    );
  }

  ///錢包（for錢包訂單內容）
  Widget _orderInfoWallet() {
    return WalletInfo(
      isHaveOnTap: !(widget.isHistoryInfo ?? false),
      onTap: () {
        _memberCashDialog();
      },
      cashName: '儲值金錢包A',
      classOriginalPrice: 9000,
      classDiscountPrice: 6000,
      walletPay: 50,
      unPaid: 1300,
    );
  }

  ///base details row
  Widget _baseDetailsRow(
    String title,
    double totalPrice, {
    Widget? child,
    bool? isHaveDot,
    bool? isHaveMinus,
  }) {
    return Row(
      children: [
        Opacity(
          opacity: isHaveDot ?? true ? 1 : 0,
          child: Circle(color: MyColors.green_121_131_90, size: 15.w),
        ),
        SizedBox(width: 21.w),
        Text(
          title,
          style: TextStyle(fontSize: 28.sp, color: MyColors.green_129_128_94),
        ),

        Spacer(),

        child ??
            Text(
              (isHaveMinus ?? false)
                  ? '-\$ ${StringUtils.formatMoneyForDouble(totalPrice)}'
                  : '\$ ${StringUtils.formatMoneyForDouble(totalPrice)}',
              style: TextStyle(fontSize: 28.sp, color: MyColors.brown_57_54_18),
            ),
      ],
    );
  }

  ///會員儲值金Dialog
  void _memberCashDialog() {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: false,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: MemberCashScreen(
                backOnTap: () {
                  Navigator.of(context).pop();
                },
                submitOnTap: () {
                  Navigator.of(context).pop();

                  //更新錢包選取狀態
                  shoppingCarVm.setSelectWalletStatue();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  ///
  Widget _baseDivider() {
    return Divider(thickness: 1, height: 1, color: MyColors.grey_156_157_152);
  }

  ///開啟現場折價彈窗
  void _paymentDiscountScreenDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
              isHaveBorder: true,
              w: double.infinity.w,
              h: double.infinity.h,
              r: 20.r,
              color: Colors.white,
              child: PaymentDiscountScreen(
                vm: widget.paymentScreenViewModel ?? PaymentScreenViewModel(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
