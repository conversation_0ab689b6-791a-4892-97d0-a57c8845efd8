import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/screen/payment/payment_details/payment_details_area.dart';
import 'package:sbar_pos/screen/payment/payment_list/payment_list_screen.dart';
import 'package:sbar_pos/screen/payment/payment_screen_view_model.dart';
import 'package:sbar_pos/screen/payment/payment_tape_switch_area/payment_tape_switch_area.dart';

import '../../gen/r.dart';
import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../route_observer/page_info.dart';
import '../../util/MyRoutes.dart';
import '../../widget/tab/base_tab_widget.dart';
import '../../widget/tab/payment_tab.dart';

class PaymentScreen extends StatefulWidget {
  final String pageName;
  const PaymentScreen({super.key, required this.pageName, });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final vm = PaymentScreenViewModel();
  late final List<Widget Function(bool isSelected)> tabs;

  @override
  void initState() {
    super.initState();
    tabs = [
      (isSelected) => BaseTabWidget(
        title: '基本資料',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_member_info(),
          width: 30.w,
          height: 30.w,
          fit: BoxFit.cover,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
        ),
      ),
      (isSelected) => BaseTabWidget(
        title: '歷史訂單',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_member_history_record(),
          width: 30.w,
          height: 30.w,
          fit: BoxFit.cover,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
        ),
      ),
      (isSelected) => BaseTabWidget(
        title: '預約訂單',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_member_reserve(),
          width: 30.w,
          height: 30.w,
          fit: BoxFit.cover,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
        ),
      ),
      (isSelected) => BaseTabWidget(
        title: '會員儲值金',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_member_top_up(),
          width: 30.w,
          height: 30.w,
          fit: BoxFit.cover,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
        ),
      ),
      (isSelected) => BaseTabWidget(
        title: '儲值紀錄',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_member_top_up_record(),
          width: 30.w,
          height: 30.w,
          fit: BoxFit.cover,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
        ),
      ),
      (isSelected) => BaseTabWidget(
        title: '會員課程',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_member_class(),
          width: 30.w,
          height: 30.w,
          fit: BoxFit.cover,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
        ),
      ),
    ];
  }

  @override
  void dispose() {
    //這邊需要將路由消散，否則訂單內容付款會判斷目前為PageName.payment導致無法再進行跳頁。
    PageInfo().pageName.value = PageName.historyInfo.toString();
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: SafeArea(
        child: Scaffold(
          body: Consumer<PaymentScreenViewModel>(
            builder: (context, data, _) {
              final shoppingCarVm = context.read<ShoppingCarProvider>();
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
                child: PaymentTab(
                  tabs: tabs,
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: 19.h,
                      left: 28.w,
                      right: 0.w,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ///左側選取類別列表
                        Padding(
                          padding: EdgeInsets.only(top: (138 + 19).h),
                          //這邊自己管控高度Padding
                          child: PaymentTapeSwitchArea(),
                        ),
                        SizedBox(width: 27.5.w),

                        ///訂單明細
                        Expanded(
                          child: Padding(
                            padding: EdgeInsets.only(top: 138.h),
                            //這邊自己管控高度Padding
                            child: Container(
                              color: Colors.white.withOpacity(0.5),
                              child: PaymentDetailsArea(controller: shoppingCarVm.discountController,),
                            ),
                          ),
                        ),

                        SizedBox(width: 17.w),

                        ///右側金額列表
                        PaymentListScreen(pageName: widget.pageName,),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
