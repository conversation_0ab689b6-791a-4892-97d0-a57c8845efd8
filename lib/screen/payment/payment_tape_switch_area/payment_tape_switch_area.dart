import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/shape/circle.dart';
import 'package:sbar_pos/widget/textField/my_text_field.dart';

import '../../../util/StringUtils.dart';
import '../../../widget/btn/switch_type_row.dart';
import '../../../widget/shopping/shopping_list.dart';

class PaymentTapeSwitchArea extends StatefulWidget {
  const PaymentTapeSwitchArea({super.key});

  @override
  State<PaymentTapeSwitchArea> createState() => _PaymentTapeSwitchAreaState();
}

class _PaymentTapeSwitchAreaState extends State<PaymentTapeSwitchArea> {
  int _selectedType = 1; //預設為商品
  TextEditingController noteController = TextEditingController();

  @override
  void initState() {
    _initListData();
    super.initState();
  }

  ///初始化顯示資料（第一個type若無資料則自動顯示有資料的type）
  void _initListData() {
    Future.microtask(() {
      final shoppingCarVm = context.read<ShoppingCarProvider>();
      final list = shoppingCarVm.shoppingCarListModel;

      int defaultType;
      if (list.any((e) => e.type == 1)) {
        defaultType = 1;
      } else if (list.any((e) => e.type == 2)) {
        defaultType = 2;
      } else {
        defaultType = 3;
      }

      setState(() {
        _selectedType = defaultType;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 844.5.w,
      child: Column(
        children: [
          ///上方切換type按鈕
          _switchTypeRow(),

          SizedBox(height: 15.h),

          ///商品列表
          Expanded(
            child: ShoppingList(
              padding: EdgeInsets.zero,
              w: 845.w,
              h: 128.h,
              type: _selectedType,
            ),
          ),

          SizedBox(height: 15.h),

          ///折扣列
          //TODO:從api判斷
          // _discountBar(),
          SizedBox(height: 19.h),

          ///按鈕列
          _btnRow(),
        ],
      ),
    );
  }

  ///按鈕列
  Widget _btnRow() {
    return Consumer<ShoppingCarProvider>(
      builder: (context, shoppingCarVm, _) {
        return Row(
          children: [
            _baseBtn(
              () {
                Navigator.of(context).pop(true);
              },
              '返回',
              MyColors.yellow_251_227_140,
              MyColors.brown_57_54_18,
            ),
            SizedBox(width: 10.w),
            _baseBtn(
              () {
                shoppingCarVm.clearShoppingCar();
              },
              '清空',
              MyColors.red_224_129_125,
              Colors.white,
            ),
            SizedBox(width: 10.w),

            Expanded(child: _note()),
          ],
        );
      },
    );
  }

  ///訂單備註
  Widget _note() {
    return ContainerWithRadius(
      w: 500.w,
      h: 120.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '訂單備註：',
              style: TextStyle(
                fontSize: 25.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(height: 8.h),
            MyTextField(
              isSingleLine: true,
              style: TextStyle(fontSize: 28.sp, color: MyColors.brown_57_54_18),
              borderColor: MyColors.grey_235_235_235,
              hint: '請輸入…',
              textAlign: TextAlign.start,
              enterTextPadding: EdgeInsets.only(left: 24.w),
              hintStyle: TextStyle(
                fontSize: 23.sp,
                color: MyColors.brown_57_54_18,
              ),
              controller: noteController,
              border: 20.r,
              height: 54.h,
            ),
          ],
        ),
      ),
    );
  }

  ///折扣列
  Widget _discountBar() {
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return ContainerWithRadius(
      w: 845.w,
      h: 78.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 20.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Circle(
              color: MyColors.yellow_251_227_140,
              size: 40.w,
              child: Icon(Icons.check, color: MyColors.green_121_131_90),
            ),
            SizedBox(width: 21.w),
            Text('已符合', style: textStyle),
            SizedBox(width: 21.w),
            Text('全單滿1000元，打9折', style: textStyle),
            Spacer(),
            Text('-\$ ${StringUtils.formatMoneyForDouble(210)}', style: textStyle),
          ],
        ),
      ),
    );
  }

  ///上方切換type按鈕
  Widget _switchTypeRow() {
    return Consumer<ShoppingCarProvider>(
      builder: (context, shoppingCarVm, _) {
        final list = shoppingCarVm.shoppingCarListModel;

        int count1 = list.where((e) => e.type == 1).length;
        int count2 = list.where((e) => e.type == 2).length;
        int count3 = list.where((e) => e.type == 3).length;

        // 自動更新 selectedType（若目前選擇的 type 沒資料了）
        if (_selectedType == 1 && count1 == 0) {
          if (count2 > 0) {
            _selectedType = 2;
          } else if (count3 > 0) {
            _selectedType = 3;
          }
        } else if (_selectedType == 2 && count2 == 0) {
          if (count1 > 0) {
            _selectedType = 1;
          } else if (count3 > 0) {
            _selectedType = 3;
          }
        } else if (_selectedType == 3 && count3 == 0) {
          if (count1 > 0) {
            _selectedType = 1;
          } else if (count2 > 0) {
            _selectedType = 2;
          }
        }

        return SwitchTypeRow(
          amount1: count1,
          amount2: count2,
          amount3: count3,
          selectedType: _selectedType,
          onChanged: (newType) {
            setState(() {
              _selectedType = newType;
            });
          },
        );
      },
    );
  }

  ///base btn
  Widget _baseBtn(
    VoidCallback onTap,
    String title,
    Color color,
    Color textColor,
  ) {
    return ContainerWithRadius(
      onTap: onTap,
      isHaveBorder: true,
      w: 138.w,
      h: 120.h,
      r: 20.r,
      color: color,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          title,
          style: TextStyle(color: textColor, fontSize: 25.sp),
        ),
      ),
    );
  }
}
