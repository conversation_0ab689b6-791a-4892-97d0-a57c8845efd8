import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/textField/base_text_field.dart';

import '../../../resource/MyColor.dart';
import '../../../util/StringUtils.dart';
import '../../../widget/btn/return_and_submit_btn.dart';
import '../../../widget/dialog/sbar_style_dialog.dart';
import '../../../widget/number_keyboard/number_keyboard.dart';
import '../../../widget/textField/my_text_field.dart';
import '../../start/my_app.dart';
import '../payment_screen_view_model.dart';

class PaymentDiscountScreen extends StatefulWidget {
  final PaymentScreenViewModel vm;

  const PaymentDiscountScreen({super.key, required this.vm});

  @override
  State<PaymentDiscountScreen> createState() => _PaymentDiscountScreenState();
}

class _PaymentDiscountScreenState extends State<PaymentDiscountScreen> {
  final int discountLimit = 65;
  int _selectedIndex = -1;
  int _originalDiscountRes = 0;
  late String _originalDiscountText;

  @override
  void initState() {
    _initTextFieldInput();
    super.initState();
  }

  /// 初始化輸入框並備份原始值
  void _initTextFieldInput() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final shoppingCarVm = context.read<ShoppingCarProvider>();

      _originalDiscountRes = shoppingCarVm.discountRes;
      _originalDiscountText = shoppingCarVm.discountRes != 0
          ? shoppingCarVm.discountRes.toString()
          : '';

      if (shoppingCarVm.discountRes != 0) {
        widget.vm.inputDiscountController.text = shoppingCarVm.discountRes.toString();
      } else {
        widget.vm.inputDiscountController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.vm,
      child: Consumer<PaymentScreenViewModel>(
        builder: (context, data, _) {
          final shoppingCarVm = context.read<ShoppingCarProvider>();

          return Scaffold(
            body: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 80.h),
                    child: SingleChildScrollView(
                      physics: BouncingScrollPhysics(),
                      padding: EdgeInsets.zero,
                      child: Column(
                        children: [
                          Text(
                            '現場折價',
                            style: TextStyle(
                              fontSize: 44.sp,
                              color: MyColors.brown_57_54_18,
                            ),
                          ),
                          SizedBox(height: 30.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 50.w),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                widget.vm.isChangeAccount
                                    ? _changeAccountArea()
                                    : _nowAccountArea(),
                                SizedBox(width: 80.w),
                                Container(
                                  width: 1110.w,
                                  height: 700.h,
                                  decoration: BoxDecoration(
                                    color: MyColors.grey_248_248_248,
                                    borderRadius: BorderRadius.circular(20.r),
                                    border: Border.all(
                                      color: MyColors.grey_235_235_235,
                                      width: 1,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20.r),
                                    child: Row(
                                      children: [
                                        _priceInfo(),
                                        _keyBoardArea(),
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                ReturnAndSubmitBtn(
                  h: 90.h,
                  backOnTap: () {
                    shoppingCarVm.setDiscountRes(_originalDiscountRes);
                    widget.vm.inputDiscountController.text = _originalDiscountText;
                    Navigator.of(context).pop();
                  },
                  submitOnTap: () {
                    if (shoppingCarVm.discountRes > 99) {
                      sbarStyleDialog(
                        context: context,
                        yesAction: () => navigatorKey.currentState?.pop(),
                        title: '提醒您！',
                        content: '輸入折數不得超過兩位數。',
                      );
                      return;
                    }

                    if (shoppingCarVm.discountRes != 0 && shoppingCarVm.discountRes < discountLimit) {
                      print('shoppingCarVm.discountRes: ${shoppingCarVm.discountRes}');
                      sbarStyleDialog(
                        context: context,
                        yesAction: () => navigatorKey.currentState?.pop(),
                        title: '提醒您！',
                        content: '輸入折數大於您的最高權限上限。',
                      );
                      return;
                    }

                    Navigator.of(context).pop();
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }



  ///快速折扣
  //UI V.1
  Widget _speedDiscount() {
    return Container(
      width: (1080 / 2).w,
      height: 750.h,
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.only(top: 50.h, right: 50.w, left: 50.w),
        child: Column(
          children: [
            Text(
              '請點選以下快速鍵，或使用左邊手動輸入',
              style: TextStyle(
                fontSize: 20.sp,
                color: MyColors.brown_57_54_18,
                letterSpacing: 1.5,
              ),
            ),
            SizedBox(height: 100.h),

            discountList(),
          ],
        ),
      ),
    );
  }

  ///金額資訊
  // UI V.2
  Widget _priceInfo() {
    final textStyle = TextStyle(
      fontSize: 34.sp,
      color: MyColors.green_121_131_90,
      letterSpacing: 1.5,
    );
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return Container(
      width: (1080 / 2).w,
      height: 700.h,
      color: Colors.white,
      child: Padding(
        padding: EdgeInsets.only(top: 50.h, right: 50.w, left: 50.w),
        child: Column(
          children: [
            Text(
              '請在右邊手動輸入折數',
              style: TextStyle(
                fontSize: 24.sp,
                color: MyColors.green_121_131_90,
                letterSpacing: 1.5,
              ),
            ),
            SizedBox(height: 100.h),

            //訂單金額
            _basePriceRow(
              title: '訂單金額',
              textStyle: textStyle,
              isMinus: false,
              price: shoppingCarVm.getTotalAmount(),
            ),

            SizedBox(height: 50.h),

            //折價金額
            _basePriceRow(
              title: '折價金額',
              textStyle: textStyle.copyWith(color: MyColors.red_255_91_110),
              isMinus: true,
              price: shoppingCarVm.getDiscountAmount(),
            ),

            SizedBox(height: 50.h),

            Divider(height: 1, thickness: 1),
            SizedBox(height: 50.h),

            //折價後訂單金額
            _basePriceRow(
              title: '折價後訂單金額',
              textStyle: textStyle.copyWith(
                fontSize: 36.sp,
                color: MyColors.brown_57_54_18,
              ),
              isMinus: false,
              price: shoppingCarVm.getDiscountPrice(),
            ),
          ],
        ),
      ),
    );
  }

  ///base price row
  Widget _basePriceRow({
    required String title,
    required TextStyle textStyle,
    required bool isMinus,
    required double price,
  }) {
    final titleTextStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_121_131_90,
      letterSpacing: 1.5,
    );

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: titleTextStyle),
        isMinus
            ? Text('-${StringUtils.formatMoneyForDouble(price)}', style: textStyle)
            : Text(StringUtils.formatMoneyForDouble(price), style: textStyle),
      ],
    );
  }

  ///折數列表
  Widget discountList() {
    return GridView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      physics: const BouncingScrollPhysics(),
      itemCount: widget.vm.speedDiscountModel.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 40.w,
        mainAxisSpacing: 40.h,
        childAspectRatio: 80 / 40,
      ),
      itemBuilder: (context, index) {
        final model = widget.vm.speedDiscountModel[index];
        return discountListWidget(model, index);
      },
    );
  }

  ///折數顯示邏輯
  String displayDiscount(int model) {
    if (model % 10 == 0) {
      return '${model ~/ 10}折'; // 整除去掉0
    }
    return '$model折';
  }

  ///折數列表元件
  Widget discountListWidget(int model, int index) {
    final bool isSelected = index == _selectedIndex;
    final isPermissionDenied = model < discountLimit; //沒有折扣權限
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return ContainerWithRadius(
      onTap: isPermissionDenied
          ? null
          : () {
              setState(() {
                _selectedIndex = index;

                widget.vm.inputDiscountController.text = model.toString();

                shoppingCarVm.setDiscountRes(model);
              });
            },
      borderColor: isPermissionDenied
          ? MyColors.grey_235_235_235.withOpacity(0.5)
          : MyColors.grey_235_235_235,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      w: 80.w,
      h: 40.h,
      r: 20.r,
      color: isSelected ? MyColors.green_157_193_65 : Colors.white,
      isHaveBorder: true,
      child: Center(
        child: Text(
          displayDiscount(model),
          style: TextStyle(
            fontSize: 28.sp,
            color: isPermissionDenied
                ? MyColors.grey_219_219_219
                : isSelected
                ? Colors.white
                : MyColors.brown_57_54_18,
          ),
        ),
      ),
    );
  }

  ///鍵盤區塊
  Widget _keyBoardArea() {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return SizedBox(
      width: (1120 / 2).w,
      height: 700.h,
      child: Padding(
        padding: EdgeInsets.only(top: 50.h),
        child: Column(
          children: [
            BaseTextField(
              isReadOnly: true,
              w: 350.w,
              hint: '請輸入折數',
              controller: widget.vm.inputDiscountController,
            ),
            SizedBox(height: 25.h),
            Text(
              '打95折請輸入95；打8折請輸入80',
              style: TextStyle(
                fontSize: 20.sp,
                color: MyColors.green_121_131_90,
                letterSpacing: 1.5,
              ),
            ),
            SizedBox(height: 25.h),
            Padding(
              padding: EdgeInsets.only(left: 70.w),
              child: NumberKeyboard(
                onValueChanged: (String value) {
                  //將快速折扣選擇清除
                  _selectedIndex = -1;

                  // 轉成 int，再補 0（如果只有一位）
                  final intValue = int.tryParse(value) ?? 0;
                  final fixedValue = intValue < 10 && value.length == 1
                      ? intValue * 10
                      : intValue;

                  // 更新輸入框內容
                  widget.vm.inputDiscountController.text = fixedValue
                      .toString();

                  // 更新 ViewModel 折扣值
                  shoppingCarVm.setDiscountRes(fixedValue);
                  setState(() {});
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///目前帳號區塊
  Widget _nowAccountArea() {
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
    );
    return Column(
      children: [
        Text('目前帳號', style: textStyle.copyWith(fontWeight: FontWeight.w600)),
        SizedBox(height: 50.h),

        SizedBox(
          width: 500.w,
          height: 1.h,
          child: Divider(thickness: 1, height: 1),
        ),

        SizedBox(height: 50.h),

        //TODO:從db撈名字
        Text('Tim', style: textStyle),
        SizedBox(height: 20.h),

        //TODO:從db撈高權限折數
        Text(
          '您的整單最高權限折數為「$discountLimit折」',
          style: TextStyle(
            fontSize: 20.sp,
            color: MyColors.green_121_131_90,
            letterSpacing: 1.5,
          ),
        ),
        SizedBox(height: 20.h),

        //切換帳號按鈕
        ContainerWithRadius(
          onTap: () {
            widget.vm.isChangeAccount = true;
          },
          boxShadow: [
            BoxShadow(
              color: const Color(0x14000000),
              offset: const Offset(3, 6),
              blurRadius: 10.r,
            ),
          ],
          w: 500.w,
          h: 70.h,
          r: 20.r,
          isHaveBorder: true,
          color: Colors.white,
          child: Center(
            child: Text(
              '切換帳號',
              style: textStyle.copyWith(
                fontSize: 24.sp,
                color: MyColors.green_121_131_90, fontWeight: FontWeight.w600
              ),
            ),
          ),
        ),
      ],
    );
  }

  ///切換帳號區塊
  Widget _changeAccountArea() {
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.brown_57_54_18,
    );
    return Column(
      children: [
        Text('切換帳號', style: textStyle),
        SizedBox(height: 50.h),

        SizedBox(
          width: 500.w,
          height: 1.h,
          child: Divider(thickness: 1, height: 1),
        ),

        SizedBox(height: 50.h),

        //帳號
        _accountArea(),
        SizedBox(height: 17.h),

        //密碼
        _pwdArea(),
        SizedBox(height: 40.h),

        //取消、登入
        Selector<PaymentScreenViewModel, bool>(
          selector: (context, vm) => vm.loading,
          builder: (context, loading, _) {
            return AnimatedSwitcher(
              duration: 300.ms,
              child: !loading
                  ? Row(
                      children: [
                        _baseBtn(
                          w: (200 - 20).w,
                          onTap: () {
                            widget.vm.isChangeAccount = false;
                          },
                          title: '取消',
                        ),
                        SizedBox(width: 20.w),
                        _baseBtn(
                          w: (300 - 20).w,
                          onTap: () {
                            widget.vm.loginLogic(context, () {});
                          },
                          title: '登入',
                        ),
                      ],
                    )
                  : Center(
                      key: ValueKey('loading'),
                      child: CircularProgressIndicator(
                        color: MyColors.green_67_160_71,
                      ),
                    ),
            );
          },
        ),
      ],
    );
  }

  ///帳號區塊
  Widget _accountArea() {
    return Row(
          children: [
            Text(
              '帳號：',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(width: 17.w),
            SizedBox(
              width: 400.w,
              child: MyTextField(
                isSingleLine: true,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
                borderColor: MyColors.grey_235_235_235,
                hint: '請輸入帳號',
                textAlign: TextAlign.start,
                enterTextPadding: EdgeInsets.only(left: 24.w),
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.grey_164_164_164,
                ),
                controller: widget.vm.accountController,
                border: 20.r,
                height: 74.h,
              ),
            ),
          ],
        )
        .animate(
          autoPlay: false,
          onInit: (controller) {
            widget.vm.animationAccountController = controller;
          },
        )
        .shakeX(duration: const Duration(milliseconds: 500));
  }

  ///密碼區塊
  Widget _pwdArea() {
    return Row(
          children: [
            Text(
              '密碼：',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
            SizedBox(width: 17.w),

            SizedBox(
              width: 400.w,
              child: MyTextField(
                isSingleLine: true,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.green_129_128_94,
                  letterSpacing: 1.5,
                ),
                obscureText: true,

                borderColor: MyColors.grey_235_235_235,
                hint: '請輸入密碼',
                textAlign: TextAlign.start,
                enterTextPadding: EdgeInsets.only(left: 24.w),
                hintStyle: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.grey_164_164_164,
                ),
                controller: widget.vm.pwdController,
                border: 20.r,
                height: 74.h,
              ),
            ),
          ],
        )
        .animate(
          autoPlay: false,
          onInit: (controller) {
            widget.vm.animationPwdController = controller;
          },
        )
        .shakeX(duration: const Duration(milliseconds: 500));
  }

  ///base btn
  Widget _baseBtn({
    required double w,
    required VoidCallback onTap,
    required String title,
  }) {
    return ContainerWithRadius(
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      onTap: onTap,
      w: w,
      h: 70.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Center(
        child: Text(
          title,
          style: TextStyle(fontSize: 20.sp, color: MyColors.grey_164_164_164),
        ),
      ),
    );
  }
}
