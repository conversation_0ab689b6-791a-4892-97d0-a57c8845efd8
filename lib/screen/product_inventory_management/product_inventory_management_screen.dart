import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/screen/product_inventory_management/product_inventory_management_screen_view_model.dart';

import '../../gen/r.dart';
import '../../model/product_inventory_management/product_inventory_management_model.dart';
import '../../model/widget_model/drop_down_model.dart';
import '../../resource/MyColor.dart';
import '../../util/MyRoutes.dart';
import '../../widget/base_title_list_bar/inventory_management_base_list_bar.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/drop_down/drop_down_button.dart';
import '../../widget/hint/empty_hint.dart';
import '../../widget/scanner/scanner.dart';
import '../../widget/switch_account/switch_account_dialog.dart';
import '../../widget/switch_account/switch_account_dialog_screen.dart';
import '../../widget/textField/base_text_field.dart';
import '../main/drawer/base_scaffold_with_drawer.dart';
import '../scan/scan_screen.dart';
import '../start/my_app.dart';

class ProductInventoryManagementScreen extends StatefulWidget {
  const ProductInventoryManagementScreen({super.key});

  @override
  State<ProductInventoryManagementScreen> createState() =>
      _ProductInventoryManagementScreenState();
}

class _ProductInventoryManagementScreenState
    extends State<ProductInventoryManagementScreen> {
  final vm = ProductInventoryManagementScreenViewModel();

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: BaseScaffoldWithDrawer(
        ///帳號列
        tailWidget: _accountArea(),
        title: '商品庫存管理',
        child: Padding(
          padding: EdgeInsets.only(
            top: 140.h,
            left: 16.w,
            right: 23.w,
            bottom: 23.h,
          ),
          child: Column(
            children: [
              ///搜尋列
              _searchBar(),

              SizedBox(height: 17.h),

              ///商品庫管理列表
              Expanded(child: _inventoryManagementArea()),
            ],
          ),
        ),
      ),
    );
  }

  //帳號列
  Widget _accountArea() {
    return Selector<ProductInventoryManagementScreenViewModel, bool>(
      selector: (context, vm) => vm.isSwitchAccount,
      builder: (context, isSwitchAccount, _) {
        return Padding(
          padding: EdgeInsets.only(right: 23.w),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '帳號： ',
                style: TextStyle(
                  fontSize: 26.sp,
                  color: MyColors.green_129_128_94,
                ),
              ),
              Text(
                'Tim',
                style: TextStyle(
                  fontSize: 34.sp,
                  color: MyColors.brown_57_54_18,
                ),
              ),
              SizedBox(width: 20.w),
              ContainerWithRadius(
                onTap: () {
                  if (isSwitchAccount) {
                    //TODO:登出邏輯
                    vm.setSwitchAccount(false);

                    return;
                  }
                  //切換帳號
                  switchAccountDialog(context: context, vm: vm);
                },
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x14000000),
                    offset: const Offset(3, 6),
                    blurRadius: 10.r,
                  ),
                ],
                w: 200.w,
                h: 50.h,
                r: 20.r,
                color: isSwitchAccount ? MyColors.red_230_75_75 : Colors.white,
                isHaveBorder: true,
                child: Center(
                  child: isSwitchAccount
                      ? Text(
                          '登出',
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: Colors.white,
                          ),
                        )
                      : Text(
                          '切換帳號',
                          style: TextStyle(
                            fontSize: 24.sp,
                            color: MyColors.green_129_128_94,
                          ),
                        ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  //商品庫管理列表區塊
  Widget _inventoryManagementArea() {
    return Column(
      children: [
        _performanceTitleBar(),
        Expanded(child: _performanceList()),
      ],
    );
  }

  //商品庫管理列表列表
  Widget _performanceList() {
    return Selector<
      ProductInventoryManagementScreenViewModel,
      List<ProductInventoryManagementModel>
    >(
      selector: (context, vm) => vm.productInventoryManagementModel,
      builder: (context, productInventoryManagementModel, _) {

        return SmartRefresher(
          controller: vm.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            vm.refreshController.refreshCompleted();
          },
          child: productInventoryManagementModel.isEmpty
              ? ListView(
                  // 用 ListView 包住 EmptyHint，這樣才能下拉
                  children: [
                    SizedBox(
                      height:
                          MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
                    ),
                    Center(child: EmptyHint(hint: '尚無商品庫管理紀錄')),
                  ],
                )
              : ListView.builder(
                  padding: EdgeInsets.only(bottom: 50.h),
                  physics: BouncingScrollPhysics(),
                  itemCount: productInventoryManagementModel.length,
                  itemBuilder: (context, index) {
                    final model = productInventoryManagementModel[index];
                    return InventoryManagementBaseListBar(
                      isTitleBar: false,

                      backgroundColor: Colors.white,
                      shopName: model.shopName,
                      productType: model.productType,
                      productName: model.productName,
                      countryCode: model.countryCode,
                      count: model.count,
                      countTitle: '',
                    );
                  },
                ),
        );
      },
    );
  }

  ///商品庫管理列表標頭
  Widget _performanceTitleBar() {
    return InventoryManagementBaseListBar(
      isTitleBar: true,
      backgroundColor: MyColors.grey_247_247_247,
      shopName: '營業店編號',
      productType: '商品類別',
      productName: '商品名稱',
      countryCode: '國際條碼',
      count: 0,
      countTitle: '庫存數量',
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(right: 12.w, left: 25.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ///類別（下拉選單輸入框）
            _typeDropDown(),
            SizedBox(width: 22.w),

            ///商品名稱輸入框
            BaseTextField(
              w: 312.w,
              hint: '請輸入商品名稱',
              controller: vm.productNameController,
            ),
            SizedBox(width: 25.w),

            ///國際條碼輸入框
            BaseTextField(
              w: 250.w,
              hint: '請輸入國際條碼',
              controller: vm.countryCodeController,
              tailWidgetOnTap: () {
                runBarCodeScannerCamForOrder();
              },
              tailWidget: Icon(
                Icons.fit_screen_outlined,
                color: Colors.lightGreen,
              ),
              tailWidgetPadding: EdgeInsets.only(right: 12.w),
            ),
            SizedBox(width: 14.w),

            ///搜尋按鈕
            ContainerWithRadius(
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///類別下拉
  Widget _typeDropDown() {
    return Selector<ProductInventoryManagementScreenViewModel, DropDownModel?>(
      selector: (context, vm) => vm.typeDropDownModel,
      shouldRebuild: (p, n) => true,
      builder: (context, typeDropDownModel, _) {
        return ContainerWithRadius(
          w: 312.w,
          h: 54.h,
          r: 20.r,
          color: Colors.white,
          isHaveBorder: true,
          child: Padding(
            padding: EdgeInsets.only(
              left: 15.w,
              right: 8.w,
              top: 7.h,
              bottom: 7.h,
            ),
            child: MyDropdownButtonT<DropDownModel>(
              tailWidget: Image(
                image: R.image.icon_arrow_down_with_circle(),
                width: 40.w,
                height: 40.w,
              ),

              isExpanded: false,
              underline: SizedBox.shrink(),
              hint: Text(
                '請選擇類型',
                style: TextStyle(
                  fontSize: 23.sp,
                  color: MyColors.grey_60_60_67.withOpacity(0.3),
                ),
              ),
              textStyle: TextStyle(
                fontSize: 23.sp,
                color: MyColors.brown_57_54_18,
              ),
              selectedItem: vm.typeDropDownModel,
              labelSelector: (item) {
                return item.name;
              },
              items: vm.typedDropDownList,
              onChanged: (DropDownModel? value) async {
                vm.typeDropDownModel = value;

                (context as Element).markNeedsBuild();
              },
            ),
          ),
        );
      },
    );
  }
}
