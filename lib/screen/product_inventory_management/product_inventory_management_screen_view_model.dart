import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../model/product_inventory_management/product_inventory_management_model.dart';
import '../../model/widget_model/drop_down_model.dart';

class ProductInventoryManagementScreenViewModel with ChangeNotifier {
  RefreshController refreshController = RefreshController();
  TextEditingController accountController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  TextEditingController productNameController = TextEditingController(); //商品名稱
  TextEditingController countryCodeController = TextEditingController(); //國際條碼

  AnimationController? animationAccountController;
  AnimationController? animationPwdController;


  bool _loading = false;

  bool get loading => _loading;

  set loading(bool value) {
    _loading = value;
    notifyListeners();
  }


  bool _isSwitchAccount = false;


  bool get isSwitchAccount => _isSwitchAccount;

  set isSwitchAccount(bool value) {
    _isSwitchAccount = value;
    notifyListeners();
  }

  // void toggleSwitchAccount() {
  //   _isSwitchAccount = !_isSwitchAccount;
  //   notifyListeners();
  // }

  void setSwitchAccount(bool value) {
    _isSwitchAccount = value;
    notifyListeners();
  }

  //下拉
  DropDownModel? typeDropDownModel; //類別下拉model

  //-----|dropDown|-----
  //類別下拉資料
  List<DropDownModel> typedDropDownList = [
    DropDownModel(id: 0, code: '', name: 'Taipei C3店'),
    DropDownModel(id: 1, code: '', name: 'Taipei C2店'),
    DropDownModel(id: 2, code: '', name: 'Taipei C1店'),
  ];

  //庫存資料
  List<ProductInventoryManagementModel> productInventoryManagementModel = [
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 100,
    ),
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 100,
    ),
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 100,
    ),
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 100,
    ),
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 0,
    ),
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 100,
    ),
    ProductInventoryManagementModel(
      shopName: 'Taipei C3店',
      productType: '商品類別'*10,
      productName: '商品名稱'*10,
      countryCode: '************',
      count: 100,
    ),

  ];

  ///登入邏輯
  void loginLogic(BuildContext context, Function()? yesAction) async {
    if (loading) return;

    //帳號為空處理
    if (accountController.text.isEmpty) {
      animationAccountController?.reset();
      animationAccountController?.forward();
      return;
    }

    //密碼為空處理
    if (pwdController.text.isEmpty) {
      animationPwdController?.reset();
      animationPwdController?.forward();
      return;
    }
    loading = true;

    //TODO:api（這邊不把會員寫進DB）
    await Future.delayed(500.ms);
    isSwitchAccount = true;
    Navigator.of(context).pop();
  }
}
