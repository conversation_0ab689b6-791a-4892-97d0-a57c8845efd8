import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../api/api_service.dart';
import '../../hive/hive_provider/hive_user_provider.dart';
import '../../model/api_model/res_model/res_member_profile.dart';
import '../../model/member/member_list_model.dart';
import '../../widget/dialog/sbar_style_dialog.dart';
import '../start/my_app.dart';

class BrandMemberScreenViewModel with ChangeNotifier{
  RefreshController refreshController = RefreshController();
  TextEditingController nameController = TextEditingController();  //姓名Controller
  TextEditingController phoneController = TextEditingController();  //手機Controller
  TextEditingController mailController = TextEditingController();  //mail Controller
  TextEditingController birthdayController = TextEditingController();  //生日Controller


  //-----|日期|-----
  DateTime selectBirthdayDate = DateTime.now();


  ///會員列表
  List<MemberListModel> memberListModel = [
    MemberListModel(avatar: '', name: '王小明', phone: '**********', mail: '<EMAIL>', birthday: '1995-09-08'),
    MemberListModel(avatar: '', name: '陸小乖', phone: '**********', mail: '<EMAIL>', birthday: '2002-01-28'),
    MemberListModel(avatar: '', name: '顏正明', phone: '**********', mail: '<EMAIL>', birthday: '1973-12-15'),
  ];

// ///api品牌會員
//  Future<void> _apiGetMemberProfile(BuildContext context)async{
//
//    showLoadingOverlay();
//    var user = await HiveUserProvider.getUserOrNull();
//    final token = 'Bearer ${user?.memberToken}';
//    memberListModel = [];
//    final res = await ApiService.create().getMemberProfile(token);
//    if(res.isSuccessful){
//      ResMemberProfile resMemberProfile = ResMemberProfile.fromJson(jsonDecode(res.bodyString));
//      if(resMemberProfile.success ?? false){
//        dismissLoadingOverlay();
//        resMemberProfile.data.
//      }
//      if(!(resMemberProfile.success ?? false)){
//        dismissLoadingOverlay();
//        sbarStyleDialog(
//          context: context,
//          yesAction: () {
//            navigatorKey.currentState?.pop();
//          },
//          title: '錯誤',
//          content: resMemberProfile.message ?? '',
//        );
//      }
//    }
//    else {
//      if (context.mounted) {
//        dismissLoadingOverlay();
//        sbarStyleDialog(
//          context: context,
//          yesAction: () {
//            navigatorKey.currentState?.pop();
//          },
//          title: '錯誤',
//          content: 'api connection error',
//        );
//      }
//
//      return;
//    }
//
//  }


}