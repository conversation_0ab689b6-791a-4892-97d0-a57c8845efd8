import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import 'package:sbar_pos/widget/app_bar/base_app_bar.dart';
import 'package:sbar_pos/widget/hint/empty_hint.dart';

import '../../gen/r.dart';
import '../../model/member/member_list_model.dart';
import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../util/MyRoutes.dart';
import '../../widget/app_bar/sbar_app_bar_with_drawer.dart';
import '../../widget/base_title_list_bar/member_list_widget.dart';
import '../../widget/base_title_list_bar/member_title_bar.dart';
import '../../widget/btn/drawer_btn.dart';
import '../../widget/btn/scan_btn.dart';
import '../../widget/cached_network_image/error_cached_network_image.dart';
import '../../widget/calender/build_single_date_calender.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/textField/base_text_field.dart';
import '../../widget/textField/my_text_field.dart';
import '../main/drawer/base_scaffold_with_drawer.dart';
import '../main/drawer/drawer_screen.dart';
import '../member/member_info/member_info_main_screen.dart';
import '../start/my_app.dart';
import 'brand_member_screen_view_model.dart';

class BrandMemberScreen extends StatefulWidget {
  const BrandMemberScreen({super.key});

  @override
  State<BrandMemberScreen> createState() => _BrandMemberScreenState();
}

class _BrandMemberScreenState extends State<BrandMemberScreen> {
  final vm = BrandMemberScreenViewModel();

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: BaseScaffoldWithDrawer(
        title: '品牌會員',
        child: Padding(
          padding: EdgeInsets.only(
            top: 142.h,
            left: 19.w,
            right: 76.w,
            bottom: 19.h,
          ),
          child: Column(
            children: [
              ///搜尋列
              _searchBar(),

              SizedBox(height: 17.h),

              ///會員列表
              Expanded(child: _memberArea()),
            ],
          ),
        ),
      ),
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(left: 500.w, right: 9.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ///姓名輸入框
            BaseTextField(hint: '請輸入姓名', controller: vm.nameController),
            SizedBox(width: 10.w),

            ///手機輸入框
            BaseTextField(hint: '請輸入手機', controller: vm.phoneController),
            SizedBox(width: 10.w),

            ///mail輸入框
            BaseTextField(hint: '請輸入E-mail', controller: vm.mailController),
            SizedBox(width: 18.w),

            ///生日輸入框
            BaseTextField(
              hint: '請選擇生日',
              controller: vm.birthdayController,
              w: 225.w,
              leadWidget: Image(image: R.image.icon_calendar(),width: 36.w,height: 40.h,),
              onTap: () {
                //開啟生日日曆
                buildSingleDateCalender(
                  context: context,
                  onDaySelected: (selectDate, date, events) {
                    vm.selectBirthdayDate = selectDate;
                  },
                  onSubmit: () {
                    if (vm.birthdayController.text.isEmpty) {
                      vm.birthdayController.text = DateTime.now()
                          .toDateYMDString()
                          .replaceAll('-', '/');
                    }

                    vm.birthdayController.text = vm.selectBirthdayDate
                        .toDateYMDString()
                        .replaceAll('-', '/');
                    navigatorKey.currentState?.pop();
                  },
                  initDate: vm.selectBirthdayDate,
                );
              },
              enterTextPadding: EdgeInsets.only(left: 12.w),
              isReadOnly: true,
            ),

            SizedBox(width: 20.w),

            // ///掃碼按鈕
            // ScanBtn(onTap: () {  },),
            //
            // SizedBox(width: 20.w),

            ///搜尋按鈕
            ContainerWithRadius(
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///會員名單區塊
  Widget _memberArea() {

    return Column(
      children: [

        ///會員列表標頭
        Container(
          height: 76.h,
          width: double.infinity.w,
          decoration: BoxDecoration(
            color: MyColors.grey_247_247_247,
            border: Border.all(color: MyColors.grey_235_235_235, width: 1),
          ),
          child: MemberTitleBar(),
        ),

        ///會員列表
        Expanded(child: _memberList()),
      ],
    );
  }




  ///會員列表
  Widget _memberList() {
    return Selector<BrandMemberScreenViewModel, List<MemberListModel>>(
      selector: (context, vm) => vm.memberListModel,
      builder: (context, memberListModel, _) {
        return SmartRefresher(
          controller: vm.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            vm.refreshController.refreshCompleted();
          },
          child: memberListModel.isEmpty
              ? ListView( // 用 ListView 包住 EmptyHint，這樣才能下拉
            children:  [
              SizedBox(
                height: MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
              ),
              Center(child: EmptyHint(hint: '尚無品牌會員')),
            ],
          )
              : ListView.builder(
            padding: EdgeInsets.zero,
            physics: const BouncingScrollPhysics(),
            itemCount: memberListModel.length,
            itemBuilder: (context, index) {
              final model = memberListModel[index];
              return MemberListWidget(
                model: model,
                onTap: () {
                  final vm = context.read<ShoppingCarProvider>();
                  // 先更新會員資料
                  vm.setMemberInfo(
                    avatar: model.avatar,
                    name: model.name,
                    phone: model.phone,
                    birthday: model.birthday,
                    email: model.mail,
                  );

                  // 導向到基本資料頁
                  pushTo(
                    PageName.memberInfoMain.toString(),
                    builder: (context) => MemberInfoMainScreen(initIndex: 0),
                  );
                },
                isSearchMemberScreen: false,
                index: index,
              );
            },
          ),
        );
      },
    );
  }

}
