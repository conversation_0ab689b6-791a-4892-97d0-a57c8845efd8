import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/model/base/base_model.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/screen/product/product_screen_view_model.dart';
import 'package:sbar_pos/widget/dialog/MyDialog.dart';

import '../../model/product/product_model.dart';
import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/shape/circle.dart';
import '../search_member/search_member_screen.dart';
import '../search_product/search_product_screen.dart';
import '../shopping_car/shopping_car_screen.dart';

class ProductScreen extends StatefulWidget {
  // final bool isSearchProduct; //是否從搜尋商品tab點擊（需開啟Dialog）
  const ProductScreen({super.key});

  @override
  State<ProductScreen> createState() => _ProductScreenState();
}

class _ProductScreenState extends State<ProductScreen> {
  final vm = ProductScreenViewModel();
  // int _selectedIndex = 0;
  RefreshController controller = RefreshController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: Padding(
        padding: EdgeInsets.only(bottom: 15.h, left: 16.w),
        child: Consumer<ShoppingCarProvider>(
          builder: (context, data, _) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ///左側商品類別列表
                Padding(
                  padding: EdgeInsets.only(top: 138.h), //這邊自己管控高度Padding
                  child: _productTypeList(),
                ),

                ///主商品列表
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(top: 138.h), //這邊自己管控高度Padding
                    child: _productList(),
                  ),
                ),

                SizedBox(width: 17.w),

                ///右側購物車列表
                ShoppingCarScreen(),
              ],
            );
          },
        ),
      ),
    );
  }

  ///左側商品類別列表
  Widget _productTypeList() {
    return SizedBox(
      width: 196.w,
      child: Selector<ShoppingCarProvider, List<BaseModel>>(
        selector: (context, vm) => vm.productType,
        builder: (context, productType, _) {
          return ListView.separated(
            padding: EdgeInsets.zero,
            physics: BouncingScrollPhysics(),
            itemCount: productType.length,
            itemBuilder: (context, index) {
              final model = productType[index];
              return _productTypeListWidget(model, index);
            },
            separatorBuilder: (context, index) => SizedBox(height: 19.h),
          );
        },
      ),
    );
  }

  ///商品類別列表元件
  Widget _productTypeListWidget(BaseModel model, int index) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    final bool isSelected = index == shoppingCarVm.productSelectedTypeIndex;

    return ContainerWithRadius(
      isHaveBorder: true,
      onTap: () async {
          shoppingCarVm.selectProductType(index);

        // 重置分頁資訊
        shoppingCarVm.currentProductPage = 1;
        shoppingCarVm.hasMoreProduct = true;

        await shoppingCarVm.apiGetProduct(
          context,
          productTypeName: shoppingCarVm.productType[shoppingCarVm.productSelectedTypeIndex].title,
          clearData: true,
          isHaveLoading: true,
        ); //取得商品列表
      },
      w: 196.w,
      h: 62.h,
      r: 20.r,
      color: isSelected ? MyColors.green_157_193_65 : Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          model.title,
          style: TextStyle(
            fontSize: 25.sp,
            color: isSelected ? Colors.white : MyColors.green_129_128_94,
          ),
        ),
      ),
    );
  }

  ///主商品列表
  Widget _productList() {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.productModel,
      builder: (context, productModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();

        return shoppingCarVm.mainLoading ? SizedBox.shrink() : SmartRefresher(
                enablePullDown: true,
                enablePullUp: true,
                controller: controller,
                onRefresh: () async {
                  try {
                    await shoppingCarVm.apiProductPullDown(
                      context,
                      productTypeName: shoppingCarVm.productType[shoppingCarVm.productSelectedTypeIndex].title,
                    );
                  } finally {
                    controller.refreshCompleted();
                  }
                },
                onLoading: () async {
                  try {
                    await shoppingCarVm.apiProductLoading(
                      context,
                      productTypeName: shoppingCarVm.productType[shoppingCarVm.productSelectedTypeIndex].title,
                    ); // 上拉加載更多
                  } finally {
                    if (shoppingCarVm.hasMoreProgram) {
                      controller.loadComplete(); // 還有更多資料
                    } else {
                      controller.loadNoData(); // 沒有更多資料
                    }
                  }
                },
                child: GridView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(bottom: 42.h, left: 27.w),
                  physics: const BouncingScrollPhysics(),
                  itemCount: productModel.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    crossAxisSpacing: 20.w,
                    mainAxisSpacing: 20.h,
                    childAspectRatio: 286 / 188,
                  ),
                  itemBuilder: (context, index) {
                    final model = productModel[index];
                    return _productListWidget(model);
                  },
                ),
              );
      },
    );
  }


  /// 商品列表元件
  Widget _productListWidget(ProductModel model) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();

    // 判斷是否已選取
    final isSelected = shoppingCarVm.shoppingCarListModel.any(
          (e) => e.productId == model.productId,
    );

    // 取得選取數量
    final selectAmount = shoppingCarVm.shoppingCarListModel
        .firstWhere(
          (e) => e.productId == model.productId,
      orElse: () => ProductModel(
        productId: model.productId,
        title: '',
        price: 0,
        selectAmount: 0,
        totalAmount: 0,
        type: 1,
        isSelect: false,
        isTempSelection: false,
      ),
    )
        .selectAmount;

    return InkWell(
      onTap: (model.totalAmount <= 0)
          ? null
          : () {
        /// 如果購物車已經有這個商品，則刪除或新增
        if (isSelected) {
          shoppingCarVm.removedShoppingCarByProductId(
            productId: model.productId,
          );
        } else {
          shoppingCarVm.addToShoppingCarByProductId(
            productId: model.productId,
          );
        }
      },
      child: Container(
        width: 286.w,
        height: 188.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(3, 6),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            /// 商品名稱
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 20.h),
              child: Text(
                model.title,
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                ),
              ),
            ),

            /// 價格文字 (依 isSelected 判斷偏移)
            Positioned(
              left: isSelected ? 75.w : 19.w,
              bottom: 17.h,
              child: Text(
                '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
                style: TextStyle(
                  fontSize: 25.sp,
                  color: MyColors.grey_119_119_119,
                ),
              ),
            ),

            /// 右下角半圓角區塊（顯示剩餘數量 or 售完）
            Align(
              alignment: Alignment.bottomRight,
              child: Container(
                width: 76.w,
                height: 54.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.r),
                    bottomRight: Radius.circular(20.r),
                  ),
                ),
                child: model.totalAmount <= 0
                    ? Center(
                  child: Text(
                    '售完',
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.red_230_75_75,
                    ),
                  ),
                )
                    : Center(
                  child: Text(
                    model.totalAmount.toString(),
                    style: TextStyle(
                      fontSize: 20.sp,
                      color: MyColors.grey_119_119_119,
                    ),
                  ),
                ),
              ),
            ),

            /// 左下選取數量 (依 isSelected 顯示)
            if (isSelected)
              Positioned(
                bottom: -3.h,
                left: -5.w,
                child: Circle(
                  color: MyColors.yellow_251_227_140,
                  size: 63.w,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x669DC141),
                      offset: Offset(3, 6),
                      blurRadius: 10,
                    ),
                  ],
                  child: Center(
                    child: Text(
                      selectAmount.toString(),
                      style: TextStyle(
                        color: MyColors.green_129_128_94,
                        fontSize: 25.sp,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }



}
