
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';

import '../../model/performance/performance_model.dart';
import '../../resource/MyColor.dart';
import '../../widget/base_title_list_bar/performance_personal_base_list_bar.dart';
import '../../widget/btn/return_and_submit_btn.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/hint/empty_hint.dart';
import '../../widget/shape/circle.dart';
import '../../widget/textField/base_text_field.dart';
import '../../widget/textField/my_text_field.dart';
import 'choose_performance_personnel_screen_view_model.dart';

class ChoosePerformancePersonnelScreen extends StatefulWidget {
  const ChoosePerformancePersonnelScreen({
    super.key,
  });

  @override
  State<ChoosePerformancePersonnelScreen> createState() =>
      _ChoosePerformancePersonnelScreenState();
}

class _ChoosePerformancePersonnelScreenState
    extends State<ChoosePerformancePersonnelScreen> {
  final vm = ChoosePerformancePersonnelScreenViewModel();
  int _selectedIndex = -1;
  final ScrollController _scrollController = ScrollController();



  @override
  void initState() {
    _initApiData();
    super.initState();
  }

  ///
  void _initApiData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final shoppingCarVm = context.read<ShoppingCarProvider>();

      // 先抓取業績人員列表
      await shoppingCarVm.apiGetEmployees(context, search: '');

      // 如果 provider 已經有選中的 staffId，就找對應索引
      if (shoppingCarVm.staff.isNotEmpty) {
        final selectedStaffId = shoppingCarVm.staff.keys.first;

        final index = shoppingCarVm.performanceModel.indexWhere(
              (e) => e.staffId == selectedStaffId,
        );

        if (index != -1) {
          setState(() {
            _selectedIndex = index;
            // 更新 UI 的選中效果
            shoppingCarVm.toggleSelectPerformance(index);
          });
        }
      }
    });
  }


  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();
    return ChangeNotifierProvider.value(
      value: vm,
      child: Padding(
        padding: EdgeInsets.only(top: 35.h),
        child: Column(
          children: [
            Text(
              '選擇銷售人員',
              style: TextStyle(fontSize: 44.sp, color: MyColors.brown_57_54_18),
            ),
            SizedBox(height: 35.h),

            ///搜尋列
            Padding(
              padding: EdgeInsets.only(left: 82.w, right: 66.w),
              child: _searchBar(),
            ),

            SizedBox(height: 17.h),

            ///銷售人員列表
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(left: 82.w, right: 66.w),
                child: _performancePersonnelArea(),
              ),
            ),

            SizedBox(height: 17.h),

            ///返回與送出按鈕
            ReturnAndSubmitBtn(
              backOnTap: () {
                shoppingCarVm.clearSelection();
                Navigator.of(context).pop();

              },
              submitOnTap: () {
                shoppingCarVm.submitSelectedStaff();
                Navigator.of(context).pop();

              },
            ),
          ],
        ),
      ),
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 74.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(right: 12.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [

            ///員工編號輸入框
            BaseTextField(hint: '員工編號', controller: vm.staffIdController),
            SizedBox(width: 26.w),

            ///姓名輸入框
            BaseTextField(hint: '請輸入姓名', controller: vm.nameController),
            SizedBox(width: 26.w),

            ///搜尋按鈕
            ContainerWithRadius(
              onTap: (){
                final shoppingCarVm = context.read<ShoppingCarProvider>();
                final keyword = vm.nameController.text.isNotEmpty
                    ? vm.nameController.text
                    : vm.staffIdController.text;

                shoppingCarVm.apiGetEmployees(context,search: keyword,);
              },
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///業績人員名單區塊
  Widget _performancePersonnelArea() {
    return Column(
      children: [
        _performancePersonnelTitleBar(),
        Expanded(child: _performancePersonnelList()),
      ],
    );
  }

  ///業績人員列表標頭
  Widget _performancePersonnelTitleBar() {
    return PerformancePersonalBaseListBar(
      isTextCenter: true,
      onTap: () {},
      isSelected: false,
      backgroundColor: MyColors.grey_247_247_247,
      staffId: '員工編號',
      shopId: '帳號',
      staffName: '員工姓名',
    );
  }

  ///業績人員列表
  Widget _performancePersonnelList() {
    return Selector<ShoppingCarProvider, List<PerformanceModel>>(
      selector: (context, vm) => vm.performanceModel,
      builder: (context, performancePersonnelListModel, _) {
        if( performancePersonnelListModel.isEmpty){
          return EmptyHint(hint: '尚無業績人員',);
        }

        final shoppingCarVm = context.read<ShoppingCarProvider>();
        return  ListView.builder(
          padding: EdgeInsets.zero,
          physics: BouncingScrollPhysics(),
          itemCount: performancePersonnelListModel.length,
          itemBuilder: (context, index) {
            final bool isSelected = index == _selectedIndex;

            final model = performancePersonnelListModel[index];
            return PerformancePersonalBaseListBar(
              isTextCenter: false,
              isSelected: isSelected,
              backgroundColor: Colors.white,
              staffId: model.staffId,
              shopId: model.shopId,
              staffName: model.staffName,
              onTap: () {
                shoppingCarVm.toggleSelectPerformance(index);
                setState(() {
                  _selectedIndex = index;
                });
              },

            );
          },
        );

      },
    );
  }





}
