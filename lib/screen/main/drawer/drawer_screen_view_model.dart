import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/push_to/push_to_web.dart';
import 'package:sbar_pos/screen/start/my_app.dart';

import '../../../gen/r.dart';
import '../../../model/drawer/drawer_list_model.dart';
import '../../../provider/main_provider.dart';
import '../../../resource/MyColor.dart';
import '../../../util/MyRoutes.dart';
import '../../brand_member/brand_member_screen.dart';
import '../../class_confirm/class_confirm_screen.dart';
import '../../history_order/history_order_screen.dart';
import '../../performance_distribution/performance_distribution_screen.dart';
import '../../product_inventory_management/product_inventory_management_screen.dart';
import '../../reserve_manage/reserve_manage_screen.dart';
import '../main_screen.dart';

class DrawerScreenViewModel with ChangeNotifier {
  String userName = '';
  String userType = '';

  ///選單
  List<DrawerListModel> drawerListModel = [
    DrawerListModel(
      icon: Image(image: R.image.icon_drawer_pos_payment(),width: 30.w,height: 30.w,fit: BoxFit.cover,color:    MyColors.green_129_128_94,),
      title: 'POS結帳',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        context.read<MainProvider>().setInitialIndex(2);

        pushTo(
          PageName.main.toString(),
          builder: (context) => MainScreen(),
          isPushReplacement: true,
        );

      },
    ),
    DrawerListModel(
      icon: Image(image: R.image.icon_drawer_reserve(),width: 30.w,height: 30.w,fit: BoxFit.cover,color:    MyColors.green_129_128_94,),
      title: '預約單管理',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        //ReserveManageScreen
        pushTo(
          PageName.reserveManage.toString(),
          builder: (context) => ReserveManageScreen(),
          isPushReplacement: true,
        );
      },
    ),
    DrawerListModel(
      icon: SvgPicture.asset(
        R.image.icon_history_order(),
        width: 30.w,
        height: 30.w,
        colorFilter: ColorFilter.mode(
        MyColors.green_129_128_94,
          BlendMode.srcIn,
        ),
      ),
      title: '歷史訂單',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        pushTo(
          PageName.historyOrder.toString(),
          builder: (context) => HistoryOrderScreen(),
          isPushReplacement: true,
        );
      },
    ),
    DrawerListModel(
      icon: Image(image: R.image.icon_drawer_stock_management(),width: 30.w,height: 30.w,fit: BoxFit.cover,color:    MyColors.green_129_128_94,),
      title: '商品庫存管理',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        pushTo(
          PageName.productInventoryManagement.toString(),
          builder: (context) => ProductInventoryManagementScreen(),
          isPushReplacement: true,
        );
      },
    ),
    DrawerListModel(
      icon: SvgPicture.asset(
        R.image.icon_brand_member(),
        width: 30.w,
        height: 30.w,
        colorFilter: ColorFilter.mode(
          MyColors.green_129_128_94,
          BlendMode.srcIn,
        ),
      ),
      title: '品牌會員',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        pushTo(
          PageName.brandMember.toString(),
          builder: (context) => BrandMemberScreen(),
          isPushReplacement: true,
        );
      },
    ),
    DrawerListModel(
      icon: Image(image: R.image.icon_drawer_performance_distribution(),width: 30.w,height: 30.w,fit: BoxFit.cover,color:    MyColors.green_129_128_94,),
      title: '業績分配',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        pushTo(
          PageName.performanceDistribution.toString(),
          builder: (context) => PerformanceDistributionScreen(),
          isPushReplacement: true,
        );
        //
      },
    ),

    DrawerListModel(
      icon: Image(image: R.image.icon_class_confirm(),width: 30.w,height: 30.w,fit: BoxFit.cover,color:    MyColors.green_129_128_94,),
      title: '課程確認書查詢',
      onTap: (context) {
        // 先關掉 drawer
        Navigator.pop(context);

        pushTo(
          PageName.classConfirm.toString(),
          builder: (context) => ClassConfirmScreen(),
          isPushReplacement: true,
        );
        //
      },
    ),

  ];
}
