import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../resource/MyColor.dart';
import '../../../widget/app_bar/sbar_app_bar_with_drawer.dart';
import '../../../widget/background/base_background.dart';
import 'drawer_screen.dart';

class BaseScaffoldWithDrawer extends StatefulWidget {
  final String title;
  final Widget child;
  final Widget? tailWidget;
  const BaseScaffoldWithDrawer({super.key, required this.title, required this.child, this.tailWidget});

  @override
  State<BaseScaffoldWithDrawer> createState() => _BaseScaffoldWithDrawerState();
}

class _BaseScaffoldWithDrawerState extends State<BaseScaffoldWithDrawer> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      drawerEnableOpenDragGesture: false,
      key: _scaffoldKey,
      drawer: Drawer(
        width: MediaQuery.of(context).size.width * 0.39,
        child: DrawerScreen(),
      ),

      body: SafeArea(
        child: SbarAppBar(
          tailWidget: widget.tailWidget,
          title: widget.title,
          drawerOnTap: () => _scaffoldKey.currentState?.openDrawer(),//開啟drawer
          child:
          BaseBackground(
            child: widget.child,
          ),
        ),
      ),
    );
  }
}
