import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/screen/login/login_screen.dart';
import 'package:sbar_pos/screen/start/my_app.dart';

import '../../../hive/hive_provider/hive_user_provider.dart';
import '../../../model/drawer/drawer_list_model.dart';
import '../../../provider/main_provider.dart';
import '../../../push_to/logout.dart';
import '../../../util/MyRoutes.dart';
import '../../../widget/cached_network_image/error_cached_network_image.dart';
import '../../../widget/container_with_radius.dart';
import '../../../widget/dialog/sbar_style_dialog.dart';
import '../main_screen.dart';
import 'drawer_screen_view_model.dart';

class DrawerScreen extends StatefulWidget {
  const DrawerScreen({super.key});

  @override
  State<DrawerScreen> createState() => _DrawerScreenState();
}

class _DrawerScreenState extends State<DrawerScreen> {
  final vm = DrawerScreenViewModel();

  @override
  void initState() {
    _initData();
    super.initState();
  }

  void _initData() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      var userData = await HiveUserProvider.getUserOrNull();
      vm.userName = userData?.name ?? '';
      vm.userType = userData?.stuffPermissions ?? '';
      setState(() {});
    });
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 22.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ///返回按鈕
                  ContainerWithRadius(
                    onTap: () {
                      // 先關掉 drawer
                      Navigator.pop(context);

                      context.read<MainProvider>().setInitialIndex(2);

                      pushTo(
                        PageName.main.toString(),
                        builder: (context) => MainScreen(),
                        isPushReplacement: true,
                      );
                    },
                    isHaveBorder: false,
                    w: 89.w,
                    h: 89.w,
                    r: 20.r,
                    color: MyColors.green_157_193_65,
                    child: Center(
                      child: Transform.translate(
                        offset: const Offset(5, 0),
                        child: Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: 40.w,
                        ),
                      ),
                    ),
                  ),

                  ///大頭照區塊
                  Expanded(child: _avatarArea()),

                  ///登出按鈕
                  _logOutBtn(),
                ],
              ),

              SizedBox(height: 55.h),

              // 選單列表
              Expanded(child: _menuList()),
            ],
          ),
        ),
      ),
    );
  }

  ///選單列表區塊
  Widget _menuList() {
    return Selector<DrawerScreenViewModel, List<DrawerListModel>>(
      selector: (context, vm) => vm.drawerListModel,
      builder: (context, drawerListModel, _) {
        return ListView.separated(
          padding: EdgeInsets.zero,
          physics: BouncingScrollPhysics(),
          itemCount: drawerListModel.length,
          itemBuilder: (context, index) {
            final model = drawerListModel[index];
            return _menuListWidget(model);
          },
          separatorBuilder: (context, index) => SizedBox(height: 20.h),
        );
      },
    );
  }

  ///選單列表元件
  Widget _menuListWidget(DrawerListModel model) {
    return ContainerWithRadius(
      onTap: () => model.onTap?.call(context),
      isHaveBorder: true,
      w: double.infinity.w,
      h: 96.h,
      r: 20.r,
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Row(
        children: [
          SizedBox(width: 50.w),
          model.icon,
          SizedBox(width: 41.w),
          Text(
            model.title,
            style: TextStyle(fontSize: 25.sp, color: MyColors.green_129_128_94),
          ),
        ],
      ),
    );
  }

  ///大頭照與基本資料區塊
  Widget _avatarArea() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 大頭照
        ClipRRect(
          borderRadius: BorderRadius.circular(50.r),
          child: CachedNetworkImage(
            imageUrl: '',
            height: 100.w,
            width: 100.w,
            fit: BoxFit.cover,
            errorWidget: (context, url, error) =>
                const ErrorCachedNetworkImage(),
          ),
        ),

        SizedBox(width: 29.w),

        // 文字區塊
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              vm.userType,
              style: TextStyle(
                fontSize: 23.sp,
                color: MyColors.green_121_131_90,
              ),
            ),
            SizedBox(height: 9.h),
            Text(
              vm.userName,
              style: TextStyle(fontSize: 28.sp, color: MyColors.brown_57_54_18),
            ),
          ],
        ),
      ],
    );
  }

  ///登出按鈕
  Widget _logOutBtn() {
    return ContainerWithRadius(
      onTap: () {
        logout(context);
      },
      isHaveBorder: true,
      w: 196.w,
      h: 96.h,
      r: 20.r,
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Center(
        child: Text(
          '登出',
          style: TextStyle(fontSize: 25.sp, color: MyColors.green_129_128_94),
        ),
      ),
    );
  }
}
