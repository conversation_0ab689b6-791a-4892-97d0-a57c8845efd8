import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/screen/main/main_screen_view_model.dart';

import '../../gen/r.dart';
import '../../util/MyRoutes.dart';
import '../../widget/background/base_background.dart';
import '../../widget/tab/base_tab_widget.dart';
import '../../widget/tab/main_tab_widget.dart';
import '../brand_member/brand_member_screen.dart';
import '../buy_cash/buy_cash_screen.dart';
import '../class/class_screen.dart';
import '../product/product_screen.dart';
import '../reserve_calendar/reserve_calendar_screen.dart';
import '../reserve_class/reserve_class_screen.dart';
import '../search_product/search_product_screen.dart';
import '../start/my_app.dart';
import '../test/test.dart';
import 'drawer/drawer_screen.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  final vm = MainScreenViewModel();
  late final List<Widget Function(bool isSelected)> tabs;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    _initApi();
    super.initState();
    tabs = [
      (isSelected) => BaseTabWidget(
        title: '搜尋商品',
        key: ValueKey('tab_search'),
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_search(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_scan'),
        title: '掃描條碼',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_scan(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_product'),
        title: '商品販售',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_product(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_class'),
        title: '課程販售',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_class(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_classReserve'),
        title: '預約課程',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_class(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_classCalendar'),
        title: '預約日曆',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_reserve_calendar(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_buyCash'),
        title: '購買儲值金',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_top_up(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_historyOrder'),
        title: '歷史訂單',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_history_order(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),
      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_brandMember'),
        title: '品牌會員',
        isSelected: isSelected,
        icon: SvgPicture.asset(
          R.image.icon_brand_member(),
          width: 30.w,
          height: 30.w,
          colorFilter: ColorFilter.mode(
            isSelected ? Colors.white : MyColors.green_129_128_94,
            BlendMode.srcIn,
          ),
        ),
      ),

      (isSelected) => BaseTabWidget(
        key: ValueKey('tab_openBox'),
        title: '開錢箱',
        isSelected: isSelected,
        icon: Image(
          image: R.image.icon_open_money_box(),
          width: 30.w,
          height: 30.w,
          color: isSelected ? Colors.white : MyColors.green_129_128_94,
          fit: BoxFit.cover,
        ),
      ),
    ];
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  ///初始化呼叫api
  void _initApi() {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final shoppingCarVm = context.read<ShoppingCarProvider>();


      await shoppingCarVm.apiGetProductsTypes(context);  //取得商品類別
      await shoppingCarVm.apiGetProgramsCourseTypes(context); //取得課程類別

      await shoppingCarVm.apiProgramsLoading(context); //
      await shoppingCarVm.apiProductLoading(context); //

      await shoppingCarVm.apiGetPrograms(
        context,
        clearData: true,
        courseTypeName: shoppingCarVm.classType[0].title,
        isHaveLoading: true
      ); //取得課程列表

      await shoppingCarVm.apiGetProduct(
        context,
        clearData: true,
        productTypeName: shoppingCarVm.productType[0].title,
          isHaveLoading: true
      ); //取得商品列表

      await shoppingCarVm.apiGetPrepaid(context); //取得儲值金列表

      await shoppingCarVm.apiGetPayMethods(context);  //取得付款方式
      shoppingCarVm.mainLoading = false;
      setState(() {

      });
    });

  }




  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: SafeArea(
        child: Scaffold(
          drawerEnableOpenDragGesture: false,
          key: _scaffoldKey,
          drawer: Drawer(
            width: MediaQuery.of(context).size.width * 0.39,
            child: DrawerScreen(),
          ),
          body: Consumer<MainScreenViewModel>(
            builder: (context, data, _) {
              return MainTabWidget(
                drawerOnTap: () => Scaffold.of(context).openDrawer(), //開啟drawer
                childScreen: [
                  ///搜尋商品(這邊從tabWidget內做彈窗)
                  Container(key: ValueKey('screen_search')),

                  ///掃描條碼(這邊從tabWidget內做條碼掃描)
                  BaseBackground(child: Container(key: ValueKey('screen_scan'))),

                  ///商品販售
                  BaseBackground(child: ProductScreen(key: ValueKey('screen_product'))),

                  ///課程販售
                  BaseBackground(child: ClassScreen(key: ValueKey('screen_class'))),

                  ///預約課程
                  BaseBackground(child: ReserveClassScreen(key: ValueKey('screen_reserve_class'))),

                  ///預約日曆
                  BaseBackground(child: ReserveCalendarScreen(key: ValueKey('screen_reserve_calendar'))),

                  ///購買儲值金
                  BaseBackground(child: BuyCashScreen(key: ValueKey('screen_buy_cash'))),

                  ///歷史訂單(這邊從tabWidget內做頁面跳轉)
                  Container(key: ValueKey('screen_history_order')),

                  ///品牌會員(這邊從tabWidget內做頁面跳轉)
                  Container(key: ValueKey('screen_brand_member')),

                  ///開錢箱(這邊從tabWidget內做彈窗)
                  Container(key: ValueKey('screen_open_box')),
                ],
                tabs: tabs,
              );
            },
          ),
        ),
      ),
    );
  }

}
