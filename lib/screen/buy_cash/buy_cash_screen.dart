import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../model/class/class_model.dart';
import '../../model/product/product_model.dart';
import '../../provider/shopping_car_provider.dart';
import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../../widget/shape/circle.dart';
import '../shopping_car/shopping_car_screen.dart';
import 'buy_cash_screen_view_model.dart';

class BuyCashScreen extends StatefulWidget {
  const BuyCashScreen({super.key});

  @override
  State<BuyCashScreen> createState() => _BuyCashScreenState();
}

class _BuyCashScreenState extends State<BuyCashScreen> {
  final vm = BuyCashScreenViewModel();
  RefreshController controller = RefreshController();

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: Consumer<ShoppingCarProvider>(
        builder: (context, data, _) {
          return Padding(
            padding: EdgeInsets.only(bottom: 15.h, left: 16.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [

                ///主商品列表
                Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(top: 138.h), //這邊自己管控高度Padding
                    child: _buyCashList(),
                  ),
                ),

                SizedBox(width: 17.w),

                ///右側購物車列表
                ShoppingCarScreen(),
              ],
            ),
          );
        },
      ),
    );
  }

  ///儲值金列表
  Widget _buyCashList() {
    return Selector<ShoppingCarProvider, List<ProductModel>>(
      selector: (context, vm) => vm.cashModel,
      builder: (context, cashModel, _) {
        final shoppingCarVm = context.read<ShoppingCarProvider>();
        return SmartRefresher(
          enablePullDown: true,
          controller: controller,
          onRefresh: () async {
            try {
              await shoppingCarVm.apiGetPrepaid(context);
            } finally {
              controller.refreshCompleted();
            }
          },
          child: GridView.builder(
            shrinkWrap: true,
            padding: EdgeInsets.only(bottom: 42.h, left: 27.w),
            physics: const BouncingScrollPhysics(),
            itemCount: cashModel.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 20.w,
              mainAxisSpacing: 20.h,
              childAspectRatio: 685 / 188,
            ),
            itemBuilder: (context, index) {
              final model = cashModel[index];
              return _buyCashWidget(model);
            },
          ),
        );
      },
    );
  }


  ///儲值金列表元件
  Widget _buyCashWidget(ProductModel model) {
    final shoppingCarVm = context.read<ShoppingCarProvider>();

    // 判斷是否已選取
    final isSelected = shoppingCarVm.shoppingCarListModel.any(
          (e) => e.productId == model.productId,
    );

    // 取得選取數量
    final selectAmount = shoppingCarVm.shoppingCarListModel
        .firstWhere(
          (e) => e.productId == model.productId,
      orElse: () =>
          ProductModel(
            productId: model.productId,
            title: '',
            price: 0,
            selectAmount: 0,
            totalAmount: 0,
            type: 1,
            isSelect: false,
            isTempSelection: false,
          ),
    )
        .selectAmount;

    return InkWell(
      onTap: () {
        /// 如果購物車已經有這個商品，則刪除或新增
        if (isSelected) {
          shoppingCarVm.removedShoppingCarByProductId(
            productId: model.productId,
          );
        } else {
          shoppingCarVm.addToShoppingCarByProductId(
            productId: model.productId,
          );
        }
      },
      child: Container(
        width: double.infinity.w,
        height: 188.h,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(3, 6),
            ),
          ],
        ),
        child: Stack(
          clipBehavior: Clip.none,
          children: [

            /// 錢包名稱
            Padding(
              padding: EdgeInsets.only(left: 19.w, top: 15.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.title,
                    style: TextStyle(
                      fontSize: 28.sp,
                      color: MyColors.brown_57_54_18,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    model.subTitle ?? '',
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 25.sp,
                      color: MyColors.green_129_128_94,
                    ),
                  ),
                ],
              ),
            ),

            /// 價格文字
            Positioned(
              left: isSelected ? 105.w : 19.w,
              bottom: 17.h,
              child: Text(
                '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
                style: TextStyle(
                  fontSize: 25.sp,
                  color: MyColors.grey_119_119_119,
                ),
              ),
            ),

            /// 左下選取數量 (依 isSelected 顯示)
            if (isSelected)
              Positioned(
                bottom: -3.h,
                left: 19.w,
                child: Circle(
                  color: MyColors.yellow_251_227_140,
                  size: 63.w,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x669DC141),
                      offset: Offset(3, 6),
                      blurRadius: 10,
                    ),
                  ],
                  child: Center(
                    child: Text(
                      selectAmount.toString(),
                      style: TextStyle(
                        color: MyColors.green_129_128_94,
                        fontSize: 25.sp,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
