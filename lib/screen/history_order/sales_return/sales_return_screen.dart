import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../model/member/member_history_order_model.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/base_title_list_bar/member_history_order_base_list_bar.dart';
import '../../../widget/base_title_list_bar/sales_return_base_list_bar.dart';
import '../../../widget/btn/return_and_submit_btn.dart';
import '../../../widget/hint/empty_hint.dart';
import '../../member/member_info/member_history_order/member_history_order_screen_view_model.dart';

class SalesReturnScreen extends StatelessWidget {
  final MemberHistoryOrderScreenViewModel memberHistoryOrderScreenViewModel;

  const SalesReturnScreen({super.key, required this.memberHistoryOrderScreenViewModel, });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: memberHistoryOrderScreenViewModel,
      child: Column(
        children: [
          SizedBox(height: 35.h),
          Text(
            '您將進行「鎖貨退回」?',
            style: TextStyle(fontSize: 44.sp, color: MyColors.brown_57_54_18),
          ),
          SizedBox(height: 40.h),
          Text(
            '以下為付款紀錄，供退款參考使用，退款完成後，請按下方的「確認銷貨退回」。',
            style: TextStyle(fontSize: 23.sp, color: MyColors.green_129_128_94),
          ),

          SizedBox(height: 38.h),

          ///歷史訂單列表區塊
          Expanded(child: Padding(
            padding:  EdgeInsets.symmetric(horizontal: 21.w),
            child: _historyOrderArea(),
          )),


          SizedBox(height: 35.h),

          ///返回與送出按鈕
          ReturnAndSubmitBtn(
            submitText: '確認銷貨退回',
            backOnTap: () {
              Navigator.of(context).pop();

            },
            submitOnTap: (){
              //TODO:api
              Navigator.of(context).pop();
            },
          ),
        ],
      ),
    );
  }

  ///歷史訂單列表區塊
  Widget _historyOrderArea() {
    return Column(
      children: [
        _historyOrderTitleBar(),
        Expanded(child: _historyOrderList()),
      ],
    );
  }

  ///歷史訂單列表
  Widget _historyOrderList() {
    return Selector<
      MemberHistoryOrderScreenViewModel,
      List<MemberHistoryOrderModel>
    >(
      selector: (context, vm) => vm.memberHistoryOrderModel,
      builder: (context, topUpRecordModel, _) {
        if (topUpRecordModel.isEmpty) {
          return EmptyHint(hint: '尚無歷史訂單紀錄');
        }

        return ListView.builder(
          padding: EdgeInsets.only(bottom: 50.h),
          physics: BouncingScrollPhysics(),
          itemCount: topUpRecordModel.length,
          itemBuilder: (context, index) {
            final model = topUpRecordModel[index];
            return SalesReturnBaseListBar(
              isTitleBar: false,
              orderId: model.orderId,
              checkOutTime: model.checkOutTime,
              price: model.price,
              memberName: model.memberName,
              operatorName: model.operatorName,
              backgroundColor: Colors.white,
              invoiceId: model.invoiceId,
              priceInfo: model.priceInfo,
              orderStatue: model.orderStatue,
              orderStatueCode: model.orderStatueCode,
              unPaidPrice: model.unPaidPrice,
              totalSalesOrderAmount: model.totalSalesOrderAmount ?? '',
            );
          },
        );
      },
    );
  }

  ///歷史訂單列表標頭
  Widget _historyOrderTitleBar() {
    return SalesReturnBaseListBar(
      isTitleBar: true,
      orderId: '訂單編號',
      invoiceId: '發票號碼',
      checkOutTime: '結帳時間',
      totalSalesOrderAmount: '銷售訂單總金額',
      price: '本次付款金額',
      orderStatue: '訂單狀態',
      orderStatueCode: 0,
      unPaidPrice: '未付款金額',
      memberName: '會員',
      operatorName: '操作員',
      priceInfo: '',
      backgroundColor: MyColors.grey_247_247_247,
    );
  }
}
