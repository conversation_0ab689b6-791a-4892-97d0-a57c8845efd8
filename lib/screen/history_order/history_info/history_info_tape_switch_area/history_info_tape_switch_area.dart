import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/widget/container_with_radius.dart';
import 'package:sbar_pos/widget/shape/circle.dart';

import '../../../../model/product/product_model.dart';
import '../../../../util/StringUtils.dart';
import '../../../../widget/btn/switch_type_row.dart';
import '../../../../widget/hint/empty_hint.dart';
import '../history_info_screen_view_model.dart';



class HistoryInfoTapeSwitchArea extends StatefulWidget {
  final HistoryInfoScreenViewModel vm;
  const HistoryInfoTapeSwitchArea({super.key, required this.vm});

  @override
  State<HistoryInfoTapeSwitchArea> createState() => _HistoryInfoTapeSwitchAreaState();
}

class _HistoryInfoTapeSwitchAreaState extends State<HistoryInfoTapeSwitchArea> {
  int _selectedType = 1;
  TextEditingController noteController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 844.5.w,
      child: Column(
        children: [
          ///上方切換type按鈕
          _switchTypeRow(),

          SizedBox(height: 15.h),

          ///商品列表
          Expanded(
            child: _productList(),
          ),

          SizedBox(height: 15.h),

          ///折扣列
          _discountBar(),
          SizedBox(height: 19.h),


        ],
      ),
    );
  }

  ///商品列表
  Widget _productList() {
    return Selector<HistoryInfoScreenViewModel, List<ProductModel>>(
      selector: (context, vm) => vm.shoppingCarListModel,

      builder: (context, shoppingCarListModel, _) {
        //暫定(1:商品，2:預約與課程，3:儲值金)
        // 根據 type 過濾資料
        final filteredList = shoppingCarListModel.where((item) => item.type == _selectedType).toList();


        if (filteredList.isEmpty) {
          return EmptyHint(hint:  '購物車尚無商品',);
        }
        return ListView.separated(
          padding: EdgeInsets.zero,
          physics: BouncingScrollPhysics(),
          itemCount: filteredList.length,
          itemBuilder: (context, index) {
            final model = filteredList[index];
            return _productListWidget(model, index);
          },
          separatorBuilder: (context, index) => SizedBox(height: 10.h),
        );
      },
    );
  }

  ///商品列表元件
  Widget _productListWidget(ProductModel model, int index) {
    return ContainerWithRadius(
      isHaveBorder: true,
      w: 830.w,
      h: 128.h,
      r: 20.r,
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: const Color(0x14000000),
          offset: const Offset(3, 6),
          blurRadius: 10.r,
        ),
      ],
      child: Padding(
        padding: EdgeInsets.only(left: 25.w, top: 15.h, bottom: 21.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              model.title,
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.brown_57_54_18,
              ),
            ),
            Spacer(),
            Text(
              '\$ ${StringUtils.formatMoneyForDouble(model.price)}',
              style: TextStyle(
                fontSize: 25.sp,
                color: MyColors.grey_119_119_119,
              ),
            ),
          ],
        ),
      ),
    );
  }


  ///折扣列
  Widget _discountBar() {
    final textStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return ContainerWithRadius(
      w: 830.w,
      h: 78.h,
      r: 20.r,
      color: Colors.white,
      isHaveBorder: true,
      child: Padding(
        padding: EdgeInsets.only(left: 16.w, right: 20.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Circle(
              color: MyColors.yellow_251_227_140,
              size: 40.w,
              child: Icon(Icons.check, color: MyColors.green_121_131_90),
            ),
            SizedBox(width: 21.w),
            Text('已符合', style: textStyle),
            SizedBox(width: 21.w),
            Text('全單滿1000元，打9折', style: textStyle),
            Spacer(),
            Text('-\$ ${StringUtils.formatMoneyForDouble(210)}', style: textStyle),
          ],
        ),
      ),
    );
  }

  ///上方切換type按鈕
  Widget _switchTypeRow() {

    return Consumer<HistoryInfoScreenViewModel>(
      builder: (context, vm, _) {
        int count1 = vm.shoppingCarListModel.where((e) => e.type == 1).length;
        int count2 = vm.shoppingCarListModel.where((e) => e.type == 2).length;
        int count3 = vm.shoppingCarListModel.where((e) => e.type == 3).length;

        return SwitchTypeRow(
          amount1: count1,
          amount2: count2,
          amount3: count3,
          selectedType: _selectedType,
          onChanged: (newType) {
            setState(() {
              _selectedType = newType;
            });
          },
        );
      },
    );
  }




}
