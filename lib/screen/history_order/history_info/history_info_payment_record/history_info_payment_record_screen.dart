import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';

import '../../../../model/member/member_history_order_model.dart';
import '../../../../resource/MyColor.dart';
import '../../../../widget/base_title_list_bar/member_history_order_base_list_bar.dart';
import '../../../../widget/btn/return_and_submit_btn.dart';
import '../../../../widget/hint/empty_hint.dart';
import '../history_info_screen_view_model.dart';

class HistoryInfoPaymentRecordScreen extends StatefulWidget {
  final HistoryInfoScreenViewModel vm;
  const HistoryInfoPaymentRecordScreen({super.key, required this.vm});

  @override
  State<HistoryInfoPaymentRecordScreen> createState() => _HistoryInfoPaymentRecordScreenState();
}

class _HistoryInfoPaymentRecordScreenState extends State<HistoryInfoPaymentRecordScreen> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: widget.vm,
      child: Padding(
        padding: EdgeInsets.only(top: 35.h, left: 16.w, right: 33.w),
        child: Column(
          children: [

            Text('付款紀錄',style: TextStyle(fontSize: 44.sp,color: MyColors.brown_57_54_18),),
            SizedBox(height: 38.h,),

            ///歷史訂單列表區塊
            Expanded(child: _historyOrderArea()),

            ///返回與送出按鈕
            ReturnAndSubmitBtn(
              submitText: '付款',
              backOnTap: () {
                Navigator.of(context).pop();

              },
              submitOnTap: (){},
            ),
          ],
        ),
      ),
    );

  }

  ///歷史訂單列表區塊
  Widget _historyOrderArea() {
    return Column(
      children: [
        _historyOrderTitleBar(),
        Expanded(child: _historyOrderList()),
      ],
    );
  }

  ///歷史訂單列表
  Widget _historyOrderList() {
    return Selector<HistoryInfoScreenViewModel, int>(
      selector: (context, vm) => vm.memberHistoryOrderModel.length,
      builder: (context, length, _) {
        if (length == 0) {
          return EmptyHint(hint: '尚無付款紀錄');
        }

        return ListView.builder(
          padding: EdgeInsets.only(bottom: 50.h),
          physics: BouncingScrollPhysics(),
          itemCount: length,
          itemBuilder: (context, index) {
            return Consumer<HistoryInfoScreenViewModel>(
              builder: (context, vm, _) {
                final model = vm.memberHistoryOrderModel[index];
                return MemberHistoryOrderBaseListBar(
                  vm: widget.vm,
                  index: index,
                  isHaveEdit: true,
                  isHaveOnTap: false,
                  isTitleBar: false,
                  orderId: model.orderId,
                  checkOutTime: model.checkOutTime,
                  somPrice: model.somPrice,
                  price: model.price,
                  memberName: model.memberName,
                  operatorName: model.operatorName,
                  backgroundColor: Colors.white,
                  onTap: () {},
                  invoiceId: model.invoiceId,
                  priceInfo: model.priceInfo,
                  orderStatue: model.orderStatue,
                  orderStatueCode: model.orderStatueCode,
                  unPaidPrice: model.unPaidPrice,
                );
              },
            );
          },
        );
      },
    );

  }

  ///歷史訂單列表標頭
  Widget _historyOrderTitleBar() {
    return MemberHistoryOrderBaseListBar(
      vm: widget.vm,
      isHaveOnTap: false,
      isTitleBar: true,
      orderId: '訂單編號',
      invoiceId: '發票號碼',
      checkOutTime: '結帳時間',
      somPrice: '銷售訂單總金額',
      price: '本次付款金額',
      orderStatue: '訂單狀態',
      orderStatueCode: 0,
      unPaidPrice: '未付款金額',
      memberName: '會員',
      operatorName: '操作員',
      priceInfo: '',
      backgroundColor: MyColors.grey_247_247_247,
      onTap: () {},
    );
  }


}
