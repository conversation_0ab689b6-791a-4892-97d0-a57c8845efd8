import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/model/product/product_model.dart';
import 'package:sbar_pos/provider/shopping_car_provider.dart';
import 'package:sbar_pos/resource/MyColor.dart';
import 'package:sbar_pos/screen/payment/payment_screen.dart';
import 'package:sbar_pos/screen/start/my_app.dart';
import 'package:sbar_pos/widget/btn/base_btn.dart';

import '../../../route_observer/page_info.dart';
import '../../../util/MyRoutes.dart';
import '../../../util/StringUtils.dart';
import '../../../widget/btn/return_and_submit_btn.dart';
import '../../../widget/cached_network_image/error_cached_network_image.dart';
import '../../../widget/container_with_radius.dart';
import '../../../widget/dialog/MyDialog.dart';
import '../../../widget/divider/custom_vertical_divider.dart';
import '../../member/member_info/member_history_order/member_history_order_screen_view_model.dart';
import '../../payment/payment_details/payment_details_area.dart';
import '../cancel_order/cancel_order_screen.dart';
import '../sales_return/sales_return_screen.dart';
import 'history_info_payment_record/history_info_payment_record_screen.dart';
import 'history_info_screen_view_model.dart';
import 'history_info_tape_switch_area/history_info_tape_switch_area.dart';

class HistoryInfoScreen extends StatefulWidget {
  final int orderStatueInfo; //暫定寫法(暫定0 未完成付款，1 已付款，２為已銷貨退回)
  final MemberHistoryOrderScreenViewModel memberHistoryOrderScreenViewModel;

  const HistoryInfoScreen(
      {super.key, required this.orderStatueInfo, required this.memberHistoryOrderScreenViewModel});

  @override
  State<HistoryInfoScreen> createState() => _HistoryInfoScreenState();
}

class _HistoryInfoScreenState extends State<HistoryInfoScreen> {
  final vm = HistoryInfoScreenViewModel();



  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: Padding(
        padding: EdgeInsets.only(top: 35.h),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: _titleBar(),
            ),

            SizedBox(height: 17.h),

            ///訂單資訊
            _orderInfoBar(),
            SizedBox(height: 11.h),

            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 30.w),
                child: Row(
                  children: [

                    ///左側訂單列表區塊
                    _orderListArea(),

                    ///中間訂單明細列表
                    SizedBox(
                      width: (420 + 25 + 5.5 + 5).w,
                      child: PaymentDetailsArea(
                        payCount: vm.shoppingCarListModel.length,
                        padding: EdgeInsets.only(
                          top: 20.h,
                          bottom: 23.5.h,
                          left: 19.w,
                        ),
                        isHistoryInfo: true,
                      ),
                    ),

                    ///右側結帳明細
                    Padding(
                      padding: EdgeInsets.only(left: 19.w),
                      child: _checkoutOutBillArea(),
                    )
                  ],
                ),
              ),
            ),

            ///返回按鈕
            ReturnAndSubmitBtn(
              isHaveSubmit: false,
              backOnTap: () {

                //將原本的購物清單資料還原
                context.read<ShoppingCarProvider>().restoreShoppingCarData();

                Navigator.of(context).pop();
              },
              submitOnTap: () {},
            ),
          ],
        ),
      ),
    );
  }

  ///標題與按鈕
  Widget _titleBar() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Text(
              '訂單內容',
              style: TextStyle(fontSize: 44.sp, color: MyColors.brown_57_54_18),
            ),
            SizedBox(width: 92.w),

            //補印課程確認書按鈕
            _baseBtn('補印課程確認書', () {}),
            SizedBox(width: 12.w),

            //補印銷貨明細按鈕
            _baseBtn('補印銷貨明細', () {}),
            SizedBox(width: 12.w),

            //補印發票按鈕
            _baseBtn('補印發票', () {}),
            SizedBox(width: 12.w),
          ],
        ),

        //銷貨退回
        if(widget.orderStatueInfo == 1)
          _baseBtn('銷貨退回', () {
            _salesReturnDialog();
          },
          textColor: MyColors.orange_240_149_68),

        //終止訂單
        if(widget.orderStatueInfo == 0)
          _baseBtn('終止訂單', () {
            _cancelOrderDialog();
          },
              textColor: MyColors.purple_120_96_221),
      ],
    );
  }

  ///訂單資訊列
  Widget _orderInfoBar() {
    return ContainerWithRadius(
      w: 1740.w,
      h: 150.h,
      r: 0.r,
      isHaveBorder: true,
      color: MyColors.grey_247_247_247,
      child: Padding(
        padding: EdgeInsets.only(left: 39.w, right: 15.w),
        child: Row(
          children: [
            //會員姓名區塊
            _memberArea(),
            SizedBox(width: 45.w),
            CustomVerticalDivider(height: 150.h),

            //訂單編號與成立時間區塊
            _orderInfo(),
            CustomVerticalDivider(height: 130.h),

            //銷售人員區塊
            _salesInfo(),
            CustomVerticalDivider(height: 130.h),

            //訂單狀態區塊
            _orderStatue(),
          ],
        ),
      ),
    );
  }

  /// 會員姓名區塊
  Widget _memberArea() {
    return Row(
      children: [
        //大頭照
        ClipRRect(
          borderRadius: BorderRadius.circular(50.r),
          child: CachedNetworkImage(
            imageUrl: '',
            height: 90.w,
            width: 90.w,
            fit: BoxFit.cover,
            errorWidget: (context, url, error) =>
            const ErrorCachedNetworkImage(),
          ),
        ),

        SizedBox(width: 25.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                '會員姓名文字',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: MyColors.brown_57_54_18,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
            SizedBox(height: 13.h),
            Text(
              '0912345678',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
          ],
        ),

        SizedBox(width: 49.25.w),

        Icon(
          Icons.arrow_forward_ios_sharp,
          size: 40.w,
          color: MyColors.grey_164_164_164,
        ),
      ],
    );
  }

  /// 訂單編號與成立時間區塊
  Widget _orderInfo() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          FittedBox(
            fit: BoxFit.scaleDown,
            child: Text(
              '訂單編號：A12345679',
              style: TextStyle(
                fontSize: 28.sp,
                color: MyColors.green_129_128_94,
              ),
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            '成立時間：2025-03-21 15:20',
            style: TextStyle(fontSize: 28.sp, color: MyColors.green_129_128_94),
          ),
        ],
      ),
    );
  }

  ///銷售人員區塊
  Widget _salesInfo() {
    return Padding(
      padding: EdgeInsets.only(left: 37.w, right: 75.w),
      child: Text(
        '銷售人員：陳美月',
        style: TextStyle(fontSize: 28.sp, color: MyColors.green_129_128_94),
      ),
    );
  }

  ///訂單狀態區塊
  Widget _orderStatue() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return Padding(
      padding: EdgeInsets.only(left: 40.w),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              //訂單狀態
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(text: '訂單狀態：', style: titleStyle),
                    WidgetSpan(child: SizedBox(width: 24.w)),
                    WidgetSpan(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          _getOrderStatueText(),
                          style: _getOrderStatueTextStyle(titleStyle),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 16.h),

              //未付款金額
              RichText(
                text: TextSpan(
                  children: [
                    TextSpan(text: '未付款金額：', style: titleStyle),
                    WidgetSpan(child: SizedBox(width: 24.w)),

                    WidgetSpan(
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          '\$ ${StringUtils.formatMoneyForDouble(1000)}',
                          style: titleStyle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          //付款按鈕(未付款才有)
          if (widget.orderStatueInfo == 0) SizedBox(width: 40.w),

          if (widget.orderStatueInfo == 0) _baseBtn('付款', () {
            final shoppingCarVm = context.read<ShoppingCarProvider>();


            //替換成此訂單的購物車資料
            shoppingCarVm.shoppingCarListModel = vm.shoppingCarListModel;



            pushTo(PageName.payment.toString(),
                builder: (context) => PaymentScreen(pageName: PageName.historyInfo.toString(),),);
          }, w: 120.w),
        ],
      ),
    );
  }

  ///左側訂單列
  Widget _orderListArea() {
    return SizedBox(
      width: 800.w,
      child: HistoryInfoTapeSwitchArea(vm: vm),
    );
  }

  ///右側結帳明細區塊
  Widget _checkoutOutBillArea() {
    return SizedBox(
      width: 460.w,
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(left: 35.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [

                //付款紀錄
                Text(
                  '付款紀錄：共計３筆',
                  style: TextStyle(
                    fontSize: 25.sp,
                    color: MyColors.green_129_128_94,
                  ),
                ),

                //付款紀錄按鈕
                _baseBtn('付款紀錄', () {
                  _historyInfoPaymentRecordDialog();
                }, w: 120.w)
              ],
            ),
          ),

          SizedBox(height: 31.h,),

          //結帳明細
          _checkoutBill()
        ],
      ),
    );
  }


  ///結帳明細
  Widget _checkoutBill() {
    final titleStyle = TextStyle(
      fontSize: 28.sp,
      color: MyColors.green_129_128_94,
    );
    return Column(
      children: [

        ///結帳人員與時間
        ContainerWithRadius(
            w: 460.w,
            h: 126.h,
            r: 0.r,
            color: MyColors.grey_248_248_248,
            isHaveBorder: true,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 35.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('結帳時間：2025-03-25 15:20', style: titleStyle,),
                  SizedBox(height: 10.h,),
                  Text('操作人員：小明', style: titleStyle,),
                ],
              ),
            )),

        SizedBox(height: 16.5.h,),

        ///金額明細
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 35.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [

              //未付款金額
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('未付款金額', style: titleStyle.copyWith(
                      fontSize: 32.sp, fontWeight: FontWeight.w700),),
                  Text('\$ ${StringUtils.formatMoneyForDouble(3000)}',
                    style: titleStyle.copyWith(fontSize: 32.sp,
                        fontWeight: FontWeight.w700,
                        color: MyColors.brown_57_54_18),)
                ],
              ),
              SizedBox(height: 23.5.h,),
              Divider(thickness: 1, height: 1,),
              SizedBox(height: 35.h,),

              //信用卡
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('信用卡', style: titleStyle,),
                  Text('\$ ${StringUtils.formatMoneyForDouble(3000)}',
                    style: titleStyle.copyWith(color: MyColors.brown_57_54_18),)
                ],
              ),

              SizedBox(height: 20.h,),
              Divider(thickness: 1, height: 1,),
              SizedBox(height: 6.h,),

              //找零
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('找零', style: titleStyle.copyWith(
                      fontSize: 32.sp, fontWeight: FontWeight.w700),),
                  Text('\$ ${StringUtils.formatMoneyForDouble(3000)}',
                    style: titleStyle.copyWith(fontSize: 32.sp,
                        fontWeight: FontWeight.w700,
                        color: MyColors.brown_57_54_18),)
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  ///baseBtn
  Widget _baseBtn(String title, VoidCallback onTap, {double? w,Color? textColor}) {
    return Padding(
      padding: EdgeInsets.only(top: (44 - 35).h),
      child: ContainerWithRadius(
        onTap: onTap,
        isHaveBorder: true,
        boxShadow: [
          BoxShadow(
            color: const Color(0x14000000),
            offset: const Offset(3, 6),
            blurRadius: 10.r,
          ),
        ],
        w: w ?? 196.w,
        h: 63.h,
        r: 20.r,
        color: Colors.white,
        child: Center(
          child: Text(
            title,
            style: TextStyle(fontSize: 25.sp, color: textColor ?? MyColors.green_121_131_90),
          ),
        ),
      ),
    );
  }

  ///提取訂單狀態文字顏色
  //暫定寫法(暫定0 未完成付款，1 已付款)
  TextStyle _getOrderStatueTextStyle(TextStyle titleStyle) {
    if (widget.orderStatueInfo == 0) {
      return titleStyle.copyWith(color: MyColors.red_230_75_75);
    }
    if (widget.orderStatueInfo == 1) {
      return titleStyle.copyWith(color: MyColors.green_129_128_94);
    }
    return titleStyle;
  }

  ///提取訂單狀態文字
  //暫定寫法(暫定0 未完成付款，1 已付款)
  String _getOrderStatueText() {
    if (widget.orderStatueInfo == 0) {
      return '未完成付款';
    }
    if (widget.orderStatueInfo == 1) {
      return '已完成付款';
    }
    return '';
  }

  ///開啟付款紀錄彈窗
  void _historyInfoPaymentRecordDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
                isHaveBorder: true,
                w: double.infinity.w,
                h: double.infinity.h,
                r: 20.r,
                color: Colors.white,
                child: HistoryInfoPaymentRecordScreen(vm: vm,)
            ),
          ),
        ),
      ),
    );
  }

  ///開啟銷貨撤回彈窗
  void _salesReturnDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
                isHaveBorder: true,
                w: double.infinity.w,
                h: double.infinity.h,
                r: 20.r,
                color: Colors.white,
                child: SalesReturnScreen(
                  memberHistoryOrderScreenViewModel: widget
                      .memberHistoryOrderScreenViewModel,)
            ),
          ),
        ),
      ),
    );
  }


  ///開啟終止訂單彈窗
  void _cancelOrderDialog() {
    MyDialog().customWidgetDialog(
      context,
      false,
      TransitionType.fadeIn,
      SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 60.w, vertical: 50.h),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20.r),
            //這邊若只靠ContainerWithRadius下方的圓角會消失
            child: ContainerWithRadius(
                isHaveBorder: true,
                w: double.infinity.w,
                h: double.infinity.h,
                r: 20.r,
                color: Colors.white,
                child: CancelOrderScreen(vm: vm,)
            ),
          ),
        ),
      ),
    );
  }
}
