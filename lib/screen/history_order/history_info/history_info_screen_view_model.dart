import 'package:flutter/cupertino.dart';

import '../../../model/member/member_history_order_model.dart';
import '../../../model/product/product_model.dart';

class HistoryInfoScreenViewModel with ChangeNotifier {


  ///購物車列表
  //暫定(1:商品，2:預約與課程，3:儲值金)
  List<ProductModel> shoppingCarListModel = [
    ProductModel(
      title: '商品名稱A',
      selectAmount: 1,
      totalAmount: 5,
      price: 300,
      type: 1,
      isSelect: false,
      isTempSelection: false,
      productId: 'A111',
    ),
    ProductModel(
      title: '商品名稱B',
      selectAmount: 1,
      totalAmount: 5,
      price: 300,
      type: 1,
      isSelect: false,
      isTempSelection: false,
      productId: 'A112',
    ),
    ProductModel(
      title: '商品名稱C',
      selectAmount: 1,
      totalAmount: 5,
      price: 300,
      type: 1,
      isSelect: false,
      isTempSelection: false,
      productId: 'A113',
    ),
    ProductModel(
      title: '預約與課程名稱D',
      selectAmount: 1,
      totalAmount: 5,
      price: 300,
      type: 2,
      isSelect: false,
      isTempSelection: false,
      productId: 'C004',
    ),

    ProductModel(
      title: '儲值金錢包A',
      selectAmount: 1,
      totalAmount: 5,
      price: 300,
      type: 3,
      isSelect: false,
      isTempSelection: false,
      productId: 'H001',
    ),
    ProductModel(
      title: '儲值金錢包B',
      selectAmount: 1,
      totalAmount: 5,
      price: 300,
      type: 3,
      isSelect: false,
      isTempSelection: false,
      productId: 'H002',
    ),
  ];

  //歷史訂單資料
  List<MemberHistoryOrderModel> memberHistoryOrderModel = [
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,
      //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '0',
      memberName: '王小美',
      operatorName: '小美',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,
      //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '0',
      memberName: '孫總',
      operatorName: '阿孫',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '28000',
      priceInfo: '現金',
      orderStatue: '未完成付款',
      orderStatueCode: 0,
      //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '28000',
      memberName: '洪詩詩',
      operatorName: '詩詩',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '50000',
      price: '35000',
      priceInfo: '百貨優惠券,現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,
      //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '0',
      memberName: '金小五',
      operatorName: '小五',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: 'LINE Pay',
      orderStatue: '未完成付款',
      orderStatueCode: 0,
      //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '35000',
      memberName: '劉曉琪',
      operatorName: '小琪',
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金,信用卡',
      orderStatue: '完成付款',
      orderStatueCode: 1,
      //(暫定：０為未完成付款，１為其他，２為已銷貨退回)
      unPaidPrice: '0',
      memberName: '陳小羊',
      operatorName: '小羊',
    ),
  ];

  ///
  void updateInvoiceId(int index, String newInvoiceId) {
    memberHistoryOrderModel[index] =
        memberHistoryOrderModel[index].copyWith(invoiceId: newInvoiceId);
    notifyListeners();
  }
}
