// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:qr_code_scanner/qr_code_scanner.dart';
// import 'dart:io';
//
// import 'package:sbar_pos/screen/scan/scanner_overlay.dart';
//
// class ScannerScreen extends StatefulWidget {
//   const ScannerScreen({super.key});
//
//   @override
//   State<StatefulWidget> createState() => _ScannerScreenState();
// }
//
// class _ScannerScreenState extends State<ScannerScreen> {
//   final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
//   QRViewController? controller;
//   String? scanResult;
//
//   @override
//   void reassemble() {
//     super.reassemble();
//     if (Platform.isAndroid) {
//       controller!.pauseCamera();
//     }
//     controller!.resumeCamera();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//
//       body: Stack(
//         children: [
//
//
//           // 相機畫面
//           QRView(
//             key: qrKey,
//             onQRViewCreated: _onQRViewCreated,
//             overlay: QrScannerOverlayShape( // optional: 可略過這個自帶遮罩
//               borderColor: Colors.transparent,
//               overlayColor: Colors.transparent,
//             ),
//           ),
//
//           // 自訂掃描框
//           Positioned.fill(child: ScannerOverlay()),
//
//           // 顯示結果 (optional)
//           if (scanResult != null)
//             Align(
//               alignment: Alignment.bottomCenter,
//               child: Container(
//                 padding: EdgeInsets.all(16),
//                 color: Colors.black.withOpacity(0.7),
//                 child: Text(
//                   '掃描結果: $scanResult',
//                   style: TextStyle(color: Colors.white, fontSize: 18),
//                 ),
//               ),
//             ),
//
//           Positioned(
//               top: 50.h,
//               left: 50.w,
//               child: InkWell(
//                   onTap: (){
//                     Navigator.of(context).pop();
//                   },
//                   child: Icon(Icons.arrow_back_ios,color: Colors.white,))),
//         ],
//       ),
//     );
//   }
//
//   void _onQRViewCreated(QRViewController controller) {
//     this.controller = controller;
//     controller.scannedDataStream.listen((scanData) {
//       controller.pauseCamera(); // 暫停避免連續掃
//       setState(() {
//         scanResult = scanData.code;
//       });
//
//       // TODO:跳轉或處理資料
//       // Navigator.pop(context, scanData.code);
//     });
//   }
//
//   @override
//   void dispose() {
//     controller?.dispose();
//     super.dispose();
//   }
// }
