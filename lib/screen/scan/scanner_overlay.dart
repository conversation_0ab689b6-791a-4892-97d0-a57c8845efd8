import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';

class ScannerOverlay extends StatelessWidget {
  const ScannerOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final width = constraints.maxWidth;
        final height = constraints.maxHeight;

        final boxSize = width * 0.6;
        final top = (height - boxSize) / 2;
        final left = (width - boxSize) / 2;

        return Stack(
          children: [
            // 四周半透明遮罩
            Container(color: Colors.black.withOpacity(0.4)),

            // 中央掃描區透明
            Positioned(
              top: top,
              left: left,
              child: Container(
                width: boxSize,
                height: boxSize,
                decoration: BoxDecoration(color: Colors.transparent),
                child: CustomPaint(painter: CornerPainter()),
              ),
            ),

            // 紅色掃描線
            Positioned(
              top: top + boxSize / 2,
              left: left,
              child: Container(
                width: boxSize,
                height: 1,
                color: MyColors.red_230_75_75,
              ),
            ),
          ],
        );
      },
    );
  }
}

class CornerPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = MyColors.green_93_93_71
      ..strokeWidth = 4
      ..style = PaintingStyle.stroke;

    final double horizontalLength = 100.w; // 水平邊長
    final double verticalLength = 100.w; // 垂直邊長

    // 左上
    canvas.drawLine(Offset(0, 0), Offset(horizontalLength, 0), paint);
    canvas.drawLine(Offset(0, 0), Offset(0, verticalLength), paint);

    // 右上
    canvas.drawLine(
      Offset(size.width, 0),
      Offset(size.width - horizontalLength, 0),
      paint,
    );
    canvas.drawLine(
      Offset(size.width, 0),
      Offset(size.width, verticalLength),
      paint,
    );

    // 左下
    canvas.drawLine(
      Offset(0, size.height),
      Offset(0, size.height - verticalLength),
      paint,
    );
    canvas.drawLine(
      Offset(0, size.height),
      Offset(horizontalLength, size.height),
      paint,
    );

    // 右下
    canvas.drawLine(
      Offset(size.width, size.height),
      Offset(size.width - horizontalLength, size.height),
      paint,
    );
    canvas.drawLine(
      Offset(size.width, size.height),
      Offset(size.width, size.height - verticalLength),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
