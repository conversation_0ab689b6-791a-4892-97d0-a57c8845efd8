import 'dart:async';

import 'package:flutter/material.dart';

import '../../flavor/flavor.dart';
import 'my_app.dart';
import 'package:flutter/services.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 鎖定橫向
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeLeft,
    DeviceOrientation.landscapeRight,
  ]);

  // 可選：隱藏狀態列進入沉浸模式
  // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);

  FlavorConfig(
    flavor: Flavor.kidProd,
    values: FlavorValues(
      baseUrl: "",
      signalRUrl: '',
      shareUrl: '',
    ),
  );

  runZonedGuarded(() async {
    await initialSetting();
    runApp(MyApp());
  }, (obj, _) {});
}
