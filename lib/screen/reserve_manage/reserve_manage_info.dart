import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../resource/MyColor.dart';
import '../../util/StringUtils.dart';
import '../../widget/container_with_radius.dart';

class ReserveManageInfo extends StatelessWidget {
  final String date; //日期
  final String week; //星期
  final String orderId; //訂單編號
  final String className; //課程名稱
  final String memberName; //會員名稱
  final String memberPhone; //會員手機
  final String beautician; //美容師
  final String customerSelectionType; //指定情況(ex:客戶指定)
  final String classDate; //課程日期
  final double price; //金額
  const ReserveManageInfo({
    super.key,
    required this.date,
    required this.week,
    required this.orderId,
    required this.className,
    required this.memberName,
    required this.memberPhone,
    required this.beautician,
    required this.customerSelectionType,
    required this.classDate,
    required this.price,
  });

  @override
  Widget build(BuildContext context) {
    final titleStyle = TextStyle(
      fontSize: 32.sp,
      color: MyColors.green_129_128_94,
    );
    return Padding(
      padding:  EdgeInsets.only(top: 69.h,left: 41.w,right: 41.w,bottom: 20.h),
      child: Column(

        children: [
          //日期
          Text(date, style: titleStyle),
          SizedBox(height: 10.h),

          //星期
          Text(week, style: titleStyle),

          SizedBox(height: 50.5.h),
          Divider(thickness: 1, height: 1),
          SizedBox(height: 23.5.h),

          //訂單編號
          Align(
              alignment: Alignment.centerLeft,
              child: Text('訂單編號：$orderId', style: titleStyle)),
          SizedBox(height: 24.h),

          //課程名稱
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
              className,
              style: titleStyle.copyWith(
                fontSize: 28.sp,
                color: MyColors.brown_57_54_18,
                overflow: TextOverflow.ellipsis,
              ),
              maxLines: 2,
            ),
          ),
          SizedBox(height: 29.h),


          //會員
          Row(
            children: [
              Text(
                  '會員：',
                  style: titleStyle
              ),

              //會員名稱
              Text(
                memberName,
                style: titleStyle.copyWith(
                    fontSize: 28.sp,
                    color: MyColors.brown_57_54_18,
                    overflow: TextOverflow.ellipsis,
                    fontWeight: FontWeight.w700
                ),
                maxLines: 2,
              ),

              Spacer(),

              Icon(Icons.arrow_forward_ios_rounded,size: 30.w,)
            ],
          ),

          SizedBox(height: 6.h,),


          ///會員手機
          Row(
            children: [

              //填充用
              Opacity(
                opacity: 0,
                child: Text(
                    '會員：',
                    style: titleStyle
                ),
              ),

              //會員名稱
              Text(
                  memberPhone,
                  style: titleStyle
              ),


            ],
          ),

          SizedBox(height: 26.h,),

          ///美容師
          Row(
            children: [
              Text(
                  '美容師：',
                  style: titleStyle
              ),

              //會員名稱
              Text(
                  beautician,
                  style: titleStyle
              ),

              SizedBox(width: 16.w,),

              //指定狀態
              Text(
                  customerSelectionType ,
                  style: titleStyle.copyWith(fontSize: 23.sp,color: MyColors.grey_119_119_119)
              ),
            ],
          ),

          SizedBox(height: 30.h,),

          ///日期
          Text(
              '日期：$classDate' ,
              style: titleStyle.copyWith(fontSize: 25.sp)
          ),

          SizedBox(height: 29.h,),

          ///金額
          Align(
            alignment: Alignment.centerLeft,
            child: Text(
                '\$ ${StringUtils.formatMoneyForDouble(price)}',
                style: titleStyle.copyWith(fontSize: 25.sp,color: MyColors.orange_240_149_68,fontWeight: FontWeight.w700)
            ),
          ),

          SizedBox(height: 45.h,),

          ///付款按鈕
          _baseBtn((){},'付款'),
          SizedBox(height: 13.h,),

          ///取消預約按鈕
          _baseBtn((){},'取消預約'),
          SizedBox(height: 13.h,),

          ///已到店按鈕
          _baseBtn((){},'已到店'),
          SizedBox(height: 13.h,),

          ///No Show按鈕
          _baseBtn((){},'No Show'),
          SizedBox(height: 13.h,),

          ///編輯按鈕
          _baseBtn((){},'編輯'),
          SizedBox(height: 13.h,),
        ],
      ),
    );
  }
}

///base btn
Widget _baseBtn(VoidCallback onTap, String title, ) {
  return ContainerWithRadius(
    onTap: onTap,
    isHaveBorder: true,
    w: 460.w,
    h: 63.h,
    r: 20.r,
    color: Colors.white,
    boxShadow: [
      BoxShadow(
        color: const Color(0x14000000),
        offset: const Offset(3, 6),
        blurRadius: 10.r,
      ),
    ],
    child: Center(
      child: Text(
        title,
        style: TextStyle(color: MyColors.green_129_128_94, fontSize: 25.sp),
      ),
    ),
  );
}
