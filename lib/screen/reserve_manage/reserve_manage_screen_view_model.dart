import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../model/member/member_reserve_model.dart';
import '../../model/widget_model/drop_down_model.dart';

class ReserveManageScreenViewModel with ChangeNotifier{
  RefreshController refreshController = RefreshController();
  TextEditingController phoneController = TextEditingController(); //手機號碼
  TextEditingController classNameController = TextEditingController(); //課程名稱
  TextEditingController memberNameController = TextEditingController(); //會員名稱

  //-----|日期|-----
  DateTime rangeStartDate = DateTime.now();
  DateTime rangeEndDate = DateTime.now();

  String startStr = '';
  String endStr = '';
  String year = '';

  //下拉
  DropDownModel? statueDropDownModel; //訂單狀態下拉model
  DropDownModel? sourceDropDownModel; //訂單來源下拉model

  //-----|dropDown|-----
  //訂單狀態下拉資料
  List<DropDownModel> statueDropDownList = [
    DropDownModel(id: 0, code: '', name: '全部'),
    DropDownModel(id: 1, code: '', name: '已到店'),
    DropDownModel(id: 2, code: '', name: '取消預約'),
    DropDownModel(id: 3, code: '', name: 'No show'),
    DropDownModel(id: 4, code: '', name: '(未處理)'),
  ];

  //訂單來源下拉資料
  List<DropDownModel> sourceDropDownList = [
    DropDownModel(id: 0, code: '', name: '全部'),
    DropDownModel(id: 1, code: '', name: '線上預約'),
    DropDownModel(id: 2, code: '', name: 'POS預約'),
  ];


  ///預約列表資料
  //訂單狀態說明(暫定：1 線上，2 POS)
  List<MemberReserveModel> memberReserveModel = [
    MemberReserveModel(
      orderId: 'A123456879',
      reserveId: 'HR1234588',
      orderTime: '2025-02-22 11:15',
      orderClassTime: '2025-03-29 15:00~16:15',
      className: '臉部/保濕系列',
      classNameInfo: '絲潤美肌課程(75分鐘)',
      memberName: '王小美',
      memberPhone: '0912345678',
      beautician: '王雨晴',
      customerSelectionType: '客戶指定',
      orderStatue: '(未處理)',
      orderStatueInfo: '1',
      week: '星期一',
      price: 3500,
      isClassConfirm: true,
    ),
    MemberReserveModel(
      orderId: 'A123456879',
      reserveId: 'HR1234588',
      orderTime: '2025-02-22 11:15',
      orderClassTime: '2025-03-29 15:00~16:15',
      className: '臉部/保濕系列',
      classNameInfo: '絲潤美肌課程(75分鐘)',
      memberName: '王小美',
      memberPhone: '0912345678',
      beautician: '王雨晴',
      customerSelectionType: '客戶指定',
      orderStatue: '已到店',
      orderStatueInfo: '2',
        week: '星期一',
        price: 3500,
      isClassConfirm: false,
    ),
    MemberReserveModel(
      orderId: 'A123456879',
      reserveId: 'HR1234588',
      orderTime: '2025-02-22 11:15',
      orderClassTime: '2025-03-29 15:00~16:15',
      className: '臉部/保濕系列',
      classNameInfo: '絲潤美肌課程(75分鐘)',
      memberName: '王小美',
      memberPhone: '0912345678',
      beautician: '王雨晴',
      customerSelectionType: '客戶指定',
      orderStatue: '已到店',
      orderStatueInfo: '1',
        week: '星期一',
        price: 3500,
       isClassConfirm: false,
    ),
    MemberReserveModel(
      orderId: 'A123456879',
      reserveId: 'HR1234588',
      orderTime: '2025-02-22 11:15',
      orderClassTime: '2025-03-29 15:00~16:15',
      className: '臉部/保濕系列',
      classNameInfo: '絲潤美肌課程(75分鐘)',
      memberName: '王小美',
      memberPhone: '0912345678',
      beautician: '陳小春',
      customerSelectionType: '客戶指定',
      orderStatue: 'No show',
      orderStatueInfo: '1',
        week: '星期一',
        price: 3500,
       isClassConfirm: true,
    ),
    MemberReserveModel(
      orderId: 'A123456879',
      reserveId: 'HR1234588',
      orderTime: '2025-02-22 11:15',
      orderClassTime: '2025-03-29 15:00~16:15',
      className: '臉部/保濕系列',
      classNameInfo: '絲潤美肌課程(75分鐘)',
      memberName: '王小美',
      memberPhone: '0912345678',
      beautician: '陳小春',
      customerSelectionType: '客戶指定',
      orderStatue: '已到店',
      orderStatueInfo: '2',
        week: '星期一',
        price: 3500,
       isClassConfirm: false,
    ),
  ];
}