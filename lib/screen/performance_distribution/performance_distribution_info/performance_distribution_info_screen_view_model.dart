import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';

import '../../../model/performance/performance_info_model.dart';
import '../../../model/widget_model/drop_down_model.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/base_title_list_bar/performance_distribution_info_base_title_bar.dart';

class PerformanceDistributionInfoScreenViewModel with ChangeNotifier {

  PerformanceDistributionInfoScreenViewModel() {
    initProductModelMap();
  }

  void initProductModelMap() {
    productModelMap[ProductType.product] = productModel;
    productModelMap[ProductType.classes] = classModel;
    productModelMap[ProductType.topUp] = topUpModel;
  }

  TextEditingController percentController = TextEditingController();  //分配比例
  TextEditingController percentPriceController = TextEditingController();  //分配金額

  final Map<ProductType, List<PerformanceMemberModel>> _backupPerformanceMemberMap = {};  //備份員工表


  Map<ProductType, List<PerformanceInfoModel>> productModelMap = {};
  Map<ProductType, List<DropDownModel?>> staffDropDownSelectedMap = {};
  Map<ProductType, List<TextEditingController>> percentControllerMap = {};
  Map<ProductType, List<TextEditingController>> percentPriceControllerMap = {};




  final Map<ProductType, bool> _editMap = {
    ProductType.product: false,
    ProductType.classes: false,
    ProductType.topUp: false,
  };


  bool isEdit(ProductType type) => _editMap[type] ?? false;



  //下拉
  DropDownModel? staffDropDownModel; //員工下拉model

  //-----|dropDown|-----
  //員工下拉資料
  List<DropDownModel> staffDropDownList = [
    DropDownModel(id: 0, code: '', name: '陳小美'),
    DropDownModel(id: 1, code: '', name: '張曉麗'),
    DropDownModel(id: 2, code: '', name: '曹雪芹'),
  ];


  //提取業績人員列表元件顏色
  Color getStaffListWidgetColor(ProductType productType) {
    switch (productType) {
      case ProductType.product:
        return MyColors.blue236_244_245;
      case ProductType.classes:
        return  MyColors.green_247_250_238;
      case ProductType.topUp:
        return  MyColors.orange_251_244_238;
    }

  }

  ///商品資料
  List<PerformanceInfoModel> productModel = [
    PerformanceInfoModel(
      skuCode: 'S0123',
      productName: '商品名稱，'*100,
      originalPrice: 1500,
      purchaseQuantity: 2,
      actualTotalSalesPrice: 3000,
      actualSalesPrice: 7290,
      performanceMemberModel: [
        PerformanceMemberModel(staffName: '陳小美', percent: 50, percentPrice: 1500, staffId: '0'),
        PerformanceMemberModel(staffName: '張曉麗', percent: 50, percentPrice: 1500, staffId: '1'),
      ],
    ),
  ];

  ///課程資料
  List<PerformanceInfoModel> classModel = [
    PerformanceInfoModel(
      skuCode: 'S0123',
      productName: '課程名稱，'*100,
      originalPrice: 1500,
      purchaseQuantity: 2,
      actualTotalSalesPrice: 3000,
      actualSalesPrice: 7290,
      performanceMemberModel: [
        PerformanceMemberModel(staffName: '陳小美', percent: 50, percentPrice: 1500, staffId: '0'),
        PerformanceMemberModel(staffName: '張曉麗', percent: 50, percentPrice: 1500, staffId: '1'),
      ],
    ),
  ];

  ///儲值金資料
  List<PerformanceInfoModel> topUpModel = [
    PerformanceInfoModel(
      skuCode: 'S0123',
      productName: '儲值金名稱，'*100,
      originalPrice: 1500,
      purchaseQuantity: 2,
      actualTotalSalesPrice: 3000,
      actualSalesPrice: 7290,
      performanceMemberModel: [
        PerformanceMemberModel(staffName: '陳小美', percent: 50, percentPrice: 1500, staffId: '0'),
        PerformanceMemberModel(staffName: '張曉麗', percent: 50, percentPrice: 1500, staffId: '1'),
      ],
    ),
  ];

  ///
  void setEdit(ProductType type, bool value) {
    _editMap[type] = value;
    notifyListeners();
  }

  ///
  void toggleEdit(ProductType type) {
    _editMap[type] = !(_editMap[type] ?? false);
    notifyListeners();
  }

  ///初始化編輯資料
  void initFromProductModel(ProductType productType, List<PerformanceMemberModel> memberList) {
    // 備份（只備份一次）
    _backupPerformanceMemberMap.putIfAbsent(
      productType,
          () => memberList.map((e) => e.copy()).toList(), // 深拷貝避免原始資料被改動
    );


    staffDropDownSelectedMap[productType] ??= [];
    percentControllerMap[productType] ??= [];
    percentPriceControllerMap[productType] ??= [];

    // 清空舊的 List
    staffDropDownSelectedMap[productType]!.clear();
    percentControllerMap[productType]!.clear();
    percentPriceControllerMap[productType]!.clear();

    // 新建暫存容器
    final newPercentControllers = <TextEditingController>[];
    final newPercentPriceControllers = <TextEditingController>[];
    final newStaffDropDown = <DropDownModel?>[];

    for (var member in memberList) {
      final matched = staffDropDownList.firstWhereOrNull(
            (e) => e.id.toString() == member.staffId,
      );

      newStaffDropDown.add(matched);
      newPercentControllers.add(TextEditingController(text: member.percent.toString()));
      newPercentPriceControllers.add(TextEditingController(text: member.percentPrice.toString()));
    }

    // 替換成新 List
    staffDropDownSelectedMap[productType] = newStaffDropDown;
    percentControllerMap[productType] = newPercentControllers;
    percentPriceControllerMap[productType] = newPercentPriceControllers;

    notifyListeners();
  }



  ///刪除員工
  void removePerformanceMember(ProductType productType, int productIndex, int staffIndex) {
    productModelMap[productType]?[productIndex].performanceMemberModel.removeAt(staffIndex);

    final memberList = productModelMap[productType]?[productIndex].performanceMemberModel ?? [];

    initFromProductModel(productType, memberList);

    notifyListeners();
  }


  ///返回還原員工表
  void restorePerformanceMember(ProductType productType, int productIndex) {
    final backupList = _backupPerformanceMemberMap[productType];
    if (backupList == null) return;

    productModelMap[productType]?[productIndex].performanceMemberModel =
        backupList.map((e) => e.copy()).toList();

    initFromProductModel(productType, backupList);

    notifyListeners();
  }

///insert資料進員工表
  void insertProduct(ProductType type, int index) {
    final newMember = PerformanceMemberModel(
      staffName: '',
      staffId: '',
      percent: 0,
      percentPrice: 0,
    );

    switch (type) {
      case ProductType.product:
        productModel[index].performanceMemberModel.add(newMember);
        break;
      case ProductType.classes:
        classModel[index].performanceMemberModel.add(newMember);
        break;
      case ProductType.topUp:
        topUpModel[index].performanceMemberModel.add(newMember);
        break;
    }

    initFromProductModel(type, getTargetList(type, index));
  }


  ///
  List<PerformanceMemberModel> getTargetList(ProductType type, int index) {
    switch (type) {
      case ProductType.product:
        return productModel[index].performanceMemberModel;
      case ProductType.classes:
        return classModel[index].performanceMemberModel;
      case ProductType.topUp:
        return topUpModel[index].performanceMemberModel;
    }
  }


  ///儲存邏輯
  void savePerformanceMember(ProductType productType, int productIndex) {
    final modelList = productModelMap[productType]?[productIndex].performanceMemberModel;
    final controllerList = percentControllerMap[productType];
    final priceControllerList = percentPriceControllerMap[productType];
    final dropdownList = staffDropDownSelectedMap[productType];

    if (modelList == null || controllerList == null || priceControllerList == null || dropdownList == null) return;

    final newList = <PerformanceMemberModel>[];

    for (int i = 0; i < dropdownList.length; i++) {
      final staff = dropdownList[i];
      if (staff == null || staff.name.trim().isEmpty) continue;

      final percent = double.tryParse(controllerList[i].text) ?? 0.0;
      final price = double.tryParse(priceControllerList[i].text) ?? 0.0;

      newList.add(PerformanceMemberModel(
        //TODO:staffId?
        staffId: staff.id.toString(),
        staffName: staff.name,
        percent: percent,
        percentPrice: price,
      ));
    }

    // 若 newList 為空，不儲存
    if (newList.isEmpty) return;

    // 若資料內容與原本一樣，就不儲存
    final isSame = _isSameList(modelList, newList);
    if (isSame) return;

    // 否則才覆蓋與備份
    productModelMap[productType]?[productIndex].performanceMemberModel = newList;
    _backupPerformanceMemberMap[productType] = newList.map((e) => e.copy()).toList();

    notifyListeners();
  }



  bool _isSameList(List<PerformanceMemberModel> oldList, List<PerformanceMemberModel> newList) {
    if (oldList.length != newList.length) return false;
    for (int i = 0; i < oldList.length; i++) {
      final a = oldList[i];
      final b = newList[i];
      if (a.staffId != b.staffId ||
          a.staffName != b.staffName ||
          a.percent != b.percent ||
          a.percentPrice != b.percentPrice) {
        return false;
      }
    }
    return true;
  }


}
