import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen_view_model.dart';

import '../../../model/member/member_history_order_model.dart';
import '../../../resource/MyColor.dart';
import '../../../widget/app_bar/sbar_app_bar_with_return.dart';
import '../../../widget/base_title_list_bar/performance_distribution/performance_distribution_base_list_bar.dart';
import '../../../widget/base_title_list_bar/performance_distribution/performance_distribution_info_base_list_bar.dart';
import '../../../widget/base_title_list_bar/performance_distribution_info_base_title_bar.dart';
import '../../../widget/member/member_info_bar.dart';

class PerformanceDistributionInfoScreen extends StatefulWidget {
  final MemberHistoryOrderModel model;

  const PerformanceDistributionInfoScreen({super.key, required this.model});

  @override
  State<PerformanceDistributionInfoScreen> createState() =>
      _PerformanceDistributionInfoScreenState();
}

class _PerformanceDistributionInfoScreenState
    extends State<PerformanceDistributionInfoScreen> {
  final vm = PerformanceDistributionInfoScreenViewModel();

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: SafeArea(
        child: Scaffold(
          body: SbarAppBarWithReturn(
            title: '明細業績分配',
            childScreen: SingleChildScrollView(
              padding: EdgeInsets.only(top: 40.h, left: 26.w, right: 26.w),
              physics: BouncingScrollPhysics(),
              child: Consumer<PerformanceDistributionInfoScreenViewModel>(
                  builder: (context, data, _) {
                    return Column(
                      children: [

                        ///業績分配列表區塊
                        _performanceArea(),
                        SizedBox(height: 18.h),

                        //TODO:判斷是否含有以下type

                        ///商品表
                        PerformanceDistributionInfoBaseTitleBar(
                          productType: ProductType.product,),
                        PerformanceDistributionInfoBaseListBar(
                          vm: vm,
                          productType: ProductType.product,
                          performanceInfoModels: vm.productModel,),

                        ///課程表
                        PerformanceDistributionInfoBaseTitleBar(
                          productType: ProductType.classes,),
                        PerformanceDistributionInfoBaseListBar(
                          vm: vm,
                          productType: ProductType.classes,
                          performanceInfoModels: vm.classModel,),

                        ///儲值金表
                        PerformanceDistributionInfoBaseTitleBar(
                          productType: ProductType.topUp,),
                        PerformanceDistributionInfoBaseListBar(
                          vm: vm,
                          productType: ProductType.topUp,
                          performanceInfoModels: vm.topUpModel,),


                        SizedBox(height: 50.h),
                      ],
                    );
                  }

              ),
            ),
          ),
        ),
      ),
    );
  }

  //業績分配列表區塊
  Widget _performanceArea() {
    return Column(children: [_performanceTitleBar(), _performanceList()]);
  }

  //業績分配列表
  Widget _performanceList() {
    return PerformanceDistributionBaseListBar(
      isInfo: true,
      isTitleBar: false,
      isHaveOnTap: false,
      orderId: widget.model.orderId,
      checkOutTime: widget.model.checkOutTime,
      somPrice: widget.model.somPrice,
      price: widget.model.price,
      memberName: widget.model.memberName,
      operatorName: widget.model.operatorName,
      backgroundColor: Colors.white,
      salesName: widget.model.salesMane ?? '',
      onTap: () {},
      invoiceId: widget.model.invoiceId,
      priceInfo: widget.model.priceInfo,
      orderStatue: widget.model.orderStatue,
      orderStatueCode: widget.model.orderStatueCode,
      unPaidPrice: widget.model.unPaidPrice,
    );
  }

  ///業績分配列表標頭
  Widget _performanceTitleBar() {
    return PerformanceDistributionBaseListBar(
      isInfo: true,
      isTitleBar: true,
      orderId: '訂單編號',
      invoiceId: '發票號碼',
      checkOutTime: '結帳時間',
      somPrice: '銷售訂單母單',
      price: '本次付款金額',
      orderStatue: '訂單狀態',
      orderStatueCode: 0,
      unPaidPrice: '未付款金額',
      salesName: '銷售人員',
      memberName: '會員',
      operatorName: '操作員',
      priceInfo: '',
      backgroundColor: MyColors.grey_247_247_247,
      onTap: () {},
    );
  }
}
