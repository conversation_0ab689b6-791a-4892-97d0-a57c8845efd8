import 'package:flutter/cupertino.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../model/member/member_history_order_model.dart';
import '../../model/widget_model/drop_down_model.dart';

class PerformanceDistributionScreenViewModel with ChangeNotifier{
  RefreshController refreshController = RefreshController();
  TextEditingController orderIdController = TextEditingController(); //訂單編號
  TextEditingController salesController = TextEditingController(); //銷售人員

  //-----|日期|-----
  DateTime rangeStartDate = DateTime.now();
  DateTime rangeEndDate = DateTime.now();

  String startStr = '';
  String endStr = '';
  String year = '';

  //下拉
  DropDownModel? typeDropDownModel; //類別下拉model

  //-----|dropDown|-----
  //類別下拉資料
  List<DropDownModel> typedDropDownList = [
    DropDownModel(id: 0, code: '', name: '全部'),
    DropDownModel(id: 1, code: '', name: 'A'),
    DropDownModel(id: 2, code: '', name: 'B'),
    DropDownModel(id: 3, code: '', name: 'C'),
  ];

  //業績分配資料
  List<MemberHistoryOrderModel> memberHistoryOrderModel = [
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,  //(暫定：０為未完成付款，１為已付款)
      unPaidPrice: '0',
      memberName: '王小美',
      operatorName: '小美',
      salesMane: '陳美月'
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,  //(暫定：０為未完成付款，１為已付款)
      unPaidPrice: '0',
      memberName: '孫總',
      operatorName: '阿孫',
        salesMane: '陳美月'
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '28000',
      priceInfo: '現金',
      orderStatue: '未完成付款',
      orderStatueCode: 0,  //(暫定：０為未完成付款，１為已付款)
      unPaidPrice: '28000',
      memberName: '洪詩詩',
      operatorName: '詩詩',
        salesMane: '陳美月'
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '50000',
      price: '35000',
      priceInfo: '百貨優惠券,現金',
      orderStatue: '完成付款',
      orderStatueCode: 1,  //(暫定：０為未完成付款，１為已付款)
      unPaidPrice: '0',
      memberName: '金小五',
      operatorName: '小五',
        salesMane: '陳美月'
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: 'LINE Pay',
      orderStatue: '未完成付款',
      orderStatueCode: 0,  //(暫定：０為未完成付款，１為已付款)
      unPaidPrice: '35000',
      memberName: '劉曉琪',
      operatorName: '小琪',
        salesMane: '陳美月'
    ),
    MemberHistoryOrderModel(
      orderId: "A123456789",
      invoiceId: "FE12345687",
      checkOutTime: '2025-02-21 15:20',
      somPrice: '35000',
      price: '35000',
      priceInfo: '現金,信用卡',
      orderStatue: '完成付款',
      orderStatueCode: 1,  //(暫定：０為未完成付款，１為已付款)
      unPaidPrice: '0',
      memberName: '陳小羊',
      operatorName: '小羊',
        salesMane: '陳美月'
    ),
  ];

}