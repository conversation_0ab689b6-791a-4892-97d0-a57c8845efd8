import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import 'package:sbar_pos/screen/performance_distribution/performance_distribution_info/performance_distribution_info_screen.dart';
import 'package:sbar_pos/screen/performance_distribution/performance_distribution_screen_view_model.dart';
import 'package:sbar_pos/screen/start/my_app.dart';

import '../../gen/r.dart';
import '../../logger/my_print.dart';
import '../../model/member/member_history_order_model.dart';
import '../../model/widget_model/drop_down_model.dart';
import '../../resource/MyColor.dart';
import '../../util/MyRoutes.dart';
import '../../widget/base_title_list_bar/performance_distribution/performance_distribution_base_list_bar.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/drop_down/drop_down_button.dart';
import '../../widget/hint/empty_hint.dart';
import '../../widget/textField/base_text_field.dart';
import '../../widget/textField/sbar_style_range_date_Text_field.dart';
import '../main/drawer/base_scaffold_with_drawer.dart';

class PerformanceDistributionScreen extends StatefulWidget {
  const PerformanceDistributionScreen({super.key});

  @override
  State<PerformanceDistributionScreen> createState() =>
      _PerformanceDistributionScreenState();
}

class _PerformanceDistributionScreenState
    extends State<PerformanceDistributionScreen> {
  final vm = PerformanceDistributionScreenViewModel();

  @override
  void initState() {
    _initRangeDate();
    super.initState();
  }

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  ///初始化日期區間
  void _initRangeDate() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      vm.startStr = startOfMonth.toDateYMDString();
      vm.endStr = endOfMonth.toDateYMDString();

      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,

      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: BaseScaffoldWithDrawer(
          title: '業績分配',
          child: Padding(
            padding: EdgeInsets.only(
              top: 142.h,
              left: 16.w,
              right: 23.w,
              bottom: 23.h,
            ),
            child: Column(
              children: [
                ///搜尋列
                _searchBar(),
                SizedBox(height: 22.h),

                ///業績分配列表區塊
                Expanded(child: _performanceArea()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  //業績分配列表區塊
  Widget _performanceArea() {
    return Column(
      children: [
        _performanceTitleBar(),
        Expanded(child: _performanceList()),
      ],
    );
  }

  //業績分配列表
  Widget _performanceList() {
    return Selector<
      PerformanceDistributionScreenViewModel,
      List<MemberHistoryOrderModel>
    >(
      selector: (context, vm) => vm.memberHistoryOrderModel,
      builder: (context, memberHistoryOrderModel, _) {

        return SmartRefresher(
          controller: vm.refreshController,
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: () async {
            await Future.delayed(300.ms);
            vm.refreshController.refreshCompleted();
          },
          child: memberHistoryOrderModel.isEmpty
              ? ListView(
            // 用 ListView 包住 EmptyHint，這樣才能下拉
            children: [
              SizedBox(
                height:
                MediaQuery.of(context).size.height * 0.3, // 根據螢幕高度調整
              ),
              Center(child: EmptyHint(hint: '尚無業績分配紀錄')),
            ],
          )
              : ListView.builder(
            padding: EdgeInsets.only(bottom: 50.h),
            physics: BouncingScrollPhysics(),
            itemCount: memberHistoryOrderModel.length,
            itemBuilder: (context, index) {
              final model = memberHistoryOrderModel[index];
              return PerformanceDistributionBaseListBar(
                isTitleBar: false,
                orderId: model.orderId,
                checkOutTime: model.checkOutTime,
                somPrice: model.somPrice,
                price: model.price,
                memberName: model.memberName,
                operatorName: model.operatorName,
                backgroundColor: Colors.white,
                salesName: model.salesMane ?? '',
                onTap: () {
                  pushTo(
                    PageName.performanceDistributionInfo.toString(),
                    builder: (context) =>
                        PerformanceDistributionInfoScreen(model: model),
                  );
                },
                invoiceId: model.invoiceId,
                priceInfo: model.priceInfo,
                orderStatue: model.orderStatue,
                orderStatueCode: model.orderStatueCode,
                unPaidPrice: model.unPaidPrice,
              );
            },
          ),
        );
      },
    );
  }

  ///業績分配列表標頭
  Widget _performanceTitleBar() {
    return PerformanceDistributionBaseListBar(
      isTitleBar: true,
      orderId: '訂單編號',
      invoiceId: '發票號碼',
      checkOutTime: '結帳時間',
      somPrice: '銷售訂單母單',
      price: '本次付款金額',
      orderStatue: '訂單狀態',
      orderStatueCode: 0,
      unPaidPrice: '未付款金額',
      salesName: '銷售人員',
      memberName: '會員',
      operatorName: '操作員',
      priceInfo: '',
      backgroundColor: MyColors.grey_247_247_247,
      onTap: () {},
    );
  }

  ///搜尋列
  Widget _searchBar() {
    return ContainerWithRadius(
      isHaveBorder: false,
      w: double.infinity.w,
      h: 86.h,
      r: 20.r,
      color: MyColors.green_157_193_65.withOpacity(0.3),
      child: Padding(
        padding: EdgeInsets.only(right: 12.w, left: 25.w),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            ///類別（下拉選單輸入框）
            _typeDropDown(),
            SizedBox(width: 11.w),

            ///日期選擇
            _dateTextField(),
            SizedBox(width: 11.w),

            ///訂單編號輸入框
            BaseTextField(hint: '請輸入訂單編號', controller: vm.orderIdController),
            SizedBox(width: 11.w),

            ///銷售人員輸入框
            BaseTextField(hint: '請輸入銷售人員', controller: vm.salesController),
            SizedBox(width: 11.w),

            ///搜尋按鈕
            ContainerWithRadius(
              isHaveBorder: true,
              w: 196.w,
              h: 62.w,
              r: 20.r,
              color: MyColors.green_157_193_65,
              child: Center(
                child: Text(
                  '搜尋',
                  style: TextStyle(fontSize: 25.sp, color: Colors.white),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///類別下拉
  Widget _typeDropDown() {
    return Selector<PerformanceDistributionScreenViewModel, DropDownModel?>(
      selector: (context, vm) => vm.typeDropDownModel,
      shouldRebuild: (p, n) => true,
      builder: (context, typeDropDownModel, _) {
        return ContainerWithRadius(
          w: 218.w,
          h: 54.h,
          r: 20.r,
          color: Colors.white,
          isHaveBorder: true,
          child: Padding(
            padding: EdgeInsets.only(
              left: 15.w,
              right: 8.w,
              top: 7.h,
              bottom: 7.h,
            ),
            child: MyDropdownButtonT<DropDownModel>(
              tailWidget: Image(
                image: R.image.icon_arrow_down_with_circle(),
                width: 40.w,
                height: 40.w,
              ),
              isExpanded: false,
              underline: SizedBox.shrink(),
              hint: Text(
                '請選擇類型',
                style: TextStyle(
                  fontSize: 23.sp,
                  color: MyColors.grey_60_60_67.withOpacity(0.3),
                ),
              ),
              textStyle: TextStyle(
                fontSize: 23.sp,
                color: MyColors.brown_57_54_18,
              ),
              selectedItem: vm.typeDropDownModel,
              labelSelector: (item) {
                return item.name;
              },
              items: vm.typedDropDownList,
              onChanged: (DropDownModel? value) async {
                vm.typeDropDownModel = value;

                (context as Element).markNeedsBuild();
              },
            ),
          ),
        );
      },
    );
  }

  ///日期選擇輸入框
  Widget _dateTextField() {
    return SbarStyleRangeDateTextField(
      isCanSelectAfterThisYear: true,
      rangeStartDate: vm.rangeStartDate,
      rangeEndDate: vm.rangeEndDate,
      startStr: vm.startStr,
      endStr: vm.endStr,
      onRangeSubmit: (start, end) {
        setState(() {
          vm.rangeStartDate = start;
          vm.rangeEndDate = end;
          vm.startStr = start.toDateYMDString();
          vm.endStr = end.toDateYMDString();
          myPrint('start: $start');
          myPrint('end: $end');
        });
      },
      onSpeedSelectMonthSubmit: (int year, int month) {
        vm.year = year.toString();

        // 該月的最後一天
        final lastDay = DateTime(year, month + 1, 0);

        // 格式化為 yyyy-MM-dd
        vm.startStr = "$year-${month.toString().padLeft(2, '0')}-01";
        vm.endStr =
            "$year-${month.toString().padLeft(2, '0')}-${lastDay.day.toString().padLeft(2, '0')}";

        myPrint("開始日期: ${vm.startStr}");
        myPrint("結束日期: ${vm.endStr}");

        setState(() {});
      },
    );
  }
}
