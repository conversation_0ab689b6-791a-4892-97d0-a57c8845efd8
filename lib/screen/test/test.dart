import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class Test extends StatefulWidget {
  const Test({super.key});

  @override
  State<Test> createState() => _TestState();
}

class _TestState extends State<Test> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(top: 200.h),
        child: _openAndCloseTimePicker(),
        // SizedBox(
        //   width: 1240,
        //   height: 72,
        //   child: RoundBorderContainer(
        //     color: Colors.white,
        //     padding: const EdgeInsets.all(16),
        //     child: Row(
        //       crossAxisAlignment: CrossAxisAlignment.center,
        //       children: [
        //         Text(
        //           '營業時段：',
        //           style: Theme.of(context).textTheme.titleMedium,
        //         ),
        //
        //         Padding(
        //           padding: const EdgeInsets.symmetric(horizontal: 8),
        //           child: SizedBox(
        //             width: 112,
        //             child: OutlineButton(
        //               radius: 3,
        //               backgroundColor: Colors.white,
        //               text: 'fff',
        //               //arrangement.startTime == null ? '--' : MaterialLocalizations.of(context).formatTimeOfDay(arrangement.startTime!),
        //               onPressed: () async {
        //
        //                 //Show TimeInDay picker.
        //                 TimeOfDay? selectedTime = await showTimePicker(
        //                   initialTime: TimeOfDay.now(),
        //                   //reserve_calender_widget.arrangement.startTime ?? TimeOfDay(hour: 9, minute: 0),
        //                   context: context,
        //                   builder: (BuildContext context, Widget? child) {
        //                     return MediaQuery(
        //                       data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
        //                       child: child!,
        //                     );
        //                   },
        //                 );
        //
        //                 if (selectedTime != null) {
        //                   setState(() {
        //                     // reserve_calender_widget.arrangement.startTime = selectedTime;
        //                   });
        //                 }
        //               },
        //             ),
        //           ),
        //         ),
        //
        //         Text('至', style: Theme.of(context).textTheme.titleMedium),
        //
        //         Padding(
        //           padding: const EdgeInsets.symmetric(horizontal: 8.0),
        //           child: SizedBox(
        //             width: 112,
        //             child: OutlineButton(
        //               radius: 3,
        //               backgroundColor: Colors.white,
        //               text: 'vvv',
        //               //arrangement.endTime == null ? '--' : MaterialLocalizations.of(context).formatTimeOfDay(arrangement.endTime!),
        //               onPressed: () async {
        //
        //                 //Show TimeInDay picker.
        //                 TimeOfDay? selectedTime = await showTimePicker(
        //                   initialTime: TimeOfDay.now(),
        //                   // reserve_calender_widget.arrangement.startTime ?? TimeOfDay(hour: 9, minute: 0),
        //                   context: context,
        //                   builder: (BuildContext context, Widget? child) {
        //                     return MediaQuery(
        //                       data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
        //                       child: child!,
        //                     );
        //                   },
        //                 );
        //
        //                 if (selectedTime != null) {
        //                   setState(() {
        //                     // reserve_calender_widget.arrangement.startTime = selectedTime;
        //                   });
        //                 }
        //               },
        //             ),
        //           ),
        //         ),
        //
        //         Text('止', style: Theme.of(context).textTheme.titleMedium),
        //
        //         VerticalDivider(
        //           color: Colors.grey,
        //           width: 32,
        //         ),
        //
        //
        //
        //
        //
        //
        //
        //       ],
        //     ),
        //   ),
        // ),
      ),
    );
  }

  Widget _openAndCloseTimePicker() {
    return Card(
      elevation: 1.5,
      color: Colors.grey.shade300,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8.0),
        child: Row(
          children: [
            Text('開閉店時間', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(width: 16),
            Expanded(
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(3),
                child: InkWell(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      children: [
                        Flexible(
                          child: Text(
                            'ffee'
                          ),
                        ),
                      ],
                    ),
                  ),
                  onTap: () async {
                    //Show TimeInDay picker.
                    TimeOfDay? selectedTime = await showTimePicker(
                      initialTime: TimeOfDay(hour: 9, minute: 0),
                      context: context,
                      builder: (BuildContext context, Widget? child) {
                        return MediaQuery(
                          data: MediaQuery.of(
                            context,
                          ).copyWith(alwaysUse24HourFormat: true),
                          child: child!,
                        );
                      },
                    );

                    if (selectedTime != null) {
                      setState(() {
                        // widget.arrangement.startTime = selectedTime;
                      });
                    }
                  },
                ),
              ),
            ),

            Padding(
              padding: EdgeInsets.symmetric(horizontal: 6),
              child: Text('至', style: Theme.of(context).textTheme.titleMedium),
            ),

            Expanded(
              child: Material(
                color: Colors.white,
                borderRadius: BorderRadius.circular(3),
                child: InkWell(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(children: [Flexible(child: Text('f'))]),
                  ),
                  onTap: () async {
                    //Show TimeInDay picker.
                    TimeOfDay? selectedTime = await showTimePicker(
                      initialTime: TimeOfDay(hour: 9, minute: 0),
                      context: context,
                      builder: (BuildContext context, Widget? child) {
                        return MediaQuery(
                          data: MediaQuery.of(
                            context,
                          ).copyWith(alwaysUse24HourFormat: true),
                          child: child!,
                        );
                      },
                    );

                    if (selectedTime != null) {
                      setState(() {
                        // widget.arrangement.endTime = selectedTime;
                      });
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class OutlineButton extends StatelessWidget {
  const OutlineButton({
    this.text = '',
    this.onPressed,
    this.backgroundColor,
    this.margin = const EdgeInsets.all(0),
    this.padding = const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12),
    this.radius = 0,
    this.elevation = 0,
    this.child,
    this.textStyle,
    super.key,
  });

  final Color? backgroundColor;
  final EdgeInsets margin;
  final EdgeInsets padding;
  final double radius;
  final double elevation;
  final String text;
  final TextStyle? textStyle;
  final Widget? child;
  final Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    TextStyle? useTextStyle =
        textStyle ?? Theme.of(context).textTheme.titleMedium;

    Color useBackgroundColor = backgroundColor ?? Colors.white;

    return Padding(
      padding: margin,
      child: Material(
        clipBehavior: Clip.antiAlias,
        borderRadius: BorderRadius.circular(radius),
        color: useBackgroundColor,
        elevation: elevation,
        child: InkWell(
          child: Container(
            padding: padding,
            decoration: BoxDecoration(
              border: Border.all(
                width: 0.5,
                color: Colors.grey.withOpacity(0.5),
              ),
              borderRadius: BorderRadius.circular(radius),
            ),
            child:
                child ??
                Text(text, style: useTextStyle, textAlign: TextAlign.center),
          ),
          onTap: () {
            onPressed?.call();
          },
        ),
      ),
    );
  }
}

class RoundBorderContainer extends StatelessWidget {
  const RoundBorderContainer({
    this.color = Colors.transparent,
    this.circularRadius = 3,
    this.borderColor = Colors.white12,
    this.borderWidth = 1.0,
    this.padding = EdgeInsets.zero,
    this.child,
    super.key,
  });

  final Color color;
  final double circularRadius;
  final Color borderColor;
  final double borderWidth;
  final EdgeInsets padding;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: color,
        border: Border.all(color: borderColor, width: borderWidth),
        borderRadius: BorderRadius.circular(circularRadius),
      ),
      child: Padding(padding: padding, child: child),
    );
  }
}
