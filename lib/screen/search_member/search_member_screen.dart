import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import 'package:sbar_pos/screen/search_member/search_member_screen_view_model.dart';
import 'package:sbar_pos/widget/shape/circle.dart';

import '../../model/member/member_list_model.dart';
import '../../resource/MyColor.dart';
import '../../widget/base_title_list_bar/member_list_widget.dart';
import '../../widget/base_title_list_bar/member_title_bar.dart';
import '../../widget/btn/return_and_submit_btn.dart';
import '../../widget/btn/scan_btn.dart';
import '../../widget/cached_network_image/error_cached_network_image.dart';
import '../../widget/calender/build_single_date_calender.dart';
import '../../widget/container_with_radius.dart';
import '../../widget/hint/empty_hint.dart';
import '../../widget/number_keyboard/number_keyboard.dart';
import '../../widget/textField/base_text_field.dart';
import '../../widget/textField/my_text_field.dart';
import '../start/my_app.dart';

class SearchMemberScreen extends StatefulWidget {
  final VoidCallback backOnTap; //返回
  final Function(
    String avatar,
    String name,
    String phone,
    String birthday,
    String eMail,
  )
  submitOnTap; //送出
  const SearchMemberScreen({
    super.key,
    required this.backOnTap,
    required this.submitOnTap,
  });

  @override
  State<SearchMemberScreen> createState() => _SearchMemberScreenState();
}

class _SearchMemberScreenState extends State<SearchMemberScreen> {
  final vm = SearchMemberScreenViewModel();
  int _selectedIndex = -1;

  @override
  void dispose() {
    vm.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: vm,
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
        child: Padding(
          padding: EdgeInsets.only(top: 75.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '搜尋會員',
                style: TextStyle(
                  fontSize: 44.sp,
                  color: MyColors.brown_57_54_18,
                ),
              ),
              SizedBox(height: 89.h),

              ContainerWithRadius(
                w: 1176.w,
                h: 600.h,
                r: 20.r,
                color: Colors.white,
                isHaveBorder: true,
                child: Center(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ///輸入框
                      _phoneTextFieldArea(),

                      SizedBox(width: 152.w),

                      ///鍵盤
                      Padding(
                        padding:  EdgeInsets.only(top: 50.h),
                        child: NumberKeyboard(
                          onValueChanged: (String value) {
                            vm.phoneController.text = value;

                            //TODO:到十位數後拋api
                            if(value.length == 10){
                              vm.memberName = '王小美';
                            }
                            setState(() {});
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              Spacer(),

              ///返回與送出按鈕
              ReturnAndSubmitBtn(
                backOnTap: widget.backOnTap,
                submitOnTap: () {
                  //TODO:將api來的值直接insert

                  widget.submitOnTap(
                    '',
                    vm.memberName,
                    vm.phoneController.text,
                    '2002-01-10',
                    '<EMAIL>',
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///電話輸入框
  Widget _phoneTextFieldArea() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '請輸入手機號碼',
          style: TextStyle(fontSize: 25.sp, color: MyColors.green_129_128_94),
        ),
        SizedBox(height: 39.h),
        BaseTextField(
          w: 312.w,
          h: 74.h,
          hint: '',
          controller: vm.phoneController,
          isReadOnly: true,
        ),
        SizedBox(height: 27.h),

        //TODO:從api來
        Text(
          vm.memberName,
          style: TextStyle(fontSize: 44.sp, color: MyColors.brown_57_54_18),
        ),
      ],
    );
  }
}
