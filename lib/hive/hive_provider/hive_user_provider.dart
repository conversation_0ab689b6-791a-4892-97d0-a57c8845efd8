import 'package:collection/collection.dart';
import '../entity/user.dart';
import '../helper/boxes.dart';
import '../../logger/my_print.dart';

class HiveUserProvider {
  ///初始化，若DB內沒有則insert一筆
  static Future<void> initDB() async {
    HiveUser? user = await getUserOrNull();
    if (user == null) {
      await BoxesX.getUser().add(HiveUser.resetToInit());
    }
  }

  ///取得單個 User 如果多個會報錯，如果沒有創建過回傳 Null
  static Future<HiveUser?> getUserOrNull() async {
    List<HiveUser> users = BoxesX.getUser().values.toList();
    //判斷user的長度是否小於等於1
    assert(users.length <= 1);
    //配合套件-collection
    return users.firstOrNull;
  }

  /// 需要確保有一個user，如果沒User會報錯，如果有兩個user也會報錯
  static Future<HiveUser> getUser() async {
    List<HiveUser> users = BoxesX.getUser().values.toList();
    assert(users.length == 1);
    return users.single;
  }

  ///更新user
  ///[user] User
  static Future<void> updateUser(HiveUser user) async {
    return user.save();
  }

  ///顯示user table的資料
  static Future<void> showData() async {
    var user = await getUserOrNull();
    myPrint(user);
  }


}
