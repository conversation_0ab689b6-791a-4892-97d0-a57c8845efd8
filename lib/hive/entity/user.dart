import 'package:hive/hive.dart';

part 'user.g.dart';

/*
  修改或新增HiveObject之後需要下這個指令：flutter packages pub run build_runner build --delete-conflicting-outputs
 */
@HiveType(typeId: 0)
class HiveUser extends HiveObject {
  @HiveField(0)
  String? shopName; //商店名稱
  @HiveField(1)
  String? shopId; //商店代號

  //---|會員相關|---
  @HiveField(2)
  String? memberToken; //memberToken
  @HiveField(3)
  String? account; //帳號
  @HiveField(4)
  String? stuffPermissions; //員工權限
  @HiveField(5)
  String? name; //姓名
  @HiveField(6)
  String? phone; //手機
  @HiveField(7)
  String? email; //email
  @HiveField(8)
  String? memberId;

  HiveUser({
    this.shopName,
    this.shopId,
    this.memberToken,
    this.account,
    this.stuffPermissions,
    this.name,
    this.phone,
    this.email,
    this.memberId,
  });

  ///
  HiveUser.resetToInit() {
    shopName = '';
    shopId = '';
    memberToken = '';
    account = '';
    stuffPermissions = '';
    name = '';
    phone = '';
    email = '';
    memberId = '';
  }

  ///
  HiveUser.fromMap(Map<String, dynamic> json) {
    shopName = json['shopName'];
    shopId = json['shopId'];
    memberToken = json['memberToken'];
    account = json['account'];
    stuffPermissions = json['stuffPermissions'];
    name = json['name'];
    phone = json['phone'];
    email = json['email'];
    memberId = json['memberId'];


  }

  ///
  Map<String, dynamic> toMap() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['shopName'] = shopName;
    data['shopId'] = shopId;
    data['memberToken'] = memberToken;
    data['account'] = account;
    data['stuffPermissions'] = stuffPermissions;
    data['name'] = name;
    data['phone'] = phone;
    data['email'] = email;
    data['memberId'] = memberId;
    return data;
  }

  ///
  @override
  String toString() {
    return 'User{shopName:$shopName,shopId:$shopId,memberToken:$memberToken,account:$account,stuffPermissions:$stuffPermissions,name:$name,phone:$phone,email:$email,memberId:$memberId';
  }
}
