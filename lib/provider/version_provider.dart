import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

class VersionProvider extends ChangeNotifier{
  String version = '';
  String buildVersion = '';

   PackageInfo _packageInfo = PackageInfo(
    appName: 'Unknown',
    packageName: 'Unknown',
    version: 'Unknown',
    buildNumber: 'Unknown',
    buildSignature: 'Unknown',
    installerStore: 'Unknown',
  );


   ///初始化
  Future<void> initPackageInfo() async {
    final info = await PackageInfo.fromPlatform();
      _packageInfo = info;
     version = _packageInfo.version;
     buildVersion = _packageInfo.buildNumber;
     notifyListeners();
  }

}