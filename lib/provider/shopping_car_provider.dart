import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../App.dart';
import '../api/api_service.dart';
import '../hive/hive_provider/hive_user_provider.dart';
import '../model/api_model/res_model/res_employees.dart';
import '../model/api_model/res_model/res_get_pay_methods.dart';
import '../model/api_model/res_model/res_get_prepaid.dart';
import '../model/api_model/res_model/res_get_products.dart';
import '../model/api_model/res_model/res_get_products_types.dart';
import '../model/api_model/res_model/res_get_programs_course_types.dart';
import '../model/api_model/res_model/res_get_programs.dart';
import '../model/base/base_model.dart';
import '../model/class/class_model.dart';
import '../model/class/reserve_class_model.dart';
import '../model/member/member_cash_model.dart';
import '../model/performance/performance_model.dart';
import '../model/product/product_model.dart';
import '../screen/start/my_app.dart';
import '../sqlite/provider/product_shopping_car_record_provider.dart';
import '../widget/dialog/sbar_style_dialog.dart';

class ShoppingCarProvider with ChangeNotifier {
  RefreshController memberWalletCashRefreshController = RefreshController();  //錢包會員儲值金下拉
  RefreshController memberCashRefreshController = RefreshController();  //會員儲值金下拉
  List<String> paymentTypes = []; //付款方式

  TextEditingController invoiceController = TextEditingController(); //發票號碼
  TextEditingController discountController = TextEditingController(); //現場減價

  final Map<String, TextEditingController> paymentControllers = {};

  double _manualDiscount = 0;
  double get manualDiscount => _manualDiscount;

  void setManualDiscount(double value) {
    _manualDiscount = value;
    notifyListeners();
  }


  /// 取得指定付款方式的 Controller，如果沒有就建立
  TextEditingController getController(String paymentType) {
    if (!paymentControllers.containsKey(paymentType)) {
      final controller = TextEditingController();
      controller.addListener(_onPaymentChanged);
      paymentControllers[paymentType] = controller;
    }
    return paymentControllers[paymentType]!;
  }


  void _onPaymentChanged() {
    notifyListeners(); // 輸入金額時自動刷新
  }

  List<ProductModel> tempProductList = [];
  List<ProductModel> tempClassList = [];
  List<ProductModel> _backupProductModel = []; // 搜尋前的暫存清單
  List<ProductModel> _backupClassModel = []; // 搜尋前的暫存清單

  bool mainLoading = true;
  int productSelectedTypeIndex = 0;
  int classSelectedTypeIndex = 0;

  //課程分頁相關
  int currentProgramPage = 0;
  bool hasMoreProgram = true;
  bool isLoadingProgramMore = false;

  //銷售人員
  Map<String, String> staff = {}; // key: staffId, value: staffName

  //商品分頁相關
  int currentProductPage = 0;
  bool hasMoreProduct = true;
  bool isLoadingProductMore = false;

  int discountRes = 0; //現場折價
  ///購物車列表
  //暫定(1:商品，2:預約與課程，3:儲值金)
  List<ProductModel> shoppingCarListModel = [];
  String? _memberAvatar;
  String? _memberName;
  String? _memberPhone;
  String? _memberBirthday;
  String? _memberEMail;
  bool _isSelectWallet = false; //是否已選錢包

  bool get isSelectWallet => _isSelectWallet;

  set isSelectWallet(bool value) {
    _isSelectWallet = value;
    notifyListeners();
  }

  String? get memberEMail => _memberEMail;

  set memberEMail(String? value) {
    _memberEMail = value;
    notifyListeners();
  }

  String? get memberAvatar => _memberAvatar;

  set memberAvatar(String? value) {
    _memberAvatar = value;
    notifyListeners();
  }

  String? get memberBirthday => _memberBirthday;

  set memberBirthday(String? value) {
    _memberBirthday = value;
    notifyListeners();
  }

  String? get memberName => _memberName;

  set memberName(String? value) {
    _memberName = value;
    notifyListeners();
  }

  String? get memberPhone => _memberPhone;

  set memberPhone(String? value) {
    _memberPhone = value;
    notifyListeners();
  }

  ///
  void setDiscountRes(int value) {
    discountRes = value;
    notifyListeners();
  }

  ///設置商品類別選擇
  void selectProductType(int index) {
    productSelectedTypeIndex = index;
    notifyListeners();
  }

  ///清除商品類別選擇
  void resetProductType() {
    productSelectedTypeIndex = -1;
    notifyListeners();
  }

  ///設置課程類別選擇
  void selectClassType(int index) {
    classSelectedTypeIndex = index;
    notifyListeners();
  }

  ///清除課程類別選擇
  void resetClassType() {
    classSelectedTypeIndex = -1;
    notifyListeners();
  }

  ///暫存購物清單
  List<ProductModel> _backupShoppingCarListModel = [];

  /// 備份目前購物車狀態
  void backupShoppingCarData() {
    _backupShoppingCarListModel = shoppingCarListModel
        .map((e) => e.copy())
        .toList();
  }

  /// 還原購物車資料
  void restoreShoppingCarData() {
    shoppingCarListModel = _backupShoppingCarListModel
        .map((e) => e.copy())
        .toList();
    notifyListeners();
  }

  ///---｜商品相關｜---
  ///商品類別
  List<BaseModel> productType = [];

  ///商品列表
  //暫定(1:商品，2:預約與課程，3:儲值金)
  List<ProductModel> productModel = [];

  ///---｜課程相關｜---
  ///課程類別
  List<BaseModel> classType = [];

  ///課程列表
  //暫定(1:商品，2:預約與課程，3:儲值金)
  List<ProductModel> classModel = [];

  ///---｜購買出值金相關｜---

  ///儲值金列表
  //暫定(1:商品，2:預約與課程，3:儲值金)
  List<ProductModel> cashModel = [];

  ///提取商品金額(依照type)
  double getAmountByType(int type) {
    return shoppingCarListModel
        .where((item) => item.type == type)
        .fold(0.0, (sum, item) => sum + (item.selectAmount * item.price));
  }

  /// 提取全部商品總金額（不分type）
  double getTotalAmount() {
    return shoppingCarListModel.fold(
      0.0,
          (sum, item) => sum + (item.selectAmount * item.price),
    );
  }

  ///現場折價計算
  double getDiscountAmount() {
    final total = getTotalAmount();

    if (discountRes != 0) {
      final discountRate = discountRes / 100.0;
      final discountedPrice = total * discountRate;
      return total - discountedPrice;
    }

    return 0.0;
  }


  /// 未付款金額 = 折後金額 - 現場減價 - 使用者輸入金額
  double getFinalAmount() {
    final discountPrice = getDiscountPrice();

    // 現場減價（安全轉數字，空字串或非法輸入則當 0）
    final onsiteDiscount = double.tryParse(discountController.text) ?? 0;

    return discountPrice - onsiteDiscount - _inputValue;
  }

  /// 折扣後應付金額
  double getDiscountPrice() {
    final total = getTotalAmount();
    final discount = getDiscountAmount();

    return total - discount;
  }



  /// 找零金額 = (所有支付方式輸入的金額總和) - 應付金額
  double getChangeAmount() {
    final finalAmount = getFinalAmount();

    double paid = 0.0;

    // 遍歷所有付款方式的輸入框
    for (var controller in paymentControllers.values) {
      paid += double.tryParse(controller.text) ?? 0.0;
    }

    final change = paid - finalAmount;

    return change > 0 ? change : 0.0; // 只有超過才算找零
  }

  ///---｜預約課程相關｜---
  //(暫定：0 全部，1 臉部，2 身體，3 手足，4 頭部舒壓，5 孕媽咪，6 男士保養，7 眉睫)
  List<ReserveClassModel> reserveClassModel = [
    ReserveClassModel(className: '臉部保養' * 10, typeId: 1, isSelect: false),
    ReserveClassModel(className: '臉部保養' * 10, typeId: 1, isSelect: false),
    ReserveClassModel(className: '臉部保養' * 10, typeId: 1, isSelect: false),
    ReserveClassModel(
        className: '身體精油按摩' * 10, typeId: 2, isSelect: false),
    ReserveClassModel(
        className: '身體精油按摩' * 10, typeId: 2, isSelect: false),
    ReserveClassModel(
        className: '身體精油按摩' * 10, typeId: 2, isSelect: false),
    ReserveClassModel(className: '手足保養' * 10, typeId: 3, isSelect: false),
    ReserveClassModel(className: '手足保養' * 10, typeId: 3, isSelect: false),
    ReserveClassModel(className: '頭部舒壓' * 10, typeId: 4, isSelect: false),
    ReserveClassModel(className: '孕媽咪護理' * 10, typeId: 5, isSelect: false),
    ReserveClassModel(className: '男士保養' * 10, typeId: 6, isSelect: false),
    ReserveClassModel(
        className: '眉型整修與護理' * 10, typeId: 7, isSelect: false),
    ReserveClassModel(
        className: '眉型整修與護理' * 10, typeId: 7, isSelect: false),
  ];

  ///預約課程單選邏輯
  void selectReserveClass(ReserveClassModel selected) {
    for (final item in reserveClassModel) {
      item.isSelect = false;
    }
    selected.isSelect = true;
    notifyListeners();
  }

  ///利用營業時間起訖列出列表
  List<DateTime> generate15MinIntervals(DateTime start, DateTime end) {
    List<DateTime> intervals = [];
    DateTime current = start;
    while (!current.isAfter(end)) {
      // <= end
      intervals.add(current);
      current = current.add(Duration(minutes: 15));
    }
    return intervals;
  }

  ///不可選取時間
  List<DateTime> generateUnavailableList(int year, int month, int day) {
    return [
      DateTime(year, month, day, 11, 00),
      DateTime(year, month, day, 12, 00),
      DateTime(year, month, day, 18, 00),
      DateTime(year, month, day, 19, 45),
    ];
  }

  ///美容師
  List<BaseModel> beauticianModel = [
    BaseModel(title: '程可音', content: '', isSelect: false),
    BaseModel(title: '王小雨', content: '', isSelect: false),
    BaseModel(title: '林伈琴', content: '', isSelect: false),
  ];

  ///空間
  //這邊content為剩餘數
  List<BaseModel> spaceModel = [
    BaseModel(title: 'A1', content: '3', isSelect: false),
    BaseModel(title: 'A2', content: '5', isSelect: false),
    BaseModel(title: 'B1', content: '8', isSelect: false),
    BaseModel(title: 'B2', content: '0', isSelect: false),
    BaseModel(title: 'B3', content: '3', isSelect: false),
  ];

  ///美容師單選邏輯
  void selectBeautician(BaseModel selected) {
    for (final item in beauticianModel) {
      item.isSelect = false;
    }
    selected.isSelect = true;
    notifyListeners();
  }

  ///預約課程單選邏輯
  void selectSpace(BaseModel selected) {
    for (final item in spaceModel) {
      item.isSelect = false;
    }
    selected.isSelect = true;
    notifyListeners();
  }

  ///單堂可購買課程
  //(暫定：0 全部，1 臉部，2 身體，3 手足，4 頭部舒壓，5 孕媽咪，6 男士保養，7 眉睫)
  List<BaseModel> singleCoursesAvailableModel = [
    BaseModel(title: '臉部', content: '', isSelect: false, iD: '1'),
    BaseModel(title: '身體', content: '', isSelect: false, iD: '2'),
    BaseModel(title: '手足', content: '', isSelect: false, iD: '3'),
    BaseModel(title: '頭部舒壓', content: '', isSelect: false, iD: '4'),
    BaseModel(title: '孕媽咪', content: '', isSelect: false, iD: '5'),
    BaseModel(title: '男士保養', content: '', isSelect: false, iD: '6'),
    BaseModel(title: '眉睫', content: '', isSelect: false, iD: '7'),
  ];

  ///已買課程
  List<BaseModel> boughtCourseModel = [
    BaseModel(title: '臉部', content: '', isSelect: false, iD: '1'),
    BaseModel(title: '身體', content: '', isSelect: false, iD: '2'),
    BaseModel(title: '手足', content: '', isSelect: false, iD: '3'),
    BaseModel(title: '頭部舒壓', content: '', isSelect: false, iD: '4'),
    BaseModel(title: '孕媽咪', content: '', isSelect: false, iD: '5'),
    BaseModel(title: '男士保養', content: '', isSelect: false, iD: '6'),
    BaseModel(title: '眉睫', content: '', isSelect: false, iD: '7'),
  ];

  ///
  List<String> selectedIdsRes = [];

  void updateSelectedIds(List<String> ids) {
    selectedIdsRes = List.from(ids);
    notifyListeners();
  }

  //----------


  /// 加入購物車
  void addToShoppingCarByProductId({required String productId}) async {
    ProductModel? product;

    try {
      product = productModel.firstWhere((e) => e.productId == productId);
    } catch (_) {
      try {
        product = classModel.firstWhere((e) => e.productId == productId);
      } catch (_) {
        try {
          product = cashModel.firstWhere((e) => e.productId == productId);
        } catch (_) {
          product = null;
        }
      }
    }

    if (product == null || product.productId.isEmpty) return;

    //強制第一次加入就是 1
    if (product.selectAmount == 0) {
      product.selectAmount = 1;
    } else {
      product.selectAmount++;
    }

    // 庫存扣掉
    if (product.totalAmount > 0) {
      product.totalAmount--;
    }

    product.isTempSelection = true;

    final cartIndex = shoppingCarListModel.indexWhere(
          (e) => e.productId == productId,
    );

    if (cartIndex != -1) {
      shoppingCarListModel[cartIndex].selectAmount = product.selectAmount;
      shoppingCarListModel[cartIndex].totalAmount = product.totalAmount;
    } else {
      shoppingCarListModel.insert(0, product);
    }

    notifyListeners();

    // ===== 同步到 SQLite =====
    final dbProvider = ProductShoppingCarRecordProvider();
    await dbProvider.createTableIfNotExists();

    int type = product.type; // 1:商品, 2:課程, 3:儲值金
    int amount = product.selectAmount;

    await dbProvider.insertOrUpdate(product.productId, type, amount: amount);

    print('已同步至 SQLite: ${product.productId}, type: $type, amount: $amount');
  }



  /// 移除整筆購物車資料，並補回庫存、重置 selectAmount，清空 SQLite
  Future<void> removedShoppingCarByProductId({
    required String productId,
  }) async {
    final cartIndex = shoppingCarListModel.indexWhere(
          (item) => item.productId == productId,
    );

    if (cartIndex == -1) return; // 購物車中沒找到

    final cartItem = shoppingCarListModel[cartIndex];

    // 優先從商品找
    final productIndex = productModel.indexWhere(
          (p) => p.productId == productId,
    );
    if (productIndex != -1) {
      final product = productModel[productIndex];
      product.totalAmount += cartItem.selectAmount;
      product.selectAmount = 0;
      productModel = List.from(productModel);
    }
    // 再從課程找
    else {
      final classIndex = classModel.indexWhere((p) => p.productId == productId);
      if (classIndex != -1) {
        final course = classModel[classIndex];
        course.totalAmount += cartItem.selectAmount;
        course.selectAmount = 0;
        classModel = List.from(classModel);
      }
      // 最後從儲值金找
      else {
        final cashIndex = cashModel.indexWhere((p) => p.productId == productId);
        if (cashIndex != -1) {
          final cash = cashModel[cashIndex];
          cash.totalAmount += cartItem.selectAmount;
          cash.selectAmount = 0;
          cashModel = List.from(cashModel);
        }
      }
    }

    // 清除購物車項目
    shoppingCarListModel.removeAt(cartIndex);
    shoppingCarListModel = List.from(shoppingCarListModel);

    // ===== 清 SQLite =====
    final provider = ProductShoppingCarRecordProvider();
    await provider.createTableIfNotExists();
    await provider.deleteByProductId(productId);

    notifyListeners();
  }



  /// 增加購物車單筆資料選取數量
  Future<void> incrementAmountByProductId(String productId) async {
    final provider = ProductShoppingCarRecordProvider();
    await provider.createTableIfNotExists();

    final db = await App.database;
    final res = await db.query(
      provider.tableName,
      where: 'productId = ?',
      whereArgs: [productId],
    );

    if (res.isEmpty) {
      print("SQLite 找不到 productId: $productId");
      return;
    }

    for (final row in res) {
      final type = row['type'] as int;

      // 更新資料庫數量
      await provider.insertOrUpdate(productId, type, amount: 1);

      // 取得最新數量
      final amount = await provider.getAmount(productId, type);

      // 嘗試找到購物車中已有的商品
      final cartIndex = shoppingCarListModel.indexWhere(
            (e) => e.productId == productId && e.type == type,
      );

      if (cartIndex != -1) {
        // 已存在於購物車，直接更新數量
        shoppingCarListModel[cartIndex].selectAmount = amount;

        // 如果是實體商品，扣庫存
        if (type == 1) {
          final prodIndex = productModel.indexWhere((p) => p.productId == productId);
          if (prodIndex != -1 && productModel[prodIndex].totalAmount > 0) {
            productModel[prodIndex].totalAmount--;
            shoppingCarListModel[cartIndex].totalAmount = productModel[prodIndex].totalAmount;
          }
        }
      } else {
        // 不在購物車，建立 ProductModel
        ProductModel product;

        if (type == 1) {
          // 實體商品
          final index = productModel.indexWhere((p) => p.productId == productId);
          if (index != -1) {
            // 扣庫存
            if (productModel[index].totalAmount > 0) {
              productModel[index].totalAmount--;
            }
            product = ProductModel(
              productId: productModel[index].productId,
              title: productModel[index].title,
              subTitle: productModel[index].subTitle,
              price: productModel[index].price,
              selectAmount: amount,
              totalAmount: productModel[index].totalAmount,
              type: productModel[index].type,
              isSelect: productModel[index].isSelect,
              isTempSelection: productModel[index].isTempSelection,
            );
          } else {
            // 找不到就建立最小模型
            product = ProductModel(
              productId: productId,
              title: '',
              subTitle: null,
              price: 0,
              selectAmount: amount,
              totalAmount: 0,
              type: type,
              isSelect: false,
              isTempSelection: false,
            );
          }
        } else if (type == 2) {
          // 虛擬課程
          try {
            final p = classModel.firstWhere((p) => p.productId == productId);
            product = ProductModel(
              productId: p.productId,
              title: p.title,
              subTitle: p.subTitle,
              price: p.price,
              selectAmount: amount,
              totalAmount: p.totalAmount,
              type: p.type,
              isSelect: p.isSelect,
              isTempSelection: p.isTempSelection,
            );
          } catch (_) {
            product = ProductModel(
              productId: productId,
              title: '',
              subTitle: null,
              price: 0,
              selectAmount: amount,
              totalAmount: 0,
              type: type,
              isSelect: false,
              isTempSelection: false,
            );
          }
        } else if (type == 3) {
          // 儲值金
          try {
            final p = cashModel.firstWhere((p) => p.productId == productId);
            product = ProductModel(
              productId: p.productId,
              title: p.title,
              subTitle: p.subTitle,
              price: p.price,
              selectAmount: amount,
              totalAmount: p.totalAmount,
              type: p.type,
              isSelect: p.isSelect,
              isTempSelection: p.isTempSelection,
            );
          } catch (_) {
            product = ProductModel(
              productId: productId,
              title: '',
              subTitle: null,
              price: 0,
              selectAmount: amount,
              totalAmount: 0,
              type: type,
              isSelect: false,
              isTempSelection: false,
            );
          }
        } else {
          // 防呆
          product = ProductModel(
            productId: productId,
            title: '',
            subTitle: null,
            price: 0,
            selectAmount: amount,
            totalAmount: 0,
            type: type,
            isSelect: false,
            isTempSelection: false,
          );
        }

        // 插入購物車
        shoppingCarListModel.insert(0, product);
      }
    }

    // 更新 UI
    productModel = List.from(productModel);
    shoppingCarListModel = List.from(shoppingCarListModel);
    notifyListeners();
  }


  /// 減少購物車單筆資料選取數量
  Future<void> decrementAmountByProductId(String productId) async {
    final provider = ProductShoppingCarRecordProvider();
    await provider.createTableIfNotExists();

    final db = await App.database;
    final res = await db.query(
      provider.tableName,
      where: 'productId = ?',
      whereArgs: [productId],
    );

    if (res.isEmpty) return;

    for (final row in res) {
      final type = row['type'] as int;

      // SQLite 減少數量
      await provider.decrementAmount(productId, type);
      final newAmount = await provider.getAmount(productId, type);

      // 更新本地商品庫存（只對商品 type==1 扣庫存）
      if (type == 1) {
        final index = productModel.indexWhere((p) => p.productId == productId);
        if (index != -1) {
          productModel[index].totalAmount++;
        }
      }

      // 更新購物車列表
      final cartIndex = shoppingCarListModel.indexWhere(
            (e) => e.productId == productId && e.type == type,
      );

      if (cartIndex != -1) {
        if (newAmount <= 0) {
          // 數量為0，從 SQLite 移除
          await provider.deleteByProductId(productId);

          // 移除購物車列表
          shoppingCarListModel.removeAt(cartIndex);

          // 重置所有對應模型的 selectAmount
          final resetProductList = [productModel, classModel, cashModel];
          for (var list in resetProductList) {
            final index = list.indexWhere((p) => p.productId == productId);
            if (index != -1) {
              list[index].selectAmount = 0;
            }
          }
        } else {
          // 更新購物車選取數量
          shoppingCarListModel[cartIndex].selectAmount = newAmount;
        }
      }
    }

    // 刷新 UI
    productModel = List.from(productModel);
    classModel = List.from(classModel);
    cashModel = List.from(cashModel);
    shoppingCarListModel = List.from(shoppingCarListModel);
    notifyListeners();
  }


  ///清空購物車
  void clearShoppingCar() {
    for (final cartItem in shoppingCarListModel) {
      // 商品處理
      final productIndex = productModel.indexWhere(
            (p) => p.productId == cartItem.productId,
      );
      if (productIndex != -1) {
        final product = productModel[productIndex];
        product.totalAmount += cartItem.selectAmount;
        product.selectAmount = 0;
        continue;
      }

      // 課程處理
      final classIndex = classModel.indexWhere(
            (c) => c.productId == cartItem.productId,
      );
      if (classIndex != -1) {
        final course = classModel[classIndex];
        course.totalAmount += cartItem.selectAmount;
        course.selectAmount = 0;
        continue;
      }

      // 儲值金處理（只清除 selectAmount，不補庫存）
      final cashIndex = cashModel.indexWhere(
            (c) => c.productId == cartItem.productId,
      );
      if (cashIndex != -1) {
        final cash = cashModel[cashIndex];
        cash.selectAmount = 0;
      }
    }

    // 將所有商品、課程、儲值金的 isSelect 設為 false
    for (var p in productModel) {
      p.isSelect = false;
    }
    for (var c in classModel) {
      c.isSelect = false;
    }
    for (var cash in cashModel) {
      cash.isSelect = false;
    }

    productModel = List.from(productModel);
    classModel = List.from(classModel);
    cashModel = List.from(cashModel);

    // 清空購物車
    shoppingCarListModel = [];

    notifyListeners();
  }

  ///清除會員資料
  void clearMember() {
    memberName = null;
    memberAvatar = null;
    memberPhone = null;
    memberBirthday = null;
    _memberEMail = null;
    notifyListeners();
  }

  //更新會員資訊
  void setMemberInfo({
    required String avatar,
    required String name,
    required String phone,
    required String birthday,
    required String email,
  }) {
    _memberAvatar = avatar;
    _memberName = name;
    _memberPhone = phone;
    _memberBirthday = birthday;
    _memberEMail = email;
    notifyListeners();
  }

  //更新會員錢包選取狀態
  void setSelectWalletStatue() {
    _isSelectWallet = true;
    notifyListeners();
  }

  ///---｜會員儲值金部分｜---

  // 錢包支付金額
  int _inputValue = 0;

  int get inputValue => _inputValue;

  set inputValue(int value) {
    _inputValue = value;
    notifyListeners();
  }

  //會員錢包選取
  void toggleSelectCash(int index) {
    for (var i = 0; i < memberCashModel.length; i++) {
      memberCashModel[i].isSelect = i == index;
    }
    notifyListeners();
  }

  //會員錢包清空
  void clearSelectCash() {
    for (var i = 0; i < memberCashModel.length; i++) {
      memberCashModel[i].isSelect = false;
     inputValue = 0;
    }
    notifyListeners();
  }


  //會員儲值金 儲值金錢包列表
  List<MemberCashModel> memberCashModel = [
    MemberCashModel(
      cashName: '儲值金錢包Ａ',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 2300,
      isSelect: false,
    ),
    MemberCashModel(
      cashName: '儲值金錢包B',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 60,
      isSelect: false,
    ),
    MemberCashModel(
      cashName: '儲值金錢包C',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 1800,
      isSelect: false,
    ),
    MemberCashModel(
      cashName: '儲值金錢包D',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 2200,
      isSelect: false,
    ),
    MemberCashModel(
      cashName: '儲值金錢包E',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 100,
      isSelect: false,
    ),
    MemberCashModel(
      cashName: '儲值金錢包F',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 50,
      isSelect: false,
    ),
    MemberCashModel(
      cashName: '儲值金錢包G',
      describe: '錢包說明，' * 10,
      cashOriginalPrice: 30000,
      overage: 1560,
      isSelect: false,
    ),
  ];

  ///---｜業績人員相關｜---
  List<PerformanceModel> performanceModel = [];

  ///清除已選銷售人員
  void clearSelectedPerformance() {
    for (var item in performanceModel) {
      item.isSelect = false;
    }
    notifyListeners();
  }

  ///提取已選項目
  void toggleSelectPerformance(int index) {
    for (var i = 0; i < performanceModel.length; i++) {
      performanceModel[i].isSelect = i == index;
    }

    performanceModel = List.from(performanceModel);

    notifyListeners();
  }

  /// 提交選中的業績人員到 staff
  void submitSelectedStaff() {
    final selected = performanceModel.firstWhere(
          (e) => e.isSelect,
      orElse: () =>
          PerformanceModel(
            staffId: '',
            staffName: '',
            shopId: '',
            isSelect: false,
          ),
    );

    if (selected.staffId.isNotEmpty) {
      staff.clear(); // 先清除舊選擇
      staff[selected.staffId] = selected.staffName;
      notifyListeners();
    }
  }

  ///清除已選
  void clearSelection() {
    for (var item in performanceModel) {
      item.isSelect = false;
    }
    notifyListeners();
  }

  ///api取得商品類別列表
  Future<void> apiGetProductsTypes(BuildContext context) async {
    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';
    productType = [];
    final res = await ApiService.create().getProductsTypes(token);
    if (res.isSuccessful) {
      ResGetProductsTypes resGetProductsTypes = ResGetProductsTypes.fromJson(
        jsonDecode(res.bodyString),
      );
      if (resGetProductsTypes.success ?? false) {
        resGetProductsTypes.data?.forEach((e) {
          productType.add(
            BaseModel(
              title: e.name ?? '',
              iD: (e.id ?? 0).toString(),
              content: '',
            ),
          );
        });
        notifyListeners();
        return;
      }

      if (!(resGetProductsTypes.success ?? false)) {
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: resGetProductsTypes.message ?? '',
          );
        }

        return;
      }
    } else {
      if (context.mounted) {
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return;
    }
    return;
  }

  ///api取得課程性質列表
  Future<void> apiGetProgramsCourseTypes(BuildContext context) async {
    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';
    classType = [];
    final res = await ApiService.create().getProgramsCourseTypes(token);
    if (res.isSuccessful) {
      ResGetProgramsCourseTypes resGetProgramsCourseTypes =
      ResGetProgramsCourseTypes.fromJson(jsonDecode(res.bodyString));
      if (resGetProgramsCourseTypes.success ?? false) {
        resGetProgramsCourseTypes.data?.forEach((e) {
          classType.add(
            BaseModel(
              title: e.name ?? '',
              iD: (e.id ?? 0).toString(),
              content: '',
            ),
          );
        });
        notifyListeners();
        return;
      }

      if (!(resGetProgramsCourseTypes.success ?? false)) {
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: resGetProgramsCourseTypes.message ?? '',
          );
        }

        return;
      }
    } else {
      if (context.mounted) {
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return;
    }
    return;
  }

  ///api取得業績人員
  //這邊員工應該不會多到要分頁。因此直接拿一百筆
  Future<void> apiGetEmployees(BuildContext context, {String? search}) async {
    showLoadingOverlay();
    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';
    performanceModel = [];
    final res = await ApiService.create().getEmployees(token, search, 100, 1);
    if (res.isSuccessful) {
      dismissLoadingOverlay();
      ResEmployees resEmployees = ResEmployees.fromJson(
        jsonDecode(res.bodyString),
      );
      if (resEmployees.success ?? false) {
        resEmployees.data?.forEach((e) {
          performanceModel.add(
            PerformanceModel(
              staffId: e.employeeNumber ?? '',
              shopId: e.storeCode ?? '',
              staffName: e.employeeName ?? '',
              isSelect: false,
            ),
          );
        });
        notifyListeners();
        return;
      }

      if (!(resEmployees.success ?? false)) {
        dismissLoadingOverlay();
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: resEmployees.message ?? '',
          );
        }

        return;
      }
    } else {
      if (context.mounted) {
        dismissLoadingOverlay();
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return;
    }
  }

  /// api取得課程列表
  /// 課程之前說沒有庫存上限，所以不用做到像商品一樣處理庫存量
  Future<void> apiGetPrograms(BuildContext context, {
    String? search,
    String? courseTypeName,
    int? perPage,
    int? page,
    bool clearData = false,
    bool isHaveLoading = false,
    bool isSearch = false,
  }) async {
    print('search: $search');
    print('courseTypeName: $courseTypeName');

    if (isHaveLoading) {
      showLoadingOverlay();
    }

    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';

    // 搜尋前先備份主清單
    if (isSearch && _backupClassModel.isEmpty) {
      _backupClassModel = classModel.map((e) => e.copy()).toList();
    }

    if (clearData) classModel.clear(); // 只有下拉清空

    final res = await ApiService.create().getPrograms(
      token,
      search,
      courseTypeName,
      perPage ?? 50,
      page ?? 1,
    );

    if (res.isSuccessful) {
      dismissLoadingOverlay();

      ResGetPrograms resPrograms = ResGetPrograms.fromJson(
        jsonDecode(res.bodyString),
      );

      if (resPrograms.success ?? false) {
        resPrograms.data?.forEach((e) {
          if (!classModel.any((item) => item.productId == e.id)) {
            classModel.add(
              ProductModel(
                productId: e.id ?? '',
                title: e.name ?? '',
                subTitle: e.courseDescription ?? '',
                price: double.tryParse(e.retailPrice ?? '') ?? 0.0,
                selectAmount: 0,
                totalAmount: e.totalCourseDuration ?? 0,
                type: 2,
                isSelect: false,
                isTempSelection: false,
              ),
            );
          }
        });

        // 先做暫存（只有主清單才需要）
        if (!isSearch) {
          cacheOldData();
        }

        notifyListeners();
        return;
      }

      if (!(resPrograms.success ?? false)) {
        dismissLoadingOverlay();
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: 'api get data error',
          );
        }
        return;
      }
    } else {
      if (context.mounted) {
        dismissLoadingOverlay();
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }
      return;
    }
  }

  /// 上拉加載更多課程
  Future<void> apiProgramsLoading(BuildContext context, {
    String? courseTypeName,
    int? perPage,
  }) async {
    if (isLoadingProgramMore || !hasMoreProgram) return;

    isLoadingProgramMore = true;
    currentProgramPage += 1; // 上拉才加

    await apiGetPrograms(
      context,
      courseTypeName: courseTypeName,
      perPage: perPage,
      page: currentProgramPage == 1 ? 1 : currentProgramPage, // 第一次拿第一頁
      isHaveLoading: false,
    );

    isLoadingProgramMore = false;
  }

  /// 下拉刷新課程
  Future<void> apiProgramsPullDown(BuildContext context, {
    String? courseTypeName,
    int? perPage,
  }) async {
    currentProgramPage = 1;
    hasMoreProgram = true;
    classModel.clear();
    notifyListeners();

    await apiGetPrograms(
      context,
      courseTypeName: courseTypeName,
      perPage: perPage,
      page: currentProgramPage,
      isHaveLoading: false,
    );
  }

  /// 設定課程列表
  void setClassList(List<ProductModel> list) {
    classModel = List.from(list);
    notifyListeners();
  }

  /// api取得商品列表
  Future<void> apiGetProduct(BuildContext context, {
    String? search,
    String? productTypeName,
    int? perPage,
    int? page,
    bool clearData = false,
    bool isHaveLoading = false,
    bool isSearch = false,
  }) async {
    if (isHaveLoading) {
      showLoadingOverlay();
    }

    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';

    //搜尋前先備份主清單
    if (isSearch && _backupProductModel.isEmpty) {
      _backupProductModel = productModel.map((e) => e.copy()).toList();
    }

    if (clearData) productModel.clear();

    final res = await ApiService.create().getProducts(
      token,
      search,
      productTypeName,
      perPage ?? 50,
      page ?? 1,
    );

    if (res.isSuccessful) {
      dismissLoadingOverlay();

      final resGetProducts = ResGetProducts.fromJson(
        jsonDecode(res.bodyString),
      );

      if (resGetProducts.success ?? false) {
        dismissLoadingOverlay();
        resGetProducts.data?.forEach((e) {
          if (!productModel.any((item) => item.productId == '${e.id}')) {
            productModel.add(
              ProductModel(
                productId: (e.id ?? 0).toString(),
                title: e.name ?? '',
                price: double.tryParse(e.retailPrice ?? '0') ?? 0.0,
                selectAmount: 0,
                totalAmount: 8,
                type: 1,
                isSelect: false,
                isTempSelection: false,
              ),
            );
          }
        });

        // 庫存修正 (搜尋和主清單都要)
        _applyShoppingCarStock();

        // 只有主清單才做暫存
        if (!isSearch) {
          cacheOldData();
        }

        notifyListeners();
        return;
      }
      if (!(resGetProducts.success ?? false)) {
        dismissLoadingOverlay();
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: 'api get data error',
          );
        }

        return;
      }
    } else {
      if (context.mounted) {
        dismissLoadingOverlay();
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return;
    }
  }

  // 修正庫存的方法
  void _applyShoppingCarStock() {
    for (var item in shoppingCarListModel.where((e) => e.type == 1)) {
      final matches = productModel.where((p) => p.productId == item.productId);
      if (matches.isNotEmpty) {
        final product = matches.first;
        if (product.isTempSelection) continue; // 已扣過就跳過
        product.totalAmount -= item.selectAmount;
        if (product.totalAmount < 0) product.totalAmount = 0;
        product.isTempSelection = true;
      }
    }
  }

  /// 上拉加載更多商品
  Future<void> apiProductLoading(BuildContext context, {
    String? productTypeName,
    int? perPage,
  }) async {
    if (isLoadingProductMore || !hasMoreProduct) return;

    isLoadingProductMore = true;
    currentProductPage += 1; // 上拉才增加頁數

    // 第一次拿資料時，如果 currentProductPage = 1，page = 1
    final pageToFetch = currentProductPage == 1 ? 1 : currentProductPage;

    await apiGetProduct(
      context,
      productTypeName: productTypeName,
      perPage: perPage,
      page: pageToFetch,
      isHaveLoading: false,
    );

    isLoadingProductMore = false;
  }

  /// 下拉刷新商品
  Future<void> apiProductPullDown(BuildContext context, {
    String? productTypeName,
    int? perPage,
  }) async {
    currentProductPage = 1;
    hasMoreProduct = true;
    productModel.clear();
    notifyListeners();

    await apiGetProduct(
      context,
      productTypeName: productTypeName,
      perPage: perPage,
      page: currentProductPage,
      isHaveLoading: false,
    );
  }

  /// 設定商品列表
  void setProductList(List<ProductModel> list) {
    productModel = List.from(list); // 這裡複製一份新的 List
    print('productModel: ${productModel.length}');
    notifyListeners(); // 通知 UI 更新
  }

  /// 暫存原本資料
  void cacheOldData() {
    tempProductList = productModel.map((e) => e.copy()).toList();
    tempClassList = classModel.map((e) => e.copy()).toList();
  }

  ///搜尋送出
  void restoreAfterSearch({String? selectedId, String? type}) {
    if (selectedId == null || type == null) {
      // 沒有選中 → 還原備份清單
      if (_backupProductModel.isNotEmpty) {
        productModel
          ..clear()
          ..addAll(_backupProductModel.map((e) => e.copy()));
        _backupProductModel.clear();
      }
      if (_backupClassModel.isNotEmpty) {
        classModel
          ..clear()
          ..addAll(_backupClassModel.map((e) => e.copy()));
        _backupClassModel.clear();
      }

      //只有取消才需要重新套用購物車庫存
      _applyShoppingCarStock();
    } else {
      // 有選中 → 提交
      if (tempProductList.isNotEmpty) {
        productModel
          ..clear()
          ..addAll(tempProductList.map((e) => e.copy()));
      }
      if (tempClassList.isNotEmpty) {
        classModel
          ..clear()
          ..addAll(tempClassList.map((e) => e.copy()));
      }

      // === 保留購物車原本已選 ===
      for (final item in shoppingCarListModel) {
        final target = productModel.where((p) => p.productId == item.productId);
        if (target.isNotEmpty) target.first.isSelect = true;

        final cls = classModel.where((c) => c.productId == item.productId);
        if (cls.isNotEmpty) cls.first.isSelect = true;
      }

      // === 標記這次新選的 ===
      if (type == '商品') {
        final product = productModel.where((p) => p.productId == selectedId);
        if (product.isNotEmpty) product.first.isSelect = true;
      } else if (type == '課程') {
        final cls = classModel.where((c) => c.productId == selectedId);
        if (cls.isNotEmpty) cls.first.isSelect = true;
      }
    }

    notifyListeners();
  }

  ///api取得付款方式
  Future<void> apiGetPayMethods(BuildContext context) async {
    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';
    final res = await ApiService.create().getPaymentMethods(token, 50);
    paymentTypes = [];
    if (res.isSuccessful) {
      ResGetPayMethods resGetPayMethods = ResGetPayMethods.fromJson(
        jsonDecode(res.bodyString),
      );
      if (resGetPayMethods.success ?? false) {
        resGetPayMethods.data?.forEach((e) {
          paymentTypes.add(e.name ?? '');
          notifyListeners();
          return;
        });
      }
      if (!(resGetPayMethods.success ?? false)) {
        if (context.mounted) {
          sbarStyleDialog(
            context: context,
            yesAction: () {
              navigatorKey.currentState?.pop();
            },
            title: '錯誤',
            content: '${resGetPayMethods.message}',
          );
        }

        return;
      }
    } else {
      if (context.mounted) {
        dismissLoadingOverlay();
        sbarStyleDialog(
          context: context,
          yesAction: () {
            navigatorKey.currentState?.pop();
          },
          title: '錯誤',
          content: 'api connection error',
        );
      }

      return;
    }
  }

  ///api取得儲值金列表
  Future<void> apiGetPrepaid(BuildContext context) async {
    var user = await HiveUserProvider.getUserOrNull();
    final token = 'Bearer ${user?.memberToken}';
    final res = await ApiService.create().getPrepaid(token);

    if (!res.isSuccessful) {
      if (context.mounted) {
        dismissLoadingOverlay();
        sbarStyleDialog(
          context: context,
          yesAction: () => navigatorKey.currentState?.pop(),
          title: '錯誤',
          content: 'api connection error',
        );
      }
      return;
    }

    final resGetPrepaid = ResGetPrepaid.fromJson(jsonDecode(res.bodyString));

    if (resGetPrepaid.code != 200) {
      if (context.mounted) {
        sbarStyleDialog(
          context: context,
          yesAction: () => navigatorKey.currentState?.pop(),
          title: '錯誤',
          content: '${resGetPrepaid.message}',
        );
      }
      return;
    }

    List<ProductModel> newCashModel = [];

    resGetPrepaid.data?.forEach((e) {
      final productId = e.id ?? '';

      // 先檢查購物車裡是否已經有選取
      final existingInCart = shoppingCarListModel.firstWhere(
            (item) => item.productId == productId,
        orElse: () => ProductModel(
          productId: productId,
          title: e.prepaidName ?? '',
          price: double.tryParse(e.prepaidAmount ?? '') ?? 0,
          selectAmount: 0,
          totalAmount: 0,
          type: 3,
          isSelect: false,
          isTempSelection: false,
        ),
      );

      newCashModel.add(ProductModel(
        productId: existingInCart.productId,
        title: existingInCart.title,
        price: existingInCart.price,
        selectAmount: existingInCart.selectAmount,
        totalAmount: 0,
        type: 3,
        isSelect: existingInCart.selectAmount > 0,
        isTempSelection: existingInCart.isTempSelection,
      ));
    });

    cashModel = newCashModel;
    notifyListeners();
  }

}
