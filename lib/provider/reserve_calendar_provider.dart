import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sbar_pos/model/reserve_calendar_model/event_card_model.dart';
import 'package:sbar_pos/model/reserve_calendar_model/time_table_event_data_time_model.dart';

import '../model/reserve_calendar_model/calender_month_event_data.dart';
import '../model/reserve_calendar_model/course_order_details_data.dart';
import '../model/reserve_calendar_model/models/event.dart';
import '../widget/reserve_calender_widget/card_widget/calender_event_card_widget.dart';

enum Calendar { day, week, month, year }

enum CardType { square, rounded, circle }

class ReserveCalendarScreenProvider with ChangeNotifier {
  DateTime weekStartDate = DateTime.now(); // 當前週起始日期
  PageController? pageController;
  ScrollController? weekScrollController;

  //月
  Map<DateTime, List<CalenderEventCardWidget>> _calendarEventDataList = {};

  Map<DateTime, List<CalenderEventCardWidget>> get calendarEventDataList =>
      _calendarEventDataList;

  //週
  Map<DateTime, List<Event>> _weekDataAPI = {};

  Map<DateTime, List<Event>> get weekDataAPI => _weekDataAPI;

  //日
  Map<DateTime, Map<String, List<TimeTableEventDataTimeModel>>> _dayDataAPI =
      {};

  Map<DateTime, Map<String, List<TimeTableEventDataTimeModel>>>
  get dayDataAPI => _dayDataAPI;

  /// 初始化假資料 (月)
  void initializeTestEventsForMonth() {
    addDay({int? y, required int m, required int d}) {
      final year = y ?? DateTime.now().year; // 保證使用當前年份
      return DateTime(year, m, d);
    }

    _calendarEventDataList = {
      addDay(m: 7, d: 17): [
        CalenderEventCardWidget(
          event: EventCardModel(
            title: '深層清潔護理',
            memberName: '陳小美',
            beautician: '王美容師',
            date: '${DateTime.now().year}-7-17',
            startTime: '15:00',
            endTime: '16:00',
            price: '1500',
            status: '已確認',
          ),
        ),
      ],
      addDay(m: 7, d: 19): [
        CalenderEventCardWidget(
          event: EventCardModel(
            title: '全身SPA按摩',
            memberName: '王先生',
            beautician: '林美容師',
            date: '${DateTime.now().year}-7-19',
            startTime: '13:00',
            endTime: '14:00',
            price: '3500',
            status: '待確認',
          ),
        ),
      ],
      addDay(m: 9, d: 19): [
        CalenderEventCardWidget(
          event: EventCardModel(
            title: '全身SPA按摩',
            memberName: '王先生',
            beautician: '林美容師',
            date: '${DateTime.now().year}-9-19',
            startTime: '13:00',
            endTime: '14:00',
            price: '3500',
            status: '待確認',
          ),
        ),
      ],
    };
    notifyListeners();
  }

  ///初始化假資料 (週)
  void initWeekCalendarDataForWeek() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    _weekDataAPI = {
      DateTime.parse('2025-09-08'): [
        Event.withTime(
          startTime: const Duration(hours: 10, minutes: 30),
          endTime: const Duration(hours: 12, minutes: 15),
          title: '足底按摩課程',
          member: '嚴小林',
          beautician: '王羽晴',
          price: 50000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
      ],
      today: [
        Event.withTime(
          startTime: const Duration(hours: 9, minutes: 0),
          endTime: const Duration(hours: 10, minutes: 0),
          title: '眼部舒壓',
          member: '蔡宜臻',
          beautician: 'Luna',
          price: 40000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 9, minutes: 0),
          endTime: const Duration(hours: 10, minutes: 0),
          title: '足底按摩1',
          member: '王大明',
          beautician: '孫慧麗',
          price: 40000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 9, minutes: 40),
          endTime: const Duration(hours: 11, minutes: 0),
          title: '肩頸油壓',
          member: '林立雯',
          beautician: 'Ivy',
          price: 40000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 9, minutes: 0),
          endTime: const Duration(hours: 12, minutes: 45),
          title: '推拿課程',
          member: '多米多羅',
          beautician: '孫雨婷',
          price: 40000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 10, minutes: 20),
          endTime: const Duration(hours: 13, minutes: 45),
          title: '孕媽咪護理',
          member: '張逸庭',
          beautician: 'Cindy',
          price: 40000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
      ],
      DateTime.parse('2025-07-31'): [
        Event.withTime(
          startTime: const Duration(hours: 12, minutes: 0),
          endTime: const Duration(hours: 13, minutes: 0),
          title: '足底按摩1',
          member: '王大明2',
          beautician: '孫子',
          price: 40700,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 13, minutes: 0),
          endTime: const Duration(hours: 14, minutes: 45),
          title: '足底按摩4',
          member: '王阿華',
          beautician: '力可單',
          price: 40900,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 12, minutes: 30),
          endTime: const Duration(hours: 14, minutes: 15),
          title: '水肌嫩模按摩課程',
          member: '李小美',
          beautician: '徐明可',
          price: 50000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
        Event.withTime(
          startTime: const Duration(hours: 14, minutes: 0),
          endTime: const Duration(hours: 16, minutes: 45),
          title: '水肌嫩模按摩課程',
          member: '李小美',
          beautician: '徐明可',
          price: 50000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
      ],
      DateTime.parse('2025-08-03'): [
        Event.withTime(
          startTime: const Duration(hours: 10, minutes: 30),
          endTime: const Duration(hours: 12, minutes: 15),
          title: '足底按摩課程',
          member: '嚴小林',
          beautician: '王羽晴',
          price: 50000,
          phone: 090041555,
          orderNumber: 'AAAA3334567',
        ),
      ],
    };
  }

  ///初始化假資料 (日)
  void initializeTestEventsForDay() {
    final today = DateTime.now();
    _dayDataAPI = {
      DateTime(today.year, today.month, today.day): {
        "Alice": [
          TimeTableEventDataTimeModel(
            title: "身體課程",
            clientName: "張耶",
            status: "已到",
            phoneNumber: '094453321',
            price: 23000,
            startTime: '10:00',
            endTime: '11:00',
          ),
        ],
        "Peter": [
          TimeTableEventDataTimeModel(
            title: "香精淋巴按摩課程",
            clientName: "張小姐",
            status: "已到",
            phoneNumber: '0944532121',
            startTime: '11:30',
            endTime: '13:00',
          ),
        ],
      },

      DateTime(2025, 9, 4): {
        "Alice": [
          TimeTableEventDataTimeModel(
            title: "身體課程",
            clientName: "張耶",
            status: "已到",
            phoneNumber: '094453321',
            price: 23000,
            startTime: '10:00',
            endTime: '11:00',
          ),
        ],
        "Peter": [
          TimeTableEventDataTimeModel(
            title: "香精淋巴按摩課程",
            clientName: "張小姐",
            status: "已到",
            phoneNumber: '0944532121',
            startTime: '11:30',
            endTime: '13:00',
          ),
        ],
      },

      DateTime(2025, 9, 5): {
        "ANowMe": [
          TimeTableEventDataTimeModel(
            title: "大地芳療去角質課程",
            clientName: "張先生",
            status: "未到",
            price: 450000,
            phoneNumber: '09223532121',
            startTime: '10:00',
            endTime: '10:30',
          ),
        ],
        "Tim": [
          TimeTableEventDataTimeModel(
            title: "曲線戰士窈窕課程",
            clientName: "駕駛者",
            status: "已到",
            price: 5800,
            depoist: 2500,
            startTime: '12:45',
            endTime: '14:00',
          ),
        ],
      },
    };

    notifyListeners();
  }

  // -------------------- Calendar & Tab --------------------
  Calendar _selectCalender = Calendar.month;

  Calendar get selectCalender => _selectCalender;

  set selectCalender(Calendar value) {
    if (_selectCalender != value) {
      _selectCalender = value;
      notifyListeners();
    }
  }

  int get currentTabIndex {
    switch (_selectCalender) {
      case Calendar.day:
        return 0;
      case Calendar.week:
        return 1;
      case Calendar.month:
        return 2;
      case Calendar.year:
        return 3;
    }
  }

  // -------------------- 日期管理 --------------------

  //---|月|---
  DateTime _currentDateForMonth = DateTime.now();

  DateTime get currentDateForMonth => _currentDateForMonth;

  /// 更新當前日期，跨月會自動更新 PageController
  void updateCurrentDateForMonth(DateTime newDate) {
    final oldDate = _currentDateForMonth;
    _currentDateForMonth = newDate;

    // 只有跨月才跳 PageView
    if ((oldDate.year != newDate.year || oldDate.month != newDate.month) &&
        pageController != null &&
        pageController!.hasClients) {
      final targetPage = monthToPage(DateTime(newDate.year, newDate.month, 1));
      pageController!.animateToPage(
        targetPage,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }

    notifyListeners();
  }

  /// 安全的月份對應 PageController index，避免跳到錯誤年份
  int monthToPage(DateTime month) {
    return (month.year - 2000) * 12 + (month.month - 1);
  }

  DateTime pageToMonth(int page) {
    final year = 2000 + (page ~/ 12);
    final month = (page % 12) + 1;
    return DateTime(year, month, 1);
  }

  /// 初始化 PageController，保證安全初始頁
  void initPageController() {
    pageController ??= PageController(
      initialPage: monthToPage(_currentDateForMonth),
    );
  }

  //---|週|---
  DateTime _currentDateForWeek = DateTime.now();

  DateTime get currentDateForWeek => _currentDateForWeek;

  void setInitialDateForWeek(DateTime date) {
    _currentDateForWeek = date;
    notifyListeners();
  }

  void updateCurrentDateForWeek(DateTime newDate) {
    _currentDateForWeek = newDate;

    // 自動調整週起始日期
    if (newDate.isBefore(weekStartDate)) {
      weekStartDate = newDate; // 早於當前週 → 向前推
    } else if (newDate.isAfter(weekStartDate.add(const Duration(days: 6)))) {
      weekStartDate = newDate; // 晚於當前週 → 往後推
    }

    // 如果有要更新事件資料
    setCalendarEventDataList(newDate);

    // 只要更新日期，不用管 PageView
    notifyListeners();
  }

  // 從 API 更新整份資料
  void updateCalendarFromAPI(Map<DateTime, List<Event>> newDataAPI) {
    _weekDataAPI = newDataAPI;
    notifyListeners();
    print('Calendar data updated from API: ${newDataAPI.length} dates');
  }

  // 添加單個事件
  void addEventFromAPI(DateTime date, Event newEvent) {
    if (_weekDataAPI.containsKey(date)) {
      _weekDataAPI[date]!.add(newEvent);
    } else {
      _weekDataAPI[date] = [newEvent];
    }
    notifyListeners();
    print('New event added: ${newEvent.title} on ${date.toString()}');
  }

  //---|日|---
  DateTime _currentDateForDay = DateTime.now();

  DateTime get currentDateForDay => _currentDateForDay;

  void setInitialDateForDay(DateTime date) {
    _currentDateForDay = date;
    notifyListeners();
  }

  void updateCurrentDateForDay(DateTime newDate) {
    _currentDateForDay = newDate;

    // 如果有要更新事件資料
    setCalendarEventDataList(newDate);

    // 只要更新日期，不用管 PageView
    notifyListeners();
  }

  /// 從 API 更新 (帶入某一天的資料)
  void updateCalendarFromAPIForDay(
    DateTime date,
    Map<String, List<TimeTableEventDataTimeModel>> newDataForDay,
  ) {
    final normalizedDate = DateTime(date.year, date.month, date.day);
    _dayDataAPI[normalizedDate] = newDataForDay;
    notifyListeners();
    print(
      'Calendar data updated for $normalizedDate: ${newDataForDay.length} keys',
    );
  }

  // 取得某一天的事件資料
  Map<String, List<TimeTableEventDataTimeModel>> getEventsByDate(
    DateTime date,
  ) {
    final normalizedDate = DateTime(date.year, date.month, date.day);
    return _dayDataAPI[normalizedDate] ?? {};
  }

  // -------------------- 檢查事件 --------------------
  bool _checkEvent = false;

  bool get CheckEvent => _checkEvent;

  void setCheckEvent(bool value) {
    _checkEvent = value;
    notifyListeners();
  }

  // -------------------- 課程資料 --------------------
  CourseOrderDetailsData? _courseOrderDetailsData;

  CourseOrderDetailsData? get courseOrderDetailsData => _courseOrderDetailsData;

  // -------------------- Calendar Course Order Data --------------------
  final Map<Calendar, CourseOrderDetailsData> _calendarCourseOrderData = {};

  Map<Calendar, CourseOrderDetailsData> get calendarCourseOrderData =>
      _calendarCourseOrderData;

  void setCalendarCourseOrderData(Calendar type, CourseOrderDetailsData data) {
    _calendarCourseOrderData[type] = data;
    notifyListeners();
  }

  CourseOrderDetailsData? getCalendarCourseOrderData(Calendar type) {
    return _calendarCourseOrderData[type];
  }

  final Map<Calendar, int> _calendarEventDataSize = {};

  void setCalendarEventDataSize(Calendar type, int length) {
    _calendarEventDataSize[type] = length;
  }

  int getCalendarEventDataSize(Calendar type) {
    return _calendarEventDataSize[type] ?? 0;
  }

  // -------------------- Calender Event Data --------------------
  CalenderMonthEventDataModel? _calenderMomEventDataModel;

  CalenderMonthEventDataModel? get calenderMomEventDataModel =>
      _calenderMomEventDataModel;

  void setCalenderMomEventDataModel(CalenderMonthEventDataModel data) {
    _calenderMomEventDataModel = data;
  }

  void setCalendarEventDataList(DateTime date) {
    final dataForDate = _calendarEventDataList[date] ?? [];
    setCalenderMomEventDataModel(CalenderMonthEventDataModel(date: date));
    notifyListeners();
  }

  // -------------------- 狀態 Widget --------------------
  Widget? _stateWidgetView;

  Widget get stateWidgetView => _stateWidgetView ?? Container();

  void setStateWidgetView(Widget viewWidget) {
    _stateWidgetView = viewWidget;
    notifyListeners();
  }

  // -------------------- 日期/時間工具 --------------------
  String nowDay() => DateTime.now().toString().split(' ')[0];

  String selectDay(DateTime day) =>
      DateTime(day.year, day.month, day.day).toString().split(' ')[0];

  String timeOfFormat(TimeOfDay time) =>
      '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';

  TimeOfDay dateTimeToTD(DateTime time) =>
      TimeOfDay(hour: time.hour, minute: time.minute);

  String weekOfFormat(int nowWeek) {
    const weekDays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    return weekDays[nowWeek - 1];
  }

  String nowWeekDay() {
    const weekDays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
    return weekDays[DateTime.now().weekday - 1];
  }

  String setDeposit(int? deposit) {
    final dp = deposit ?? 0;
    if (dp > 0) return '(訂金\$$dp)';
    if (dp == 0) return '';
    return '已完成付款';
  }

  ///取得目前日期(依type)
  DateTime getCurrentDate() {
    if (selectCalender == Calendar.month) {
      return currentDateForMonth;
    } else if (selectCalender == Calendar.week) {
      return currentDateForWeek;
    } else if (selectCalender == Calendar.day) {
      return currentDateForDay;
    } else {
      return DateTime.now();
    }
  }
}
