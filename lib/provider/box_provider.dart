import 'package:get_storage/get_storage.dart';
import 'package:sbar_pos/hive/helper/boxes.dart';

class BoxProvider {
  String _namespace = '';

  Future<void> init(String namepsace) async {
    _namespace = namepsace;
    for (var box in Boxes.values) {
      await _initGsBox(box.name);
    }
  }

  Future<bool> _initGsBox(String name) async {
    final fullName = _getFullName(name);
    return await GetStorage.init(fullName);
  }

  GetStorage getGsBox(Boxes box, {bool withNamespace = true}) {
    final fullName = _getFullName(box.name, withNamespace);
    return GetStorage(fullName);
  }

  String _getFullName(String name, [bool withNamespace = true]) {
    Iterable<String> children([bool withNamespace = true]) sync* {
      yield name;
      if (withNamespace && _namespace.isNotEmpty) {
        yield _namespace;
      }
    }

    return children(withNamespace).join('.');
  }
}
