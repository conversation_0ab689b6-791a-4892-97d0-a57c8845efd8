import 'package:flutter/material.dart';

/*
 * main = kidTest
 * cus_prod = cusProd
 */
class App {
  static final App _singleton = App._internal();

  factory App() {
    return _singleton;
  }

  App._internal();

  static String dbName = 'sbar_database.db';
  static String salesRecordDBName = 'salesRecord';  //主銷人員
  static String productShoppingCarRecordDBName = 'productShoppingCarRecord';  //購物車

  static var database;
}
