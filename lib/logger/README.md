# 本地日誌緩存系統

這是一個完整的本地日誌緩存系統，基於 `talker` 套件構建，使用 `GetStorage` 進行本地存儲，並支持未來的批量上傳功能。

## 功能特性

- ✅ **自動日誌攔截**：自動攔截所有 Talker 日誌並存儲到本地
- ✅ **結構化存儲**：使用 ObjectId 生成唯一ID，支持 JSON 序列化
- ✅ **靈活查詢**：支持按時間、級別、關鍵字等條件查詢日誌
- ✅ **存儲管理**：自動清理過期日誌，防止存儲空間無限增長
- ✅ **統計分析**：提供日誌統計信息和分析功能
- ✅ **批量上傳**：預留完整的批量上傳接口設計
- ✅ **依賴注入**：完全集成到現有的 GetIt 依賴注入系統

## 系統架構

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Talker 日誌   │───▶│ LocalLogObserver │───▶│ LocalLogService │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                                                         ▼
                                               ┌─────────────────┐
                                               │   GetStorage    │
                                               │   (Boxes.log)   │
                                               └─────────────────┘
```

## 核心組件

### 1. LogEntry (日誌數據模型)
- 包含 id、message、timestamp、level、context 等字段
- 支持 JSON 序列化/反序列化
- 提供格式化時間和級別顯示

### 2. LocalLogService (本地日誌服務)
- 負責日誌的 CRUD 操作
- 支持複雜查詢和統計分析
- 自動管理存儲空間（最大 10,000 條日誌）

### 3. LocalLogObserver (日誌觀察者)
- 攔截 Talker 的所有日誌事件
- 根據配置過濾日誌級別
- 異步存儲，不阻塞主線程

### 4. LogUploadService (上傳服務)
- 提供批量上傳接口設計
- 支持分批上傳和進度回調
- 包含重試機制和錯誤處理

## 快速開始

### 1. 系統已自動配置

本地日誌緩存系統已經在 `lib/di/locator.dart` 中自動配置，無需額外設置。

### 2. 基本使用

```dart
import 'package:sbar_pos/di/locator.dart';
import 'package:talker_flutter/talker_flutter.dart';

// 獲取 Talker 實例（已自動配置本地存儲）
final talker = getIt<Talker>();

// 記錄日誌（會自動存儲到本地）
talker.info('用戶登入成功');
talker.warning('網絡連接不穩定');
talker.error('API 請求失敗');
```

### 3. 查詢本地日誌

```dart
import 'package:sbar_pos/di/locator.dart';
import 'package:sbar_pos/logger/local_log_service.dart';

final localLogService = getIt<LocalLogService>();

// 獲取所有日誌
final allLogs = await localLogService.getAllLogs();

// 獲取最近的日誌
final recentLogs = await localLogService.getRecentLogs(10);

// 根據級別查詢
final errorLogs = await localLogService.getLogsByLevel(LogLevel.error);

// 複雜查詢
final filter = LogQueryFilter(
  levels: [LogLevel.error, LogLevel.warning],
  messageKeyword: 'API',
  startTime: DateTime.now().subtract(Duration(days: 1)).millisecondsSinceEpoch,
  limit: 50,
);
final filteredLogs = await localLogService.queryLogs(filter);
```

## 配置選項

### 日誌級別配置

系統根據運行模式自動配置日誌級別：
- **調試模式**：存儲所有級別的日誌（DEBUG 及以上）
- **生產模式**：只存儲重要日誌（WARNING 及以上）

### 存儲限制

- 最大存儲日誌數量：10,000 條
- 超過限制時自動刪除最舊的日誌
- 支持手動清理過期日誌

## 日誌級別

```dart
enum LogLevel {
  debug,    // 調試信息
  info,     // 一般信息
  warning,  // 警告信息
  error,    // 錯誤信息
  critical, // 嚴重錯誤
}
```

## 批量上傳（未來功能）

系統已預留完整的批量上傳接口：

```dart
final uploadService = LogUploadService(localLogService);

// 配置上傳參數
final config = LogUploadConfig(
  endpoint: 'https://your-api.com/logs/upload',
  batchSize: 100,
  timeoutSeconds: 30,
  deleteAfterUpload: true,
);

// 上傳所有日誌
final result = await uploadService.uploadAllLogs(
  config: config,
  onProgress: (uploaded, total) {
    print('進度：$uploaded/$total');
  },
);
```

## 最佳實踐

### 1. 應用啟動時的初始化

```dart
// 在應用啟動時清理過期日誌
final localLogService = getIt<LocalLogService>();
final thirtyDaysAgo = DateTime.now().subtract(Duration(days: 30));
await localLogService.deleteLogsBeforeTime(thirtyDaysAgo);
```

### 2. 錯誤處理

```dart
try {
  // 業務邏輯
} catch (e, stackTrace) {
  // 自動記錄到本地
  talker.handle(e, stackTrace);
}
```

### 3. 定期維護

```dart
// 定期獲取統計信息
final stats = await localLogService.getLogStatistics();
print('本地日誌數量：${stats['totalCount']}');

// 清理過期日誌
final deletedCount = await localLogService.deleteLogsBeforeTime(
  DateTime.now().subtract(Duration(days: 7))
);
```

## 文件結構

```
lib/logger/
├── README.md                 # 本文檔
├── local_log_service.dart    # 本地日誌服務
├── local_log_observer.dart   # 日誌觀察者
├── log_upload_service.dart   # 上傳服務接口
└── log_cache_example.dart    # 使用示例

lib/model/log/
└── log_entry.dart           # 日誌數據模型
```

## 依賴套件

- `talker`: 日誌記錄工具
- `get_storage`: 本地存儲
- `objectid`: 唯一ID生成
- `get_it`: 依賴注入

## 注意事項

1. **性能考慮**：日誌存儲是異步的，不會阻塞主線程
2. **存儲空間**：系統會自動管理存儲空間，防止無限增長
3. **隱私安全**：本地日誌可能包含敏感信息，上傳前請確保數據安全
4. **網絡上傳**：上傳功能需要根據實際API設計來實現具體邏輯

## 示例代碼

完整的使用示例請參考 `log_cache_example.dart` 文件。

## 技術支持

如有問題或建議，請聯繫開發團隊。
