import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';

class NetworkListener {
  static final NetworkListener _networkListener = NetworkListener._internal();

  ///singleton模式
  factory NetworkListener() {
    return _networkListener;
  }

  NetworkListener._internal();

  final StreamController<bool> _networkStateController = StreamController<bool>();
  late final Stream<bool> networkStateStream = _networkStateController.stream.asBroadcastStream();

  void pushConnectionEvent(ConnectivityResult event) {

    final hasConnection = toIsConnection(event);

    _isConnection = hasConnection;
    _networkStateController.add(hasConnection);
  }

  bool toIsConnection(ConnectivityResult result) => switch(result) {
    ConnectivityResult.wifi ||
    ConnectivityResult.mobile => true,
    _ => false,
  };

  bool _isConnection = false;

  /// 回傳當前的連線狀態
  bool get isConnection => _isConnection;

}