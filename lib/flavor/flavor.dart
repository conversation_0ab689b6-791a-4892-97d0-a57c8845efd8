enum Flavor { kidTest, kidProd, cusTest, cusProd }

class FlavorValues {
  FlavorValues({
    required this.baseUrl,
    required this.shareUrl,
    this.signalRUrl = '',

  });

  final String baseUrl;
  final String signalRUrl;
  final String shareUrl;
}

class FlavorConfig {
  final Flavor flavor;
  final String name;
  final FlavorValues values;
  static FlavorConfig? _instance;

  factory FlavorConfig({Flavor? flavor, FlavorValues? values}) {
    _instance ??= FlavorConfig._internal(flavor ?? Flavor.kidTest,
        flavor.toString(), values ?? FlavorValues(baseUrl: '', shareUrl: ''));
    return _instance!;
  }

  FlavorConfig._internal(
      this.flavor,
      this.name,
      this.values,
      );

  static FlavorConfig get instance {
    return _instance!;
  }

  static bool isKidProduction() => _instance?.flavor == Flavor.kidProd;

  static bool isKidTest() => _instance?.flavor == Flavor.kidTest;

  static bool isCusProduction() => _instance?.flavor == Flavor.cusProd;

  static bool isCusTest() => _instance?.flavor == Flavor.cusTest;
}