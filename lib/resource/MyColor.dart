import 'package:flutter/material.dart';

class MyColors {




  static const Color indicatorBG = Colors.white60;

  //grey
  static const Color grey_49_49_49 = Color.fromRGBO(49, 49, 49, 1);
  static const Color grey238_238_238 = Color.fromRGBO(238, 238, 238, 1);
  static const Color grey_210_210_210 = Color.fromRGBO(210, 210, 210, 1);
  static const Color grey_151_151_151 = Color.fromRGBO(151, 151, 151, 1);
  static const Color grey_151_151_151_032 = Color.fromRGBO(151, 151, 151, 0.32);
  static const Color grey_74_74_74 = Color.fromRGBO(74, 74, 74, 1);
  static const Color grey_205_205_205 = Color.fromRGBO(205, 205, 205, 1);
  static const Color grey_205_205_222 = Color.fromRGBO(205, 205, 222, 1);
  static const Color grey_245_245_245_1 = Color.fromRGBO(245, 245, 245, 1); //#F5F5F5
  static const Color grey_145_145_145 = Color.fromRGBO(145, 145, 145, 1);
  static const Color grey_83_83_83 = Color.fromRGBO(83, 83, 83, 1);
  static const Color grey_156_157_152 = Color.fromRGBO(156, 157, 152, 1);
  static const Color grey_219_219_219 = Color.fromRGBO(219, 219, 219, 1);
  static const Color grey169_169_169 = Color.fromRGBO(169, 169, 169, 1);
  static const Color grey_220_220_220 = Color.fromRGBO(220, 220, 220, 1);
  static const Color grey_224_224_224 = Color.fromRGBO(224, 224, 224, 1);
  static const Color grey_75_75_75 = Color.fromRGBO(75, 75, 75, 1);
  static const Color grey_239_243_243 = Color.fromRGBO(239, 243, 243, 1);
  static const Color grey_195_195_195 = Color.fromRGBO(195, 195, 195, 1);
  static const Color grey_140_140_140 = Color.fromRGBO(140, 140, 140, 1);
  static const Color grey_51_51_51 = Color.fromRGBO(51, 51, 51, 1);
  static const Color grey_66_66_66 = Color.fromRGBO(66, 66, 66, 1);
  static const Color grey_235_235_235 = Color.fromRGBO(235, 235, 235, 1);  //ebebeb
  static const Color grey_33_33_33 = Color.fromRGBO(33, 33, 33, 1);
  static const Color grey_119_119_119 = Color.fromRGBO(119, 119, 119, 1);  //777777
  static const Color grey_112_112_112 = Color.fromRGBO(112, 112, 112, 1);  //707070
  static const Color grey_214_213_213 = Color.fromRGBO(214, 213, 213, 1);
  static const Color grey_92_92_92 = Color.fromRGBO(92, 92, 92, 1);
  static const Color grey_248_248_248 = Color.fromRGBO(248, 248, 248, 1); //f8f8f8
  static const Color grey_247_247_247 = Color.fromRGBO(247, 247, 247, 1); //#F7F7F7
  static const Color grey_249_249_249 = Color.fromRGBO(249, 249, 249, 1); //#F9F9F9
  static const Color grey_189_189_189 = Color.fromRGBO(189, 189, 189, 1);
  static const Color grey_70_70_70 = Color.fromRGBO(70, 70, 70, 1);
  static const Color grey_220_220_213 = Color.fromRGBO(220, 220, 213, 1);
  static const Color grey_211_211_200 = Color.fromRGBO(211, 211, 200, 1);
  static const Color grey_197_197_197 = Color.fromRGBO(197, 197, 197, 1);
  static const Color grey_61_61_61 = Color.fromRGBO(61, 61, 61, 1);
  static const Color grey_255_255_255_08 = Color.fromRGBO(255, 255, 255, 0.8);
  static const Color grey_4_4_15_045 = Color.fromRGBO(4, 4, 15, 0.45);
  static const Color grey_250_250_250 = Color.fromRGBO(250, 250, 250, 1);
  static const Color grey_249_249_247 = Color.fromRGBO(249, 249, 247, 1);
  static const Color grey_230_230_230 = Color.fromRGBO(230, 230, 230, 1);
  static const Color grey_204_204_204 = Color.fromRGBO(204, 204, 204, 1); //#CCCCCC
  static const Color grey_101_101_101 = Color.fromRGBO(101, 101, 101, 1);
  static const Color grey_238_241_244 = Color.fromRGBO(238, 241, 244, 1);
  static const Color grey_164_164_164 = Color.fromRGBO(164, 164, 164, 1);
  static const Color grey_91_91_91 = Color.fromRGBO(91, 91, 91, 1);
  static const Color grey_239_239_239 = Color.fromRGBO(239, 239, 239, 1);
  static const Color grey_60_60_67 = Color.fromRGBO(60, 60, 67, 1);
  static const Color grey_217_217_217 = Color.fromRGBO(217, 217, 217, 1);
  static const Color grey_dbdbdb = Color(0xffdbdbdb);
  static const Color grey_c3c3c3 = Color(0xffc3c3c3);

  //blue
  static const Color blue = Color.fromRGBO(0, 136, 255, 1);
  static const Color loginBlue = Color.fromRGBO(0, 176, 213, 1);
  static const Color blue0_122_255_1 = Color.fromRGBO(0, 122, 255, 1);
  static const Color blue202_226_255 = Color.fromRGBO(202, 226, 255, 1);
  static const Color blue101_196_213 = Color.fromRGBO(101, 196, 213, 1); //#65C4D5
  static const Color blue121_160_167 = Color.fromRGBO(121, 160, 167, 1); //##79A0A7
  static const Color blue222_240_242 = Color.fromRGBO(222, 240, 242, 1);
  static const Color blue236_244_245 = Color.fromRGBO(236, 244, 245, 1);
  static const Color blue122_160_167 = Color.fromRGBO(122, 160, 167, 1);//#7AA0A7
  static const Color blue176_165_231 = Color.fromRGBO(176, 165, 231, 1);

  //red
  static const Color red_255_176_184 = Color.fromRGBO(255, 176, 184, 1);
  static const Color red_240_224_229 = Color.fromRGBO(240, 224, 229, 1);
  static const Color red_255_91_110 = Color.fromRGBO(255, 91, 110, 1);
  static const Color red_216_34_34 = Color.fromRGBO(216, 34, 34, 1);
  static const Color red_255_232_234 = Color.fromRGBO(255, 232, 234, 1);
  static const Color red_154_0_40 = Color.fromRGBO(154, 0, 40, 1);
  static const Color red_230_75_75 = Color.fromRGBO(230, 75, 75, 1); //e64b4b
  static const Color red_224_129_125 = Color.fromRGBO(224, 129, 125, 1);

  //orange
  static const Color orange_240_149_68 = Color.fromRGBO(240, 149, 68, 1); //f09544
  static const Color orange_236_218_199 = Color.fromRGBO(236, 218, 199, 1);
  static const Color orange_229_165_104 = Color.fromRGBO(229, 165, 104, 1);
  static const Color orange_251_244_238 = Color.fromRGBO(251, 244, 238, 1);

//sbar綠
  static const Color green_157_193_65 = Color.fromRGBO(157, 193, 65, 1);  //9dc141
  //green
  static const Color green_67_160_71 = Color.fromRGBO(67, 160, 71, 1);
  static const Color green_2_159_109 = Color.fromRGBO(2, 159, 109, 1);
  static const Color green_129_128_94 = Color.fromRGBO(129, 128, 94, 1);  //81805e
  static const Color green_192_210_136 = Color.fromRGBO(192, 210, 136, 1);
  static const Color green_93_93_71 = Color.fromRGBO(93, 93, 71, 1);
  static const Color green_121_131_90 = Color.fromRGBO(121, 131, 90, 1);  //#79835A
  static const Color green_252_254_244 = Color.fromRGBO(252, 254, 244, 1);
  static const Color green_245_248_232 = Color.fromRGBO(245, 248, 232, 1);
  static const Color green_233_240_209 = Color.fromRGBO(233, 240, 209, 1);
  static const Color green_214_226_179 = Color.fromRGBO(214, 226, 179, 1);
  static const Color green_190_200_160 = Color.fromRGBO(190, 200, 160, 1);
  static const Color green_188_208_125 = Color.fromRGBO(188, 208, 125, 1);
  static const Color green_200_203_191 = Color.fromRGBO(200, 203, 191, 1);
  static const Color green_247_250_238 = Color.fromRGBO(247, 250, 238, 1);


  //brown
  static const Color brown_59_48_48 = Color.fromRGBO(59, 48, 48, 1);
  static const Color brown_199_187_171 = Color.fromRGBO(199, 187, 171, 1);
  static const Color brown_112_103_91 = Color.fromRGBO(112, 103, 91, 1);
  static const Color brown_57_54_18 = Color.fromRGBO(57, 54, 18, 1);  //393612
  static const Color brown_197_186_165 = Color.fromRGBO(197, 186, 165, 1);  //#C5BAA5


//yellow
  static const Color yellow_251_227_140 = Color.fromRGBO(251, 227, 140, 1);  //fbe38c

//purple
  static const Color purple_120_96_221 = Color.fromRGBO(120, 96, 221, 1); //7860DD

}
