import '../../../../../../../model/reserve_calendar_model/models/event.dart';

class EventLayout {
  final Event event;
  final double leftOffset;
  final double width;

  final bool isLeftmost;   // 新增：是否為最左邊的事件
  final bool isRightmost; // 新增：是否為最右邊的事件


  EventLayout({
    required this.event,
    required this.leftOffset,
    required this.width,

    this.isLeftmost=false,
    this.isRightmost=false

  });
}