mixin ValidationMixin {
  bool isFieldEmpty(String fieldValue) => fieldValue.isEmpty;

  ///驗證email格式
  bool validateEmailAddress(String email) {
    return RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(email);
  }

  ///驗證身分證格式
  bool validateIDFormat(String id) {
    return RegExp(r'^[A-Za-z][12]\d{8}$').hasMatch(id);
  }

  ///驗證密碼格式
  //密碼要有8-12位數，最少要有一個大、小寫、特殊符號、數字
  bool validatePwdFormat(String pwd) {
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#\$%\^&\*])[A-Za-z\d!@#\$%\^&\*]{8,12}$')
        .hasMatch(pwd);
  }

  ///驗證舊式居留證
  bool validateResidentPermit(String id) {
    return RegExp(r'^[A-Za-z]{2}\d{8}$').hasMatch(id);
  }

  ///驗證新式居留證
  bool validateNewResidentPermit(String id) {
    return RegExp(r"^[A-Za-z]\d{9}$").hasMatch(id);
  }

  ///驗證手機號碼
  bool validatePhoneNumber(String phoneNumber) {
    return RegExp(r'^09\d{8}$').hasMatch(phoneNumber);
  }
}
