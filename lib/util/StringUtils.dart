import 'package:chinese_lunar_calendar/chinese_lunar_calendar.dart';
import 'package:lunar/lunar.dart' as lunar;
import 'package:intl/intl.dart';
import 'package:lunar_calendar_converter_new/lunar_solar_converter.dart';
import 'package:sbar_pos/extension/my_extension.dart';
import '../logger/my_print.dart';


class StringUtils {
  ///取得enum的value
  static String enumName(String enumS) {
    return enumS.substring(enumS.indexOf('.') + 1);
  }

  ///安全檢查字串
  ///null與空字串都回傳空字串
  static String safetyEmptyCheck(String? s) {
    if (s == null) {
      return "";
    } else {
      return s;
    }
  }

  ///判斷是否為html字串
  static bool isHtml(String value) {
    return RegExp(r"/^<([a-z]+)([^<]+)*(?:>(.*)<\/\1>|\s+\/>)$/")
        .hasMatch(value);
  }

  ///千分位逗號格式化
  static String formatMoney(int number) {
    return NumberFormat("#,##0", "en_US").format(number);
  }

  /// 千分位逗號格式化 (支援 double)
  static String formatMoneyForDouble(double number) {
    return NumberFormat("#,##0.##", "en_US").format(number);
  }


  ///將DateTime轉成String: yyyy-MM-dd
  static String dateTimeToString(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  ///將DateTime轉成String: yyyy-MM-dd HH:mm:ss
  static String dateTimeToStringYMDHMS(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(date);
  }

  ///將DateTime專成String: yyyy-MM-dd HH:mm
  static String dateTimeToStringYMDHM(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }

  ///將DateTime轉成String: HH:mm:ss
  static String dateTimeToStringHMS(DateTime date) {
    return DateFormat('HH:mm:ss').format(date);
  }

  ///將DateTime轉成String: HH:mm:ss
  static String dateTimeToStringHM(DateTime date) {
    return DateFormat('HH:mm').format(date);
  }

  ///將字串轉DateTime yyyy-MM-dd
  static DateTime stringToDateTime(String s) {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd");
    DateTime dateTime = dateFormat.parse(s);
    return dateTime;
  }

  ///將字串轉DateTime yyyy-M-dTHH:mm:ssZ
  static DateTime stringToDateTimeYMDHmsZ(String s) {
    DateFormat dateFormat = DateFormat("yyyy-M-dTHH:mm:ssZ");
    DateTime dateTime = dateFormat.parse(s);
    return dateTime;
  }

  ///將字串轉DateTime yyyy-M-dTHH:mm:ssZ
  static DateTime stringToDateTimeYMHmsZ(String s) {
    DateFormat dateFormat = DateFormat("yyyy-M-d HH:mm:ssZ");
    DateTime dateTime = dateFormat.parse(s);
    return dateTime;
  }

  ///將字串轉DateTime yyyy-M-dTHH:mm:
  static DateTime stringToDateTimeYMDHm(String s) {
    DateFormat dateFormat = DateFormat("yyyy-M-d HH:mm");
    DateTime dateTime = dateFormat.parse(s);
    return dateTime;
  }

  ///將字串轉DateTime HH:mm
  static DateTime stringToDateTimeYMDHHmm(String s) {
    DateFormat dateFormat = DateFormat("yyyy/M/d HH:mm");
    DateTime dateTime = dateFormat.parse(s);
    return dateTime;
  }

  ///將字串轉DateTime mm
  static DateTime stringToDateTimeMm(String s) {
    DateFormat dateFormat = DateFormat("mm");
    DateTime dateTime = dateFormat.parse(s);
    return dateTime;
  }

  ///將DateTime

  ///計算時間差
  static String calculatingDateTime(String begin, String end) {
    var beginTime = stringToDateTimeYMDHHmm(begin);
    var endTime = stringToDateTimeYMDHHmm(end);
    var result = ' ';
    if (endTime.isAfter(beginTime)) {
      var difference = endTime.difference(beginTime);
      result =
          '${padLeft0(difference.inHours)}:${padLeft0(difference.inMinutes % 60)}';
      return result;
    }
    return result;
  }

  ///取得上週星期日日期
  static DateTime getSunDayDateTime(DateTime datetime) {
    var week = datetime.weekday;
    return datetime.subtract(Duration(days: week));
  }

  ///取得該週星期六日期
  static DateTime getSaturdayDateTime(DateTime datetime) {
    var week = datetime.weekday;
    var saturday = 6 - week;
    return datetime.subtract(Duration(days: -saturday));
  }

  ///個位數補0
  static String padLeft0(int num) {
    var result = '';
    if (num < 10) {
      result = '0$num';
      return result;
    }
    return '$num';
  }

  /// 修改日期格式 2020-02-06 17:35 to 2020/02/06 17:35
  static String replaceDataFormat(String date) {
    var dateFormat = '';
    if (date.contains('-')) {
      dateFormat = date.replaceAll('-', '/');
      myPrint('date: $date');
      return dateFormat;
    }
    return date;
  }

  /// 2022/09/11 01:54 to 2019年09月11日(一) 01：54
  static String dateTimeToYMDWHM(String locale, String date) {
    //此正則針對api有傳星期(一)，將api的星期刪除掉再轉換格式
    if (date.isNullOrEmpty()) return "";
    RegExp reg = RegExp('[(].*[)]');
    date = date.replaceFirst(reg, ' ');
    DateTime dt = stringToDateTimeYMDHHmm(date);

    if ("zh-TW" == locale) {
      return "${dt.year}年${dt.month}月${dt.day}日(${toCHWeekDay(dt).replaceAll('星期', '')}) ${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}";
    } else {
      return "${dt.year}/${dt.month}/${dt.day}(${toEnWeekDay(dt)}) ${dt.hour.toString().padLeft(2, '0')}:${dt.minute.toString().padLeft(2, '0')}";
    }
  }

  ///min換算時常
  static String durationToString(int minutes) {
    var d = Duration(minutes: minutes);
    List<String> parts = d.toString().split(':');
    return '${parts[0].padLeft(2, '0')}:${parts[1].padLeft(2, '0')}';
  }

  ///判斷英文weekday
  static String toEnWeekDay(DateTime date) {
    switch (date.weekday) {
      case 1:
        return 'MON';
      case 2:
        return 'TUE';
      case 3:
        return 'WED';
      case 4:
        return 'THU';
      case 5:
        return 'FRI';
      case 6:
        return 'SAT';
      case 7:
        return 'SUN';
      default:
        return '';
    }
  }

  ///判斷中文weekday
  static String toCHWeekDay(DateTime date) {
    switch (date.weekday) {
      case 1:
        return '星期一';
      case 2:
        return '星期二';
      case 3:
        return '星期三';
      case 4:
        return '星期四';
      case 5:
        return '星期五';
      case 6:
        return '星期六';
      case 7:
        return '星期日';
      default:
        return '';
    }
  }

  ///日期轉農曆（國字）
  static String dateNumToLunarChinese(DateTime date) {
    Solar solar =
        Solar(solarYear: date.year, solarMonth: date.month, solarDay: date.day);
    int? lunar = LunarSolarConverter.solarToLunar(solar).lunarDay;
    switch (lunar) {
      case 1:
        return '正月';
      case 2:
        return '初二';
      case 3:
        return '初三';
      case 4:
        return '初四';
      case 5:
        return '初五';
      case 6:
        return '初六';
      case 7:
        return '初七';
      case 8:
        return '初八';
      case 9:
        return '初九';
      case 10:
        return '初十';
      case 11:
        return '十一';
      case 12:
        return '十二';
      case 13:
        return '十三';
      case 14:
        return '十四';
      case 15:
        return '十五';
      case 16:
        return '十六';
      case 17:
        return '十七';
      case 18:
        return '十八';
      case 19:
        return '十九';
      case 20:
        return '二十';
      case 21:
        return '廿一';
      case 22:
        return '廿二';
      case 23:
        return '廿三';
      case 24:
        return '廿四';
      case 25:
        return '廿五';
      case 26:
        return '廿六';
      case 27:
        return '廿七';
      case 28:
        return '廿八';
      case 29:
        return '廿九';
      case 30:
        return '三十';
      default:
        return '';
    }
  }

  ///日期轉農曆（農曆2024年 10月 25日 廿五 小雪）
  static String dateNumToLunarSolarTermYMD(DateTime date) {
    // 將DateTime 轉換為 Solar
    lunar.Solar solar = lunar.Solar.fromDate(date);

    // 使用 Solar 取得 Lunar
    lunar.Lunar lunarDate = solar.getLunar();

    // 取得農曆的年份、月份、日期
    int lunarYear = lunarDate.getYear();
    int lunarMonth = lunarDate.getMonth();
    int lunarDay = lunarDate.getDay();

    //取得節氣
    String? solarTerm = lunarDate.getJieQi();
    return '$lunarYear年 $lunarMonth月 $lunarDay日 ${solarTerm ?? ''}';
  }

  ///日期轉農曆（數字）
  static String dateNumToLunarMD(DateTime date) {
    final lunarCalendar = LunarCalendar.from(
      utcDateTime: date.toUtc(),
    );
    final lunar = lunarCalendar.lunarDate;

    return '農曆 ${lunar.lunarMonth.number} 月 ${lunar.lunarDay} 號';
  }

  static String numberToChinese(int number) {
    const chineseNumbers = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    if (number > 0 && number <= 10) {
      return chineseNumbers[number - 1];
    }
    return number.toString(); // fallback
  }

}
