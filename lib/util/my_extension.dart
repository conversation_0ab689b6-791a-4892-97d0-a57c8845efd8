// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart' as Intl;
// import 'package:intl/intl.dart' as DateFormat;
//
//
// import '../util/StringUtils.dart';
//
// part 'list_extension.dart';
//
// part 'object_extension.dart';
//
// part 'string_extension.dart';
//
// part 'datetime_extension.dart';
//
// part 'int_extension.dart';
//
// part 'iterable_extension.dart';
//
// extension MyTextTheme on TextTheme {
// //   //pingFangTC_Regular
// //   TextStyle get fz8PFTCR => TextStyle(
// //       fontSize: 8.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz9PFTCR => TextStyle(
// //       fontSize: 9.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz10PFTCR => TextStyle(
// //       fontSize: 10,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz11PFTCR => TextStyle(
// //       fontSize: 11,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz12PFTCR => TextStyle(
// //       fontSize: 12.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz13PFTCR => TextStyle(
// //       fontSize: 13.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz14PFTCR => TextStyle(
// //       fontSize: 14.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz15PFTCR => TextStyle(
// //       fontSize: 15.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz16PFTCR => TextStyle(
// //       fontSize: 16.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz17PFTCR => TextStyle(
// //       fontSize: 17.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz36PFTCR => TextStyle(
// //       fontSize: 36.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz26PFTCR => TextStyle(
// //       fontSize: 26.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz20PFTCR => TextStyle(
// //       fontSize: 20.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   TextStyle get fz18PFTCR => TextStyle(
// //       fontSize: 18.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Regular);
// //
// //   //pingFangTC_Medium
// //   TextStyle get appBar => TextStyle(
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium,
// //       fontSize: 17.sp);
// //
// //   TextStyle get fz11PFTCM => TextStyle(
// //       fontSize: 11.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz12PFTCM => TextStyle(
// //       fontSize: 12.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz13PFTCM => TextStyle(
// //       fontSize: 13.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz14PFTCM => TextStyle(
// //       fontSize: 14.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz15PFTCM => TextStyle(
// //       fontSize: 15.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz16PFTCM => TextStyle(
// //       fontSize: 16.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz17PFTCM => TextStyle(
// //       fontSize: 17.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   TextStyle get fz25PFTCM => TextStyle(
// //       fontSize: 25.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //
// //   //sFProDisplay_Regular
// //   TextStyle get fz17SFPTR => TextStyle(
// //       fontSize: 17.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.sFProDisplay_Regular);
// //
// //   TextStyle get fz15SFPTR => TextStyle(
// //       fontSize: 15.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.sFProDisplay_Regular);
// //
// //
// //   TextStyle get fz13SFPTR => TextStyle(
// //       fontSize: 13.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.sFProDisplay_Regular);
// //
// //   TextStyle get fz16SFPDR => TextStyle(
// //       fontSize: 16.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.sFProDisplay_Regular);
// //
// //   TextStyle get fz13SFPDR => TextStyle(
// //       fontSize: 13.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.sFProDisplay_Regular);
// //
// //   TextStyle get fz11SFPDR => TextStyle(
// //       fontSize: 11.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.sFProDisplay_Regular);
// //
// //   //pingFangTC_Thin
// //   TextStyle get fz16PFTCT => TextStyle(
// //       fontSize: 16.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_Thin);
// //
// //   //pingFang_SC_Semibold
// //   TextStyle get fz16SCSB => TextStyle(
// //       fontSize: 16.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_SC_Semibold);
// //
// //   TextStyle get fz10SCSB => TextStyle(
// //       fontSize: 10.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_SC_Semibold);
// //
// //   TextStyle get fz12SCSB => TextStyle(
// //       fontSize: 12.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_SC_Semibold);
// //
// //   TextStyle get fz18SCSB => TextStyle(
// //       fontSize: 18.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_SC_Semibold);
// //
// //   TextStyle get fz20SCSB => TextStyle(
// //       fontSize: 20.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_SC_Semibold);
// //
// //   //pingFang_Light
// //   TextStyle get fz9PFL => TextStyle(
// //       fontSize: 9.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_Light);
// //
// //   TextStyle get fz10PFL => TextStyle(
// //       fontSize: 10.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_Light);
// //
// //   TextStyle get fz13PFL => TextStyle(
// //       fontSize: 13.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_Light);
// //
// //   TextStyle get fz17PFL => TextStyle(
// //       fontSize: 17.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.pingFang_Light);
// //
// //   //fontsFree_Net_SFProText_Regular
// //   TextStyle get fz12FNSFPTR => TextStyle(
// //       fontSize: 12.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.fontsFree_Net_SFProText_Regular);
// //
// //   TextStyle get fz17FNSFPTR => TextStyle(
// //       fontSize: 17.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.fontsFree_Net_SFProText_Regular);
// //
// //   TextStyle get fz11FNSFPTR => TextStyle(
// //       fontSize: 11.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.fontsFree_Net_SFProText_Regular);
// //
// //   TextStyle get fz15FNSFPTR => TextStyle(
// //       fontSize: 15.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.fontsFree_Net_SFProText_Regular);
// //
// //   TextStyle get fz13FNSFPTR => TextStyle(
// //       fontSize: 13.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.fontsFree_Net_SFProText_Regular);
// //
// //   //notoSansTC_Regular
// //   TextStyle get fz40NOTOSANSTCR => TextStyle(
// //       fontSize: 40.sp,
// //       color: Colors.black,
// //       height: 1,
// //       fontFamily: R.fontFamily.notoSansTC_Regular);
// //
// //   //app bar style
// //   TextStyle get appBarStyle => TextStyle(
// //       color: Colors.black,
// //       fontSize: 17.sp,
// //       fontFamily: R.fontFamily.pingFangTC_Medium);
// //
// //   //PingFangSC-Regular
// // TextStyle get fz13PFSCR => TextStyle(
// //     fontSize: 13.sp,
// //   color: Colors.black,
// //   height: 1,
// //     fontFamily: R.fontFamily.pingFang_SC_Regular
// //
// // );
// }
