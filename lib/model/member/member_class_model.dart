class MemberClassModel {
  final String type;
  final int typeId; //(暫定：0 全部，1 臉部，2 身體，3 手足，4 頭部舒壓，5 孕媽咪，6 男士保養，7 眉睫)
  final String orderId;
  final String checkOutDate;
  final String checkOutTime;
  final List<ClassInnerInfo> classInnerInfo;


  MemberClassModel({
    required this.type,
    required this.typeId,
    required this.orderId,
    required this.checkOutDate,
    required this.checkOutTime,
    required this.classInnerInfo
  });
}

class ClassInnerInfo{

  final String className;
  final String classNameInfo;
  final int purchasedClass; //已購買堂數
  final int remainingClass; //剩餘堂數
  final int paidClass; //已付款堂數
  final int unPaidClass; //未付款堂數

  ClassInnerInfo({
    required this.className,
    required this.classNameInfo,
    required this.purchasedClass,
    required this.remainingClass,
    required this.paidClass,
    required this.unPaidClass,
  });

}