class MemberData {
  int? memberId;
  String? name;
  String? phone;
  String? email;
  String? id;
  String? birthday;
  String? sex;
  String? address;
  int? status;
  String? account;
  String? createTime;
  String? updateTime;

  MemberData({
    this.id,
    this.name,
    this.phone,
    this.email,
    this.status,
    this.account,
    this.createTime,
    this.updateTime,
    this.sex,
    this.birthday,
    this.address,
    this.memberId
  });

  MemberData.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    phone = json['phone'];
    email = json['email'];
    status = json['mstatus'];
    account = json['account'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    memberId = json['memberId'];
    sex =  json['sex'];
    birthday =  json['birthday'];
    address =  json['address'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['phone'] = phone;
    data['email'] = email;
    data['mstatus'] = status;
    data['account'] = account;
    data['createTime'] = createTime;
    data['updateTime'] = updateTime;
    data['memberId'] = memberId;
    data['sex'] = sex;
    data['birthday'] = birthday;
    data['address'] = address;
    return data;
  }
}