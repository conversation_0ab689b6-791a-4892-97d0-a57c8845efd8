class MemberHistoryOrderModel{
  final String orderId; //訂單編號
  final String invoiceId; //發票號碼
  final String checkOutTime; //結帳時間
  final String somPrice; //銷售訂單母單
  final String price; //本次付款金額
  final String priceInfo; //本次付款金額(說明)
  final String orderStatue; //訂單狀態
  final int orderStatueCode; //訂單狀態(暫定：０為未完成付款，１為已付款)
  final String unPaidPrice; //未付款金額
  final String memberName; //會員
  final String operatorName; //操作員
  final String? salesMane;  //銷售人員
  final String? totalSalesOrderAmount;  //銷售訂單總金額


  MemberHistoryOrderModel({
    required this.orderId,
    required this.invoiceId,
    required this.checkOutTime,
    required this.somPrice,
    required this.price,
    required this.priceInfo,
    required this.orderStatue,
    required this.orderStatueCode,
    required this.unPaidPrice,
    required this.memberName,
    required this.operatorName,
     this.salesMane,
     this.totalSalesOrderAmount,
  });

  MemberHistoryOrderModel copyWith({
    String? orderId,
    String? invoiceId,
    String? checkOutTime,
    String? somPrice,
    String? price,
    String? priceInfo,
    String? orderStatue,
    int? orderStatueCode,
    String? unPaidPrice,
    String? memberName,
    String? operatorName,
  }) {
    return MemberHistoryOrderModel(
      orderId: orderId ?? this.orderId,
      invoiceId: invoiceId ?? this.invoiceId,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      somPrice: somPrice ?? this.somPrice,
      price: price ?? this.price,
      priceInfo: priceInfo ?? this.priceInfo,
      orderStatue: orderStatue ?? this.orderStatue,
      orderStatueCode: orderStatueCode ?? this.orderStatueCode,
      unPaidPrice: unPaidPrice ?? this.unPaidPrice,
      memberName: memberName ?? this.memberName,
      operatorName: operatorName ?? this.operatorName,
    );
  }
}