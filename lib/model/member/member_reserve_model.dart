class MemberReserveModel{
  final String orderId; //訂單編號
  final String reserveId; //預約單編號
  final String orderTime; //預約下單時間
  final String orderClassTime; //預約課程時間
  final String className; //課程名稱
  final String classNameInfo; //課程名稱詳細說明(ex:濕潤美肌課程(75分鐘))
  final String memberName; //會員
  final String memberPhone; //會員手機
  final String beautician; //美容師
  final String customerSelectionType; //指定情況(ex:客戶指定)
  final String orderStatue; //訂單狀態
  final String orderStatueInfo; //訂單狀態說明(ex:線上),
  final String week; //星期
  final bool isClassConfirm;  //課程確認書是否已簽
  final double price;

  MemberReserveModel({
    required this.orderId,
    required this.reserveId,
    required this.orderTime,
    required this.orderClassTime,
    required this.className,
    required this.classNameInfo,
    required this.memberName,
    required this.memberPhone,
    required this.beautician,
    required this.customerSelectionType,
    required this.orderStatue,
    required this.orderStatueInfo,
    required this.week,
    required this.price,
    required this.isClassConfirm,
  });
}