import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import '../../api/api_service.dart';
import '../../flavor/flavor.dart';
import '../../logger/my_print.dart';
import '../../model/member/member_data.dart';

abstract class MemberRepository {
  bool get isMember;

  String get accessToken;

  Future<String> getToken();

  Future<void> login(String account, String password);

  Future<void> logout();

  Future<String> memberLoggedInfo();

  Future<MemberData> memberList();

  Future<MemberData> modifyMember();

  Future<void> registerMember();

  Future<void> delete();

  Future<void> refreshToken();

  @visibleForTesting
  Future<void> expire() async {}
}

MemberRepository _globalAuthMemberRepository = MemberRepositoryImpl();
MemberRepository get getAuthMemberRepository => _globalAuthMemberRepository;


class MemberRepositoryImpl extends MemberRepository {

  @override
  bool isMember = false;

  @override
  String accessToken = '';
  // String accessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MCwiYWNjb3VudCI6bnVsbCwibG9nb3V0IjpmYWxzZSwiZXhwaXJlIjoiMjAyNC0wMS0wOVQxNToxNTo1MC45NTQwMTY2KzA4OjAwIn0.IATFCi0f00uscnx9pxxDRRGhypYpo41FNh1eoj3XlXM';

  @override
  Future<void> delete() {
    // TODO: implement delete
    throw UnimplementedError();
  }

  @override
  Future<String> getToken() async {

    final headers = {
      'Accept': 'text/plain',
    };
    final dio = Dio();

    final response = await dio.request(
      '${FlavorConfig().values.baseUrl}ApiToken/GetToken',
      options: Options(
        method: 'POST',
        headers: headers,
      ),
    );


    if (response.statusCode == 200) {
      myPrint(jsonEncode(response.data));
      myPrint(response.data['retData']);

      return response.data['retData'];
    }
    else {
      myPrint(response.statusMessage);
      throw 'get token error';
    }
    return '';
  }

  @override
  Future<void> login(String account, String password) async {
    // Object? rethrowErrorTemp;
    // final token = await ApiService.create().login(account, password)
    //     .then((value) => value.isSuccessful ? value : throw const ApiException())
    //     .then((value) => ResLogin.fromJson(jsonDecode(value.bodyString)), onError: (error, stack) => throw const ApiException('json 格式錯誤'))
    //     .then((value) => value.success == true ? value : (throw ApiException('login is failure, message: ${value.message}')))
    //     .then((value) => value.retData?.token)
    //     .catchError((error) {
    //   myPrint('catch error in repository');
    //   rethrowErrorTemp = error;
    //   return null;
    // });
    //
    // if(token == null) {
    //   myPrint('login failure');
    //   accessToken = '';
    //   isMember = false;
    //   if(rethrowErrorTemp != null) {
    //     throw rethrowErrorTemp!;
    //   }
    //   return;
    // }
    //
    // myPrint('success: $token');
    // accessToken = token;
    // isMember = true;
  }

  /// 清除登入資訊
  @override
  Future<void> logout() async {
    accessToken = '';
    isMember = false;
  }

  @override
  Future<MemberData> memberList() {
    // TODO: implement memberList
    throw UnimplementedError();
  }

  @override
  Future<String> memberLoggedInfo() {
    // TODO: implement memberLoggedInfo
    throw UnimplementedError();
  }

  @override
  Future<MemberData> modifyMember() {
    // TODO: implement modifyMember
    throw UnimplementedError();
  }

  @override
  Future<void> registerMember() {
    // TODO: implement registerMember
    throw UnimplementedError();
  }

  @override
  Future<void> refreshToken() async {
    final token = await getToken();
    accessToken = token;
  }


  @override
  Future<void> expire() async {

    accessToken = '';
  }
}