class TopUpRecordModel {
  final String orderId; //訂單編號
  final String cashId; //儲值編號
  final String checkOutTime; //結帳時間
  final String somPrice; //銷售訂單母單
  final String price; //金額
  final bool isPriceMinus; //金額是否為-
  final String useFor; //用途
  final String walletName; //錢包名稱
  final String memberName; //會員
  final String operatorName; //操作員

  const TopUpRecordModel({
    required this.orderId,
    required this.cashId,
    required this.checkOutTime,
    required this.somPrice,
    required this.price,
    required this.useFor,
    required this.walletName,
    required this.memberName,
    required this.operatorName,
    required this.isPriceMinus,
  });
}
