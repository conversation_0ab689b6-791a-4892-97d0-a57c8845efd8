class ApiException implements Exception {
  final String message;
  ApiException(this.message);

  @override
  String toString() => 'ApiException: $message';
}

class BasicRes<T> {
  final bool success;
  final String? message;
  final T? data;

  BasicRes({required this.success, this.message, this.data});

  factory BasicRes.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) parseT,
  ) {
    final success = json['success'] as bool? ?? false;
    final message = json['message'] as String?;
    T? data;
    if (json.containsKey('data')) {
      data = parseT(json['data']);
    }
    return BasicRes<T>(success: success, message: message, data: data);
  }

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) {
    return {
      'success': success,
      'message': message,
      'data': data == null ? null : toJsonT(data as T),
    };
  }
}
