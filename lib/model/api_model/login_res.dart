import 'dart:convert';

class LoginRes {
  String? accessToken;
  String? tokenType;
  int? expiresIn;

  LoginRes({this.accessToken, this.tokenType, this.expiresIn});

  LoginRes copyWith({String? accessToken, String? tokenType, int? expiresIn}) =>
      LoginRes(
        accessToken: accessToken ?? this.accessToken,
        tokenType: tokenType ?? this.tokenType,
        expiresIn: expiresIn ?? this.expiresIn,
      );

  factory LoginRes.fromRawJson(String str) =>
      LoginRes.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory LoginRes.fromJson(Map<String, dynamic> json) => LoginRes(
    accessToken: json["access_token"],
    tokenType: json["token_type"],
    expiresIn: json["expires_in"],
  );

  Map<String, dynamic> toJson() => {
    "access_token": accessToken,
    "token_type": tokenType,
    "expires_in": expiresIn,
  };
}
