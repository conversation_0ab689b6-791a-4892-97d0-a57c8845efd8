class ResEmployees {
  bool? success;
  String? message;
  List<Data>? data;
  Pagination? pagination;

  ResEmployees({this.success, this.message, this.data, this.pagination});

  ResEmployees.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
    pagination = json['pagination'] != null
        ? new Pagination.fromJson(json['pagination'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.pagination != null) {
      data['pagination'] = this.pagination!.toJson();
    }
    return data;
  }
}

class Data {
  String? id;
  String? userId;
  String? employeeNumber;
  String? employeeName;
  String? gender;
  String? birthDate;
  String? email;
  String? phone;
  String? address;
  String? roleCode;
  String? roleName;
  String? departmentCode;
  String? departmentName;
  String? zoneCode;
  String? zoneName;
  String? storeCode;
  String? storeName;
  String? startDate;
  String? reportDate;
  String? insuranceEndDate;
  String? transferDate;
  String? state;
  String? modificationTime;
  String? updatedByName;
  String? updatedAt;
  String? updatedBy;

  Data(
      {this.id,
        this.userId,
        this.employeeNumber,
        this.employeeName,
        this.gender,
        this.birthDate,
        this.email,
        this.phone,
        this.address,
        this.roleCode,
        this.roleName,
        this.departmentCode,
        this.departmentName,
        this.zoneCode,
        this.zoneName,
        this.storeCode,
        this.storeName,
        this.startDate,
        this.reportDate,
        this.insuranceEndDate,
        this.transferDate,
        this.state,
        this.modificationTime,
        this.updatedByName,
        this.updatedAt,
        this.updatedBy});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    employeeNumber = json['employee_number'];
    employeeName = json['employee_name'];
    gender = json['gender'];
    birthDate = json['birth_date'];
    email = json['email'];
    phone = json['phone'];
    address = json['address'];
    roleCode = json['role_code'];
    roleName = json['role_name'];
    departmentCode = json['department_code'];
    departmentName = json['department_name'];
    zoneCode = json['zone_code'];
    zoneName = json['zone_name'];
    storeCode = json['store_code'];
    storeName = json['store_name'];
    startDate = json['start_date'];
    reportDate = json['report_date'];
    insuranceEndDate = json['insurance_end_date'];
    transferDate = json['transfer_date'];
    state = json['state'];
    modificationTime = json['modification_time'];
    updatedByName = json['updated_by_name'];
    updatedAt = json['updated_at'];
    updatedBy = json['updated_by'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['employee_number'] = this.employeeNumber;
    data['employee_name'] = this.employeeName;
    data['gender'] = this.gender;
    data['birth_date'] = this.birthDate;
    data['email'] = this.email;
    data['phone'] = this.phone;
    data['address'] = this.address;
    data['role_code'] = this.roleCode;
    data['role_name'] = this.roleName;
    data['department_code'] = this.departmentCode;
    data['department_name'] = this.departmentName;
    data['zone_code'] = this.zoneCode;
    data['zone_name'] = this.zoneName;
    data['store_code'] = this.storeCode;
    data['store_name'] = this.storeName;
    data['start_date'] = this.startDate;
    data['report_date'] = this.reportDate;
    data['insurance_end_date'] = this.insuranceEndDate;
    data['transfer_date'] = this.transferDate;
    data['state'] = this.state;
    data['modification_time'] = this.modificationTime;
    data['updated_by_name'] = this.updatedByName;
    data['updated_at'] = this.updatedAt;
    data['updated_by'] = this.updatedBy;
    return data;
  }
}

class Pagination {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? total;
  int? from;
  int? to;

  Pagination(
      {this.currentPage,
        this.lastPage,
        this.perPage,
        this.total,
        this.from,
        this.to});

  Pagination.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    lastPage = json['last_page'];
    perPage = json['per_page'];
    total = json['total'];
    from = json['from'];
    to = json['to'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    data['last_page'] = this.lastPage;
    data['per_page'] = this.perPage;
    data['total'] = this.total;
    data['from'] = this.from;
    data['to'] = this.to;
    return data;
  }
}
