class ResMyProfile {
  bool? success;
  String? message;
  Data? data;

  ResMyProfile({this.success, this.message, this.data});

  ResMyProfile.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? id;
  String? userType;
  String? name;
  Null? username;
  Null? phone;
  String? email;
  Role? role;

  Data(
      {this.id,
        this.userType,
        this.name,
        this.username,
        this.phone,
        this.email,
        this.role});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userType = json['user_type'];
    name = json['name'];
    username = json['username'];
    phone = json['phone'];
    email = json['email'];
    role = json['role'] != null ? new Role.fromJson(json['role']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_type'] = this.userType;
    data['name'] = this.name;
    data['username'] = this.username;
    data['phone'] = this.phone;
    data['email'] = this.email;
    if (this.role != null) {
      data['role'] = this.role!.toJson();
    }
    return data;
  }
}

class Role {
  String? code;
  String? name;
  String? groupCode;
  String? groupName;

  Role({this.code, this.name, this.groupCode, this.groupName});

  Role.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    name = json['name'];
    groupCode = json['group_code'];
    groupName = json['group_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['name'] = this.name;
    data['group_code'] = this.groupCode;
    data['group_name'] = this.groupName;
    return data;
  }
}
