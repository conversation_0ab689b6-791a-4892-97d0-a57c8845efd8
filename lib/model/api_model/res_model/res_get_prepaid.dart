class ResGetPrepaid {
  int? code;
  String? message;
  List<Data>? data;

  ResGetPrepaid({this.code, this.message, this.data});

  ResGetPrepaid.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  String? id;
  String? prepaidName;
  String? prepaidAmount;
  String? prepaidDiscount;
  String? description;
  String? status;
  String? usedAmount;
  String? createdAt;
  String? updatedAt;
  List<String>? programIds;

  Data(
      {this.id,
        this.prepaidName,
        this.prepaidAmount,
        this.prepaidDiscount,
        this.description,
        this.status,
        this.usedAmount,
        this.createdAt,
        this.updatedAt,
        this.programIds});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    prepaidName = json['prepaid_name'];
    prepaidAmount = json['prepaid_amount'];
    prepaidDiscount = json['prepaid_discount'];
    description = json['description'];
    status = json['status'];
    usedAmount = json['used_amount'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    programIds = json['program_ids'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['prepaid_name'] = this.prepaidName;
    data['prepaid_amount'] = this.prepaidAmount;
    data['prepaid_discount'] = this.prepaidDiscount;
    data['description'] = this.description;
    data['status'] = this.status;
    data['used_amount'] = this.usedAmount;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['program_ids'] = this.programIds;
    return data;
  }
}
