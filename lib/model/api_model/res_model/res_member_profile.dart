class ResMemberProfile {
  bool? success;
  String? message;
  Data? data;

  ResMemberProfile({this.success, this.message, this.data});

  ResMemberProfile.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    message = json['message'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['message'] = this.message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? userId;
  String? memberId;
  String? name;
  String? phone;
  String? email;
  String? birthDate;
  String? gender;
  int? city;
  String? cityName;
  int? district;
  String? districtName;
  String? emergencyName;
  String? emergencyPhone;
  String? knownPipeline;
  String? createdAt;
  String? updatedAt;

  Data(
      {this.userId,
        this.memberId,
        this.name,
        this.phone,
        this.email,
        this.birthDate,
        this.gender,
        this.city,
        this.cityName,
        this.district,
        this.districtName,
        this.emergencyName,
        this.emergencyPhone,
        this.knownPipeline,
        this.createdAt,
        this.updatedAt});

  Data.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    memberId = json['member_id'];
    name = json['name'];
    phone = json['phone'];
    email = json['email'];
    birthDate = json['birth_date'];
    gender = json['gender'];
    city = json['city'];
    cityName = json['city_name'];
    district = json['district'];
    districtName = json['district_name'];
    emergencyName = json['emergency_name'];
    emergencyPhone = json['emergency_phone'];
    knownPipeline = json['known_pipeline'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this.userId;
    data['member_id'] = this.memberId;
    data['name'] = this.name;
    data['phone'] = this.phone;
    data['email'] = this.email;
    data['birth_date'] = this.birthDate;
    data['gender'] = this.gender;
    data['city'] = this.city;
    data['city_name'] = this.cityName;
    data['district'] = this.district;
    data['district_name'] = this.districtName;
    data['emergency_name'] = this.emergencyName;
    data['emergency_phone'] = this.emergencyPhone;
    data['known_pipeline'] = this.knownPipeline;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
