class ResGetPrograms {
  bool? success;
  List<Data>? data;
  Meta? meta;

  ResGetPrograms({this.success, this.data, this.meta});

  ResGetPrograms.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
    meta = json['meta'] != null ? new Meta.fromJson(json['meta']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    if (this.meta != null) {
      data['meta'] = this.meta!.toJson();
    }
    return data;
  }
}

class Data {
  String? id;
  String? name;
  String? code;
  String? serviceItemName;
  int? totalCourseDuration;
  String? retailPrice;
  String? status;
  String? courseDescription;
  String? createdAt;
  String? updatedAt;
  String? updatedByName;

  Data(
      {this.id,
        this.name,
        this.code,
        this.serviceItemName,
        this.totalCourseDuration,
        this.retailPrice,
        this.status,
        this.courseDescription,
        this.createdAt,
        this.updatedAt,
        this.updatedByName});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    code = json['code'];
    serviceItemName = json['service_item_name'];
    totalCourseDuration = json['total_course_duration'];
    retailPrice = json['retail_price'];
    status = json['status'];
    courseDescription = json['course_description'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    updatedByName = json['updated_by_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['code'] = this.code;
    data['service_item_name'] = this.serviceItemName;
    data['total_course_duration'] = this.totalCourseDuration;
    data['retail_price'] = this.retailPrice;
    data['status'] = this.status;
    data['course_description'] = this.courseDescription;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['updated_by_name'] = this.updatedByName;
    return data;
  }
}

class Meta {
  int? currentPage;
  int? lastPage;
  int? perPage;
  int? total;

  Meta({this.currentPage, this.lastPage, this.perPage, this.total});

  Meta.fromJson(Map<String, dynamic> json) {
    currentPage = json['current_page'];
    lastPage = json['last_page'];
    perPage = json['per_page'];
    total = json['total'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['current_page'] = this.currentPage;
    data['last_page'] = this.lastPage;
    data['per_page'] = this.perPage;
    data['total'] = this.total;
    return data;
  }
}
