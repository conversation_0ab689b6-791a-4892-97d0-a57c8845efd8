class ClassConfirmModel {
  final String orderId;
  final String memberName;
  final String memberPhone;
  final bool isAllSign;
  final List<ClassConfirmInfo> classConfirmInnerInfo;

  ClassConfirmModel({
    required this.isAllSign,
    required this.orderId,
    required this.memberName,
    required this.memberPhone,
    required this.classConfirmInnerInfo,
  });
}

class ClassConfirmInfo {
  final String classId; //課程商品編號
  final String className; //課程名稱
  final List<ClassConfirmInnerInfo> classConfirmInnerInfo;


  ClassConfirmInfo({
    required this.classId,
    required this.className,
    required this.classConfirmInnerInfo,

  });
}

class ClassConfirmInnerInfo{
  final String reserveId; //預約單編號
  final String reserveClassTime; //預約課程時間
  final bool isSign; //是否已簽


  ClassConfirmInnerInfo({
    required this.reserveId,
    required this.reserveClassTime,
    required this.isSign,
  });
}
