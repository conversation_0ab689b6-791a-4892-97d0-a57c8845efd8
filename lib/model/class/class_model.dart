class ClassModel {
  final String classId;
  final String title;
  final String subTitle;
  final double price;
  int selectAmount;
   int totalAmount;
  final int type;  //暫定(1:商品，2:預約與課程，3:儲值金)

  ClassModel({
    required this.classId,
    required this.title,
    required this.subTitle,
    required this.price,
    required this.selectAmount,
    required this.totalAmount,
    required this.type,
  });
}
