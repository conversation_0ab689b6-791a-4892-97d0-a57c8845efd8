class ProductModel {
  final String productId;
  final String title;
  final String? subTitle;
  final double price;
  int selectAmount;
  int totalAmount;
  final int type; //暫定(1:商品，2:預約與課程，3:儲值金)
  bool isSelect;
  bool isTempSelection;

  ProductModel({
    required this.productId,
    required this.title,
    required this.price,
    required this.selectAmount,
    required this.totalAmount,
    required this.type,
    required this.isSelect,
    required this.isTempSelection,
    this.subTitle,
  });

  /// 複製一份獨立的 ProductModel
  ProductModel copy() {
    return ProductModel(
      productId: productId,
      title: title,
      subTitle: subTitle,
      price: price,
      selectAmount: selectAmount,
      totalAmount: totalAmount,
      type: type,
      isSelect: isSelect, isTempSelection: isTempSelection,
    );
  }

  /// 判斷兩個物件是否相等（避免 memory address 比較問題）
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
          other is ProductModel &&
              runtimeType == other.runtimeType &&
              productId == other.productId &&
              selectAmount == other.selectAmount &&
              totalAmount == other.totalAmount &&
              isSelect == other.isSelect &&
              isTempSelection == other.isTempSelection;

  @override
  int get hashCode =>
      productId.hashCode ^
      selectAmount.hashCode ^
      totalAmount.hashCode ^
      isTempSelection.hashCode ^
      isSelect.hashCode;
}
