
import '../../widget/reserve_calender_widget/card_widget/calender_event_card_widget.dart';

class CalenderMonthEventDataModel{

  final Map<DateTime, List<CalenderEventCardWidget>>? calenderEventDataList;
  final DateTime? date;

  CalenderMonthEventDataModel({
    this.calenderEventDataList,
    this.date,
  });
  DateTime get safeDate => date ?? DateTime.now();

  CalenderMonthEventDataModel copy(){

    return CalenderMonthEventDataModel(
        calenderEventDataList: calenderEventDataList,
        date: date
    );
  }

}