class TimeTableEventModel {
  final String title; // 課程名稱（綠色）
  final String clientName; // 會員人名（黑色）
  final String status; // 狀態（如 "No Show"）
  final int? price;
  final int? deposit;
  final String?  orderNumber;
  final String? phoneNumber;

  TimeTableEventModel({
    required this.title,
    required this.clientName,
    required this.status,
    this.price,
    this.deposit,
    this.orderNumber,
    this.phoneNumber
  });

  TimeTableEventModel copyWith({
    String? title,
    String? clientName,
    String? status,
    int? price,
    int? deposit,
    String? orderNumber,
    String? phoneNumber
  }) {
    return TimeTableEventModel(
      title: title ?? this.title,
      clientName: clientName ?? this.clientName,
      status: status ?? this.status,
      price: price ?? this.price,
      deposit: deposit ?? this.deposit,
      orderNumber: orderNumber ?? this.orderNumber,
      phoneNumber: phoneNumber ?? this.phoneNumber
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeTableEventModel &&
        other.title == title &&
        other.clientName == clientName &&
        other.status == status &&
        other.price == price &&
        other.deposit == deposit &&
        other.orderNumber==orderNumber &&
        other.phoneNumber==phoneNumber
    ;

  }

  @override
  int get hashCode =>
      title.hashCode ^
      clientName.hashCode ^
      status.hashCode ^
      price.hashCode ^
      deposit.hashCode^
      orderNumber.hashCode^
      phoneNumber.hashCode
  ;
}
