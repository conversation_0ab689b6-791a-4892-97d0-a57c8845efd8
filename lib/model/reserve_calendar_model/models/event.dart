class Event {
  final DateTime start;
  final DateTime end;
  final String title;
  final String? member;
  final String? beautician;
  final int? price;
  final int? phone;
  final String? orderNumber;

  Event(this.start, this.end, this.title, {this.member, this.beautician,this.price,this.phone,this.orderNumber});

  Event.withTime({

    required Duration startTime,
    required Duration endTime,
    required this.title,
    this.member,
    this.beautician,
    this.price,
    this.phone,
    this.orderNumber
}): start=DateTime(1970,1,1).add(startTime), end=DateTime(1970,1,1).add(endTime);

  // 建立新的 Event 實例，並設定正確的日期
  Event copyWithDate (DateTime EventDate){
// 提取原本的時間部分
    Duration startTimeOfDay=Duration(
      hours: start.hour,
      minutes: start.minute,
    );
    Duration endTimeOfDay=Duration(
      hours: end.hour,
      minutes: end.minute
    );
    // 將時間部分加到目標日期上
    DateTime newStartTime=DateTime(EventDate.year,EventDate.month,EventDate.day).add(startTimeOfDay);
    DateTime newEndTime=DateTime(EventDate.year,EventDate.month,EventDate.day).add(endTimeOfDay);

    return Event(
        newStartTime,
        newEndTime,
      title,
      member: member,
      beautician: beautician,
      price: price,
      phone: phone,
      orderNumber: orderNumber
    );
  }
}