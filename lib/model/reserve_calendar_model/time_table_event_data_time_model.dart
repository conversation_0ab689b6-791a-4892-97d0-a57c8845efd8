import 'package:flutter/material.dart';
import 'package:sbar_pos/model/reserve_calendar_model/time_table_event_model.dart';


/// 帶時間信息的事件數據
class TimeTableEventDataTimeModel extends TimeTableEventModel {
  // final TimeOfDay startTime;
  // final TimeOfDay endTime;

  final String startTime;
  final String endTime;

  final int? price;
  final int? depoist;

  final String? orderNumber;
  final String? phoneNumber;

  TimeTableEventDataTimeModel({
    required super.title,
    required super.clientName,
    required super.status,
    this.price,
    this.depoist,
    this.orderNumber,
    this.phoneNumber,
    required this.startTime,
    required this.endTime,
  }) : super(price: price,deposit: depoist,orderNumber:orderNumber,phoneNumber: phoneNumber);

  @override
  TimeTableEventDataTimeModel copyWith({
    String? title,
    String? clientName,
    String? status,
    int? price,
    int? deposit,
    String? orderNumber,
    String? phoneNumber,
    String? startTime,
    String? endTime,
  }) {
    return TimeTableEventDataTimeModel(
        title: title ?? this.title,
        clientName: clientName ?? this.clientName,
        status: status ?? this.status,
        price: price ?? this.price,
        depoist: depoist ?? this.depoist,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        orderNumber: orderNumber ?? this.orderNumber,
        phoneNumber: phoneNumber ?? this.phoneNumber
    );
  }
}