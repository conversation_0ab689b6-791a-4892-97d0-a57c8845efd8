class CourseOrderDetailsData {

  final String? date;
  final String? week;
  final String? orderNumber;
  final String? memberName;
  final String? phoneNumber;
  final String? beauticianName;
  final String? courseName;
  final String? startTime;
  final String? endTime;
  final int? deposit;
  final String? depositState;
  final double? price;


  CourseOrderDetailsData({
     this.date,
     this.week,
     this.orderNumber,
     this.phoneNumber,
     this.memberName,
     this.beauticianName,
     this.courseName,
     this.startTime,
     this.endTime,
     this.deposit,
     this.depositState,
     this.price
});

  CourseOrderDetailsData copy(){
    return CourseOrderDetailsData(
      date: date,
      week: week ,
      orderNumber: orderNumber ,
      memberName: memberName ,
      phoneNumber: phoneNumber ,
      beauticianName: beauticianName ,
      courseName: courseName,
      startTime: startTime ,
      endTime: endTime,
      deposit: deposit,
      depositState: depositState,
      price: price,
    );
  }

}