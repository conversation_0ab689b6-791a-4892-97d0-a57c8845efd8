class EventCardModel {
  String title;
  final String memberName;
  final String beautician;
  final String date;
  final String price;
  final String? discount;
  final String? status;
  final String? phoneNumber;
  final String? orderNumber;
  final String? startTime;
  final String? endTime;

  EventCardModel({
    required this.title,
    required this.memberName,
    required this.beautician,
    required this.date,
    required this.price,
    this.startTime,
    this.endTime,

    this.discount,
    this.status,
    this.phoneNumber,
    this.orderNumber,
  });
}
