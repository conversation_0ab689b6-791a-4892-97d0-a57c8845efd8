class TimetableControllerModel {
  final DateTime start;
  final int initialColumns;
  final int startHour;
  final int endHour;
  final double cellHeight;
  final double headerHeight;
  final double timelineWidth;
  final double? cellWidth;
  final int visibleHourCount;

  TimetableControllerModel({
    required this.start,
    required this.initialColumns,
    required this.startHour,
    required this.endHour,
    required this.cellHeight,
    required this.headerHeight,
    required this.timelineWidth,
    this.cellWidth,
    required this.visibleHourCount,
  });
}