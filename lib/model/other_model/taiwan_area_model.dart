class TaiwanArea {
  List<Districts>? districts;
  String? name;

  TaiwanArea({this.districts, this.name});

  TaiwanArea.fromJson(Map<String, dynamic> json) {
    if (json['districts'] != null) {
      districts = <Districts>[];
      json['districts'].forEach((v) {
        districts!.add(new Districts.fromJson(v));
      });
    }
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.districts != null) {
      data['districts'] = this.districts!.map((v) => v.toJson()).toList();
    }
    data['name'] = this.name;
    return data;
  }
}

class Districts {
  String? zip;
  String? name;

  Districts({this.zip, this.name});

  Districts.fromJson(Map<String, dynamic> json) {
    zip = json['zip'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['zip'] = this.zip;
    data['name'] = this.name;
    return data;
  }
}
