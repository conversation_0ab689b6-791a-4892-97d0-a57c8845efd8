import 'package:flutter/cupertino.dart';

/// Base dialog選單class
/// [title]用於顯示選項，
/// [object]用於置放對應型別的資料
/// [pos]用於紀錄位置
/// [isCheck]多選項使用
class BaseDialogListModel {
  String title;
  Object? object;
  int pos;
  bool isCheck;

  BaseDialogListModel(this.pos,  this.title, this.object,
      {this.isCheck = false,});

  @override
  String toString() {
    return "pos: $pos, title: $title, object: ${object.toString()}, isCheck: $isCheck";
  }
}
