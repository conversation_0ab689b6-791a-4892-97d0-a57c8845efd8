class PerformanceInfoModel{
  final String skuCode;  //SKU編碼
  final String productName; //商品、課程、儲值金名稱
  final int purchaseQuantity; //購買數量
  final double originalPrice; //原售價
  final double actualTotalSalesPrice; //實售小計
  final double actualSalesPrice; //實售價
   List<PerformanceMemberModel> performanceMemberModel;


  PerformanceInfoModel({
    required this.skuCode,
    required this.productName,
    required this.originalPrice,
    required this.purchaseQuantity,
    required this.actualTotalSalesPrice,
    required this.actualSalesPrice,
    required this.performanceMemberModel,

  });
}

class PerformanceMemberModel{
  final String staffName; //員工姓名
  final String staffId; //員工編號
  final double percent; //分配比例
  final double percentPrice; //分配金額

  const PerformanceMemberModel({
    required this.staffId,
    required this.staffName,
    required this.percent,
    required this.percentPrice,

  });


}
extension PerformanceMemberModelCopy on PerformanceMemberModel {
  PerformanceMemberModel copy() {
    return PerformanceMemberModel(
      staffId: staffId,
      staffName: staffName,
      percent: percent,
      percentPrice: percentPrice,
    );
  }
}
